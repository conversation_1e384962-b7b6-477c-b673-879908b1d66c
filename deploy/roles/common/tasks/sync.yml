---
- name: "停止{{ srv_descrip }}"
  ansible.builtin.shell: "{{ deploy_dir }}/shell/shutdown.sh"
- name: "设置时间变量"
  ansible.builtin.set_fact:
    ctime: "{{ lookup('pipe', 'date +%Y%m%d-%H%M') }}"
- name: "备份当前应用程序"
  community.general.archive:
    path:
    - "{{ deploy_dir }}/{{ srv_name }}"
    - "{{ deploy_dir }}/{{ config_name }}"
    dest: "{{ deploy_dir }}/backup/{{ srv_name }}_{{ ctime }}.gz"
    format: gz
- name: "上传{{ config_name }}"
  ansible.posix.synchronize: 
    src: "{{ workspace }}/{{ config_name }}"
    dest: "{{ deploy_dir }}/configs"
    owner: false
    group: false
- name: "上传{{ srv_name }}"
  ansible.posix.synchronize: 
    src: "{{ workspace }}/{{ srv_name }}"
    dest: "{{ deploy_dir }}"
    owner: false
    group: false
- name: "启动{{ srv_descrip }}"
  ansible.builtin.shell: "{{ deploy_dir }}/shell/startup.sh"
- name: "暂停5秒"
  ansible.builtin.shell: sleep 5s
- name: "检测是否启动成功"
  ansible.builtin.shell: "echo `ps -aux | grep {{ srv_name }} | grep -v grep | grep -v '/bin/sh'|wc -l`"
  ignore_errors: yes
  register: is_success
- name: "获取当前备份文件名"
  ansible.builtin.shell: "echo `ls -1t {{ deploy_dir }}/backup | head -n 1`"
  register: rollback_current_file_name
  when: is_success.stdout == "0"
- name: "回滚应用程序"
  ansible.builtin.unarchive:
    remote_src: yes
    src: "{{ deploy_dir }}/backup/{{ rollback_current_file_name.stdout }}"
    dest: "{{ deploy_dir }}"
  when: is_success.stdout == "0"
- name: "启动{{ srv_descrip }}"
  ansible.builtin.shell: "{{ deploy_dir }}/shell/startup.sh"
  when: is_success.stdout == "0"
- name: "删除备份文件"
  ansible.builtin.file:
    path: "{{ deploy_dir }}/backup/{{ srv_name }}_{{ ctime }}.gz"
    state: absent
- name: "回滚通知"
  ansible.builtin.fail:
    msg: "部署失败，程序回滚！"
  when: is_success.stdout == "0"