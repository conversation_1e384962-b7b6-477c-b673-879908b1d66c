---
- name: "创建项目根目录: {{ deploy_dir }}"
  ansible.builtin.file:
    state: directory
    path: "{{ deploy_dir }}"
- name: "创建配置文件存放目录"
  ansible.builtin.file:
    state: directory
    path: "{{ deploy_dir }}/configs"
- name: "创建备份目录"
  ansible.builtin.file:
    state: directory
    path: "{{ deploy_dir }}/backup"
- name: "创建日志文件存放目录"
  ansible.builtin.file:
    state: directory
    path: "{{ deploy_dir }}/logs"
- name: "创建脚本存放目录"
  ansible.builtin.file:
    state: directory
    path: "{{ deploy_dir }}/shell"
- name: "复制启动脚本"
  ansible.builtin.template:
    src: ../templates/startup.sh.j2
    dest: "{{ deploy_dir }}/shell/startup.sh"
    mode: 0755
- name: "复制停止脚本"
  ansible.builtin.template:
    src: ../templates/shutdown.sh.j2
    dest: "{{ deploy_dir }}/shell/shutdown.sh"
    mode: 0755
- name: "复制服务检查脚本"
  ansible.builtin.template:
    src: ../templates/check.sh.j2
    dest: "{{ deploy_dir }}/shell/check.sh"
    mode: 0755
- name: "复制日志清理脚本"
  ansible.builtin.template:
    src: ../templates/clean.sh.j2
    dest: "{{ deploy_dir }}/shell/clean.sh"
    mode: 0755
- name: "设置定时清理日志任务"
  ansible.builtin.cron:
    name: "clean {{ srv_name }} log"
    hour: "*/24"
    job: "{{ deploy_dir }}/shell/clean.sh >/dev/null 2>&1"
- name: "设置服务自动拉起任务"
  ansible.builtin.cron:
    name: "check {{ srv_name }} task"
    minute: "*/3"
    job: "{{ deploy_dir }}/shell/check.sh >/dev/null 2>&1"