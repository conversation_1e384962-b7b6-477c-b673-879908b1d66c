#!/bin/bash
NAME="{{ srv_name }}"
DESCRIP="{{ srv_descrip }}"

echo "正在停止$DESCRIP..."
pid=$(ps -ef | grep ./$NAME | grep -v grep | awk '{print $2}')
while [ -n "$pid" ]; do
kill -15 $pid
pid=$(ps -ef | grep ./$NAME | grep -v grep | awk '{print $2}')
done
echo "$DESCRIP停止成功！"

echo "正在启动$DESCRIP..."
cd "{{ deploy_dir }}"
env GOTRACEBACK=crash nohup ./$NAME -conf ./configs >> logs/console.log 2>&1 &
echo "$DESCRIP启动成功！"