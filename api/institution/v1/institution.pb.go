// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.17.3
// source: institution/v1/institution.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateInstInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校简称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 学校名字
	FullName string `protobuf:"bytes,3,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	// 类型  1-公办幼儿园、2-民办幼儿园（普惠）、3-民办幼儿园、4-托育中心
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	// 幼儿园地址省份code
	ProvinceCode string `protobuf:"bytes,5,opt,name=province_code,json=provinceCode,proto3" json:"province_code,omitempty"`
	// 幼儿园地址城市code
	CityCode string `protobuf:"bytes,6,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	// 幼儿园地址区县code
	DistrictCode string `protobuf:"bytes,7,opt,name=district_code,json=districtCode,proto3" json:"district_code,omitempty"`
	// 详细地址
	Address string `protobuf:"bytes,8,opt,name=address,proto3" json:"address,omitempty"`
	// 园长姓名
	AdminName string `protobuf:"bytes,9,opt,name=admin_name,json=adminName,proto3" json:"admin_name,omitempty"`
	// 园长手机号
	AdminMobile string `protobuf:"bytes,10,opt,name=admin_mobile,json=adminMobile,proto3" json:"admin_mobile,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,11,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,12,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,13,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,14,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateInstInfoReq) Reset() {
	*x = UpdateInstInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateInstInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInstInfoReq) ProtoMessage() {}

func (x *UpdateInstInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInstInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateInstInfoReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateInstInfoReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateInstInfoReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateInstInfoReq) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *UpdateInstInfoReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *UpdateInstInfoReq) GetProvinceCode() string {
	if x != nil {
		return x.ProvinceCode
	}
	return ""
}

func (x *UpdateInstInfoReq) GetCityCode() string {
	if x != nil {
		return x.CityCode
	}
	return ""
}

func (x *UpdateInstInfoReq) GetDistrictCode() string {
	if x != nil {
		return x.DistrictCode
	}
	return ""
}

func (x *UpdateInstInfoReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UpdateInstInfoReq) GetAdminName() string {
	if x != nil {
		return x.AdminName
	}
	return ""
}

func (x *UpdateInstInfoReq) GetAdminMobile() string {
	if x != nil {
		return x.AdminMobile
	}
	return ""
}

func (x *UpdateInstInfoReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateInstInfoReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateInstInfoReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateInstInfoReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type GetInstFullInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetInstFullInfoReq) Reset() {
	*x = GetInstFullInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstFullInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstFullInfoReq) ProtoMessage() {}

func (x *GetInstFullInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstFullInfoReq.ProtoReflect.Descriptor instead.
func (*GetInstFullInfoReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{1}
}

func (x *GetInstFullInfoReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetInstFullInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 园所id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 全称
	FullName string `protobuf:"bytes,3,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	// 类型  1-公办幼儿园、2-民办幼儿园（普惠）、3-民办幼儿园、4-托育中心
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	// 幼儿园地址省份code
	ProvinceCode string `protobuf:"bytes,5,opt,name=province_code,json=provinceCode,proto3" json:"province_code,omitempty"`
	// 幼儿园地址城市code
	CityCode string `protobuf:"bytes,6,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	// 幼儿园地址区县code
	DistrictCode string `protobuf:"bytes,7,opt,name=district_code,json=districtCode,proto3" json:"district_code,omitempty"`
	// 详细地址
	Address string `protobuf:"bytes,8,opt,name=address,proto3" json:"address,omitempty"`
	// 园长姓名
	AdminName string `protobuf:"bytes,9,opt,name=admin_name,json=adminName,proto3" json:"admin_name,omitempty"`
	// 园长手机号
	AdminMobile string `protobuf:"bytes,10,opt,name=admin_mobile,json=adminMobile,proto3" json:"admin_mobile,omitempty"`
}

func (x *GetInstFullInfoRsp) Reset() {
	*x = GetInstFullInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstFullInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstFullInfoRsp) ProtoMessage() {}

func (x *GetInstFullInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstFullInfoRsp.ProtoReflect.Descriptor instead.
func (*GetInstFullInfoRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{2}
}

func (x *GetInstFullInfoRsp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetInstFullInfoRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetInstFullInfoRsp) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *GetInstFullInfoRsp) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GetInstFullInfoRsp) GetProvinceCode() string {
	if x != nil {
		return x.ProvinceCode
	}
	return ""
}

func (x *GetInstFullInfoRsp) GetCityCode() string {
	if x != nil {
		return x.CityCode
	}
	return ""
}

func (x *GetInstFullInfoRsp) GetDistrictCode() string {
	if x != nil {
		return x.DistrictCode
	}
	return ""
}

func (x *GetInstFullInfoRsp) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *GetInstFullInfoRsp) GetAdminName() string {
	if x != nil {
		return x.AdminName
	}
	return ""
}

func (x *GetInstFullInfoRsp) GetAdminMobile() string {
	if x != nil {
		return x.AdminMobile
	}
	return ""
}

type UpdateFirstCardUpdateFlagReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 门禁首卡更新标识 1-更新 0-未更新
	UpdateFlag uint32 `protobuf:"varint,2,opt,name=update_flag,json=updateFlag,proto3" json:"update_flag,omitempty"`
}

func (x *UpdateFirstCardUpdateFlagReq) Reset() {
	*x = UpdateFirstCardUpdateFlagReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFirstCardUpdateFlagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFirstCardUpdateFlagReq) ProtoMessage() {}

func (x *UpdateFirstCardUpdateFlagReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFirstCardUpdateFlagReq.ProtoReflect.Descriptor instead.
func (*UpdateFirstCardUpdateFlagReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateFirstCardUpdateFlagReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateFirstCardUpdateFlagReq) GetUpdateFlag() uint32 {
	if x != nil {
		return x.UpdateFlag
	}
	return 0
}

type UpdateAccessSetUpdateFlagReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 出入园设置更新标识 1-更新 0-未更新
	UpdateFlag uint32 `protobuf:"varint,2,opt,name=update_flag,json=updateFlag,proto3" json:"update_flag,omitempty"`
}

func (x *UpdateAccessSetUpdateFlagReq) Reset() {
	*x = UpdateAccessSetUpdateFlagReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccessSetUpdateFlagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccessSetUpdateFlagReq) ProtoMessage() {}

func (x *UpdateAccessSetUpdateFlagReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccessSetUpdateFlagReq.ProtoReflect.Descriptor instead.
func (*UpdateAccessSetUpdateFlagReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAccessSetUpdateFlagReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateAccessSetUpdateFlagReq) GetUpdateFlag() uint32 {
	if x != nil {
		return x.UpdateFlag
	}
	return 0
}

// 更新学校系统参数请求参数
type UpdateInstParamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 参数值列表
	Params map[uint32]string `protobuf:"bytes,2,rep,name=params,proto3" json:"params,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateInstParamReq) Reset() {
	*x = UpdateInstParamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateInstParamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInstParamReq) ProtoMessage() {}

func (x *UpdateInstParamReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInstParamReq.ProtoReflect.Descriptor instead.
func (*UpdateInstParamReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateInstParamReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateInstParamReq) GetParams() map[uint32]string {
	if x != nil {
		return x.Params
	}
	return nil
}

// 更新学校单个系统参数请求参数
type UpdateInstParamByCodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 参数编号
	Code uint32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	// 参数值
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *UpdateInstParamByCodeReq) Reset() {
	*x = UpdateInstParamByCodeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateInstParamByCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInstParamByCodeReq) ProtoMessage() {}

func (x *UpdateInstParamByCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInstParamByCodeReq.ProtoReflect.Descriptor instead.
func (*UpdateInstParamByCodeReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateInstParamByCodeReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateInstParamByCodeReq) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateInstParamByCodeReq) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 获取学校系统参数列表请求参数
type ListInstParamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *ListInstParamReq) Reset() {
	*x = ListInstParamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInstParamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInstParamReq) ProtoMessage() {}

func (x *ListInstParamReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInstParamReq.ProtoReflect.Descriptor instead.
func (*ListInstParamReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{7}
}

func (x *ListInstParamReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 获取学校系统参数列表返回参数
type ListInstParamRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校系统参数列表
	List []*InstParamInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListInstParamRsp) Reset() {
	*x = ListInstParamRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInstParamRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInstParamRsp) ProtoMessage() {}

func (x *ListInstParamRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInstParamRsp.ProtoReflect.Descriptor instead.
func (*ListInstParamRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{8}
}

func (x *ListInstParamRsp) GetList() []*InstParamInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type InstParamInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 参数编号
	Code uint32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	// 参数值
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *InstParamInfo) Reset() {
	*x = InstParamInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstParamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstParamInfo) ProtoMessage() {}

func (x *InstParamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstParamInfo.ProtoReflect.Descriptor instead.
func (*InstParamInfo) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{9}
}

func (x *InstParamInfo) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *InstParamInfo) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *InstParamInfo) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 获取学校系统参数请求参数
type GetInstParamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 参数编号
	Code uint32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *GetInstParamReq) Reset() {
	*x = GetInstParamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstParamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstParamReq) ProtoMessage() {}

func (x *GetInstParamReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstParamReq.ProtoReflect.Descriptor instead.
func (*GetInstParamReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{10}
}

func (x *GetInstParamReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetInstParamReq) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

// 获取地区列表
type GetAreasReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 上级code  顶级传0
	ParentCode string `protobuf:"bytes,1,opt,name=parent_code,json=parentCode,proto3" json:"parent_code,omitempty"`
}

func (x *GetAreasReq) Reset() {
	*x = GetAreasReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAreasReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAreasReq) ProtoMessage() {}

func (x *GetAreasReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAreasReq.ProtoReflect.Descriptor instead.
func (*GetAreasReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{11}
}

func (x *GetAreasReq) GetParentCode() string {
	if x != nil {
		return x.ParentCode
	}
	return ""
}

type GetAreasRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*AreaInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetAreasRsp) Reset() {
	*x = GetAreasRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAreasRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAreasRsp) ProtoMessage() {}

func (x *GetAreasRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAreasRsp.ProtoReflect.Descriptor instead.
func (*GetAreasRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{12}
}

func (x *GetAreasRsp) GetData() []*AreaInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type AreaInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地区名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 地区code
	AreaCode string `protobuf:"bytes,2,opt,name=area_code,json=areaCode,proto3" json:"area_code,omitempty"`
	// 父级code
	ParentCode string `protobuf:"bytes,3,opt,name=parent_code,json=parentCode,proto3" json:"parent_code,omitempty"`
	// 地区简称
	ShortName string `protobuf:"bytes,4,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty"`
	// 层级
	Level int32 `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *AreaInfo) Reset() {
	*x = AreaInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaInfo) ProtoMessage() {}

func (x *AreaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaInfo.ProtoReflect.Descriptor instead.
func (*AreaInfo) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{13}
}

func (x *AreaInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AreaInfo) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *AreaInfo) GetParentCode() string {
	if x != nil {
		return x.ParentCode
	}
	return ""
}

func (x *AreaInfo) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *AreaInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// 机构是否存在请求参数
type IsExistInstReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *IsExistInstReq) Reset() {
	*x = IsExistInstReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsExistInstReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsExistInstReq) ProtoMessage() {}

func (x *IsExistInstReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsExistInstReq.ProtoReflect.Descriptor instead.
func (*IsExistInstReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{14}
}

func (x *IsExistInstReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 机构是否存在返回参数
type IsExistInstRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构是否存在（1：存在，0：不存在）
	IsExist int32 `protobuf:"varint,1,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`
}

func (x *IsExistInstRsp) Reset() {
	*x = IsExistInstRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsExistInstRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsExistInstRsp) ProtoMessage() {}

func (x *IsExistInstRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsExistInstRsp.ProtoReflect.Descriptor instead.
func (*IsExistInstRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{15}
}

func (x *IsExistInstRsp) GetIsExist() int32 {
	if x != nil {
		return x.IsExist
	}
	return 0
}

// 获取手机号关联的机构列表
type ListAccInstLinkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 手机号码
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
}

func (x *ListAccInstLinkReq) Reset() {
	*x = ListAccInstLinkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccInstLinkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccInstLinkReq) ProtoMessage() {}

func (x *ListAccInstLinkReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccInstLinkReq.ProtoReflect.Descriptor instead.
func (*ListAccInstLinkReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{16}
}

func (x *ListAccInstLinkReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

type ListAccInstLinkRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 手机号绑定的机构列表
	Data []*BriefInstInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ListAccInstLinkRsp) Reset() {
	*x = ListAccInstLinkRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccInstLinkRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccInstLinkRsp) ProtoMessage() {}

func (x *ListAccInstLinkRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccInstLinkRsp.ProtoReflect.Descriptor instead.
func (*ListAccInstLinkRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{17}
}

func (x *ListAccInstLinkRsp) GetData() []*BriefInstInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 机构简要信息
type BriefInstInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构ID
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 机构名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 全称
	FullName string `protobuf:"bytes,3,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	// 机构图像
	Logo string `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`
	// 是否最近登录（1：是，0：否）
	IsLastLogin int32 `protobuf:"varint,5,opt,name=is_last_login,json=isLastLogin,proto3" json:"is_last_login,omitempty"`
	// 创建时间
	CreateTime string `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *BriefInstInfo) Reset() {
	*x = BriefInstInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BriefInstInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BriefInstInfo) ProtoMessage() {}

func (x *BriefInstInfo) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BriefInstInfo.ProtoReflect.Descriptor instead.
func (*BriefInstInfo) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{18}
}

func (x *BriefInstInfo) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *BriefInstInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BriefInstInfo) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *BriefInstInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *BriefInstInfo) GetIsLastLogin() int32 {
	if x != nil {
		return x.IsLastLogin
	}
	return 0
}

func (x *BriefInstInfo) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

// 根据关键词获取企业列表
type GetEnterpriseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 关键词
	KeyWords string `protobuf:"bytes,1,opt,name=key_words,json=keyWords,proto3" json:"key_words,omitempty"`
}

func (x *GetEnterpriseReq) Reset() {
	*x = GetEnterpriseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseReq) ProtoMessage() {}

func (x *GetEnterpriseReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseReq.ProtoReflect.Descriptor instead.
func (*GetEnterpriseReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{19}
}

func (x *GetEnterpriseReq) GetKeyWords() string {
	if x != nil {
		return x.KeyWords
	}
	return ""
}

type GetEnterpriseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 企业列表
	Data []*EnterpriseInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetEnterpriseRsp) Reset() {
	*x = GetEnterpriseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseRsp) ProtoMessage() {}

func (x *GetEnterpriseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseRsp.ProtoReflect.Descriptor instead.
func (*GetEnterpriseRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{20}
}

func (x *GetEnterpriseRsp) GetData() []*EnterpriseInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type EnterpriseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 企业名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *EnterpriseInfo) Reset() {
	*x = EnterpriseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterpriseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterpriseInfo) ProtoMessage() {}

func (x *EnterpriseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterpriseInfo.ProtoReflect.Descriptor instead.
func (*EnterpriseInfo) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{21}
}

func (x *EnterpriseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 创建机构
type CreateInstReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 名称
	InstName string `protobuf:"bytes,1,opt,name=inst_name,json=instName,proto3" json:"inst_name,omitempty"`
	// 类型 1-公办幼儿园、2-民办幼儿园（普惠）、3-民办幼儿园、4-托育中心
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// 幼儿园地址省份code
	ProvinceCode string `protobuf:"bytes,3,opt,name=province_code,json=provinceCode,proto3" json:"province_code,omitempty"`
	// 幼儿园地址城市code
	CityCode string `protobuf:"bytes,4,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	// 幼儿园地址区县code
	DistrictCode string `protobuf:"bytes,5,opt,name=district_code,json=districtCode,proto3" json:"district_code,omitempty"`
	// 详细地址
	Address string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	// 园长姓名
	AdminName string `protobuf:"bytes,7,opt,name=admin_name,json=adminName,proto3" json:"admin_name,omitempty"`
	// 园长手机号
	AdminMobile string `protobuf:"bytes,8,opt,name=admin_mobile,json=adminMobile,proto3" json:"admin_mobile,omitempty"`
	// 短信验证码
	SmsCode int32 `protobuf:"varint,9,opt,name=sms_code,json=smsCode,proto3" json:"sms_code,omitempty"`
}

func (x *CreateInstReq) Reset() {
	*x = CreateInstReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInstReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInstReq) ProtoMessage() {}

func (x *CreateInstReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInstReq.ProtoReflect.Descriptor instead.
func (*CreateInstReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{22}
}

func (x *CreateInstReq) GetInstName() string {
	if x != nil {
		return x.InstName
	}
	return ""
}

func (x *CreateInstReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CreateInstReq) GetProvinceCode() string {
	if x != nil {
		return x.ProvinceCode
	}
	return ""
}

func (x *CreateInstReq) GetCityCode() string {
	if x != nil {
		return x.CityCode
	}
	return ""
}

func (x *CreateInstReq) GetDistrictCode() string {
	if x != nil {
		return x.DistrictCode
	}
	return ""
}

func (x *CreateInstReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *CreateInstReq) GetAdminName() string {
	if x != nil {
		return x.AdminName
	}
	return ""
}

func (x *CreateInstReq) GetAdminMobile() string {
	if x != nil {
		return x.AdminMobile
	}
	return ""
}

func (x *CreateInstReq) GetSmsCode() int32 {
	if x != nil {
		return x.SmsCode
	}
	return 0
}

type CreateInstRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 账号
	Mobile string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 默认密码
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *CreateInstRsp) Reset() {
	*x = CreateInstRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInstRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInstRsp) ProtoMessage() {}

func (x *CreateInstRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInstRsp.ProtoReflect.Descriptor instead.
func (*CreateInstRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{23}
}

func (x *CreateInstRsp) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *CreateInstRsp) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// 获取APP启动页图片
type GetLaunchScreenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *GetLaunchScreenReq) Reset() {
	*x = GetLaunchScreenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLaunchScreenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLaunchScreenReq) ProtoMessage() {}

func (x *GetLaunchScreenReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLaunchScreenReq.ProtoReflect.Descriptor instead.
func (*GetLaunchScreenReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{24}
}

func (x *GetLaunchScreenReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

type GetLaunchScreenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 启动图片链接地址
	PicUrl string `protobuf:"bytes,1,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
}

func (x *GetLaunchScreenRsp) Reset() {
	*x = GetLaunchScreenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLaunchScreenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLaunchScreenRsp) ProtoMessage() {}

func (x *GetLaunchScreenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLaunchScreenRsp.ProtoReflect.Descriptor instead.
func (*GetLaunchScreenRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{25}
}

func (x *GetLaunchScreenRsp) GetPicUrl() string {
	if x != nil {
		return x.PicUrl
	}
	return ""
}

// ------->>>>>>>------获取用户信息------<<<<<<<GetUserInfo>>>>>>---MaWei@2023-06-05 14:17----<<<<<<----//
type GetUserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 提取类型 （1.班级 2.学生 3.老师 4.全园 5.混合）
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// 是否只返回id
	IsInfo int32 `protobuf:"varint,3,opt,name=is_info,json=isInfo,proto3" json:"is_info,omitempty"`
	// 是否返回父母
	IsParent int32 `protobuf:"varint,4,opt,name=is_parent,json=isParent,proto3" json:"is_parent,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 员工ids
	StaffIds []int64 `protobuf:"varint,6,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// 学生ids
	StudentIds []int64 `protobuf:"varint,7,rep,packed,name=student_ids,json=studentIds,proto3" json:"student_ids,omitempty"`
	// 班级id
	ClassIds []int64 `protobuf:"varint,8,rep,packed,name=class_ids,json=classIds,proto3" json:"class_ids,omitempty"`
}

func (x *GetUserInfoReq) Reset() {
	*x = GetUserInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoReq) ProtoMessage() {}

func (x *GetUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoReq.ProtoReflect.Descriptor instead.
func (*GetUserInfoReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{26}
}

func (x *GetUserInfoReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetUserInfoReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GetUserInfoReq) GetIsInfo() int32 {
	if x != nil {
		return x.IsInfo
	}
	return 0
}

func (x *GetUserInfoReq) GetIsParent() int32 {
	if x != nil {
		return x.IsParent
	}
	return 0
}

func (x *GetUserInfoReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserInfoReq) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *GetUserInfoReq) GetStudentIds() []int64 {
	if x != nil {
		return x.StudentIds
	}
	return nil
}

func (x *GetUserInfoReq) GetClassIds() []int64 {
	if x != nil {
		return x.ClassIds
	}
	return nil
}

type GetUserInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 员工信息
	StaffInfo map[int64]*UserInfo `protobuf:"bytes,1,rep,name=staff_info,json=staffInfo,proto3" json:"staff_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 学生信息
	StudentInfo map[int64]*UserInfo `protobuf:"bytes,2,rep,name=student_info,json=studentInfo,proto3" json:"student_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 班级信息
	ClassInfo map[int64]*ClassInfo `protobuf:"bytes,3,rep,name=class_info,json=classInfo,proto3" json:"class_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetUserInfoRsp) Reset() {
	*x = GetUserInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoRsp) ProtoMessage() {}

func (x *GetUserInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoRsp.ProtoReflect.Descriptor instead.
func (*GetUserInfoRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{27}
}

func (x *GetUserInfoRsp) GetStaffInfo() map[int64]*UserInfo {
	if x != nil {
		return x.StaffInfo
	}
	return nil
}

func (x *GetUserInfoRsp) GetStudentInfo() map[int64]*UserInfo {
	if x != nil {
		return x.StudentInfo
	}
	return nil
}

func (x *GetUserInfoRsp) GetClassInfo() map[int64]*ClassInfo {
	if x != nil {
		return x.ClassInfo
	}
	return nil
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户姓名
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	// 用户头像
	UserAvatar string `protobuf:"bytes,3,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar,omitempty"`
	// 用户班级
	ClassName string `protobuf:"bytes,4,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 用户班级id
	ClassId int64 `protobuf:"varint,5,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 家长id
	ParentId int64 `protobuf:"varint,6,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 家长姓名
	ParentName string `protobuf:"bytes,7,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"`
	// 家长电话
	Mobile string `protobuf:"bytes,8,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 家长头像
	ParentAvatar string `protobuf:"bytes,9,opt,name=parent_avatar,json=parentAvatar,proto3" json:"parent_avatar,omitempty"`
	// 关系
	Relation int32 `protobuf:"varint,10,opt,name=relation,proto3" json:"relation,omitempty"`
	// 称谓
	Alias string `protobuf:"bytes,11,opt,name=alias,proto3" json:"alias,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{28}
}

func (x *UserInfo) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UserInfo) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *UserInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *UserInfo) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *UserInfo) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *UserInfo) GetParentName() string {
	if x != nil {
		return x.ParentName
	}
	return ""
}

func (x *UserInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UserInfo) GetParentAvatar() string {
	if x != nil {
		return x.ParentAvatar
	}
	return ""
}

func (x *UserInfo) GetRelation() int32 {
	if x != nil {
		return x.Relation
	}
	return 0
}

func (x *UserInfo) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

// 班级信息
type ClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,2,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
}

func (x *ClassInfo) Reset() {
	*x = ClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassInfo) ProtoMessage() {}

func (x *ClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassInfo.ProtoReflect.Descriptor instead.
func (*ClassInfo) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{29}
}

func (x *ClassInfo) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *ClassInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

type InstInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *InstInfoReq) Reset() {
	*x = InstInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstInfoReq) ProtoMessage() {}

func (x *InstInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstInfoReq.ProtoReflect.Descriptor instead.
func (*InstInfoReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{30}
}

func (x *InstInfoReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

type InstInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 名称全名
	FullName string `protobuf:"bytes,3,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
}

func (x *InstInfoRsp) Reset() {
	*x = InstInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstInfoRsp) ProtoMessage() {}

func (x *InstInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstInfoRsp.ProtoReflect.Descriptor instead.
func (*InstInfoRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{31}
}

func (x *InstInfoRsp) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *InstInfoRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InstInfoRsp) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

type InstAppSettingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 配置id
	SettingId int64 `protobuf:"varint,3,opt,name=setting_id,json=settingId,proto3" json:"setting_id,omitempty"`
	// 参数code
	Code int32 `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *InstAppSettingReq) Reset() {
	*x = InstAppSettingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstAppSettingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstAppSettingReq) ProtoMessage() {}

func (x *InstAppSettingReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstAppSettingReq.ProtoReflect.Descriptor instead.
func (*InstAppSettingReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{32}
}

func (x *InstAppSettingReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *InstAppSettingReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *InstAppSettingReq) GetSettingId() int64 {
	if x != nil {
		return x.SettingId
	}
	return 0
}

func (x *InstAppSettingReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type InstAppSettingRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据
	Data []*InstAppSetting `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *InstAppSettingRsp) Reset() {
	*x = InstAppSettingRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstAppSettingRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstAppSettingRsp) ProtoMessage() {}

func (x *InstAppSettingRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstAppSettingRsp.ProtoReflect.Descriptor instead.
func (*InstAppSettingRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{33}
}

func (x *InstAppSettingRsp) GetData() []*InstAppSetting {
	if x != nil {
		return x.Data
	}
	return nil
}

type InstAppSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// code
	Code int32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	// 值
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	// 名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *InstAppSetting) Reset() {
	*x = InstAppSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstAppSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstAppSetting) ProtoMessage() {}

func (x *InstAppSetting) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstAppSetting.ProtoReflect.Descriptor instead.
func (*InstAppSetting) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{34}
}

func (x *InstAppSetting) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InstAppSetting) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *InstAppSetting) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *InstAppSetting) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UpdateInstAppSettingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 参数code
	Code int64 `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
	// 值
	Value string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *UpdateInstAppSettingReq) Reset() {
	*x = UpdateInstAppSettingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateInstAppSettingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInstAppSettingReq) ProtoMessage() {}

func (x *UpdateInstAppSettingReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInstAppSettingReq.ProtoReflect.Descriptor instead.
func (*UpdateInstAppSettingReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{35}
}

func (x *UpdateInstAppSettingReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateInstAppSettingReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateInstAppSettingReq) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateInstAppSettingReq) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type UserRelationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户类型
	UserRole int32 `protobuf:"varint,3,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,4,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
}

func (x *UserRelationReq) Reset() {
	*x = UserRelationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRelationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRelationReq) ProtoMessage() {}

func (x *UserRelationReq) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRelationReq.ProtoReflect.Descriptor instead.
func (*UserRelationReq) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{36}
}

func (x *UserRelationReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UserRelationReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserRelationReq) GetUserRole() int32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *UserRelationReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

type UserRelationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 返回数据
	Data *UserRelation `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *UserRelationRsp) Reset() {
	*x = UserRelationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRelationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRelationRsp) ProtoMessage() {}

func (x *UserRelationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRelationRsp.ProtoReflect.Descriptor instead.
func (*UserRelationRsp) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{37}
}

func (x *UserRelationRsp) GetData() *UserRelation {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserRelation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 关联的班次
	RelationClass []*RelationClass `protobuf:"bytes,14,rep,name=relationClass,proto3" json:"relationClass,omitempty"`
	// 关联的老师和园长
	RelationTeacher []*RelationTeacher `protobuf:"bytes,15,rep,name=relationTeacher,proto3" json:"relationTeacher,omitempty"`
}

func (x *UserRelation) Reset() {
	*x = UserRelation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRelation) ProtoMessage() {}

func (x *UserRelation) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRelation.ProtoReflect.Descriptor instead.
func (*UserRelation) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{38}
}

func (x *UserRelation) GetRelationClass() []*RelationClass {
	if x != nil {
		return x.RelationClass
	}
	return nil
}

func (x *UserRelation) GetRelationTeacher() []*RelationTeacher {
	if x != nil {
		return x.RelationTeacher
	}
	return nil
}

type RelationClass struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班次id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 班次名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *RelationClass) Reset() {
	*x = RelationClass{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationClass) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationClass) ProtoMessage() {}

func (x *RelationClass) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationClass.ProtoReflect.Descriptor instead.
func (*RelationClass) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{39}
}

func (x *RelationClass) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RelationClass) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type RelationTeacher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班次id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 班次名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 角色
	RoleId int32 `protobuf:"varint,4,opt,name=roleId,proto3" json:"roleId,omitempty"`
}

func (x *RelationTeacher) Reset() {
	*x = RelationTeacher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_institution_v1_institution_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationTeacher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationTeacher) ProtoMessage() {}

func (x *RelationTeacher) ProtoReflect() protoreflect.Message {
	mi := &file_institution_v1_institution_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationTeacher.ProtoReflect.Descriptor instead.
func (*RelationTeacher) Descriptor() ([]byte, []int) {
	return file_institution_v1_institution_proto_rawDescGZIP(), []int{40}
}

func (x *RelationTeacher) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RelationTeacher) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RelationTeacher) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *RelationTeacher) GetRoleId() int32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

var File_institution_v1_institution_proto protoreflect.FileDescriptor

var file_institution_v1_institution_proto_rawDesc = []byte{
	0x0a, 0x20, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x12, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa9, 0x04, 0x0a,
	0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x0f, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x09, 0x66, 0x75,
	0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x1a, 0x08, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x30, 0x04, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x63, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x0d, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x0c, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x25,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x2d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0xac, 0x02, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x22, 0x6c, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x43, 0x61, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46,
	0x6c, 0x61, 0x67, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x2a, 0x04, 0x30, 0x00, 0x30, 0x01, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x46, 0x6c, 0x61, 0x67, 0x22, 0x6c, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6c, 0x61,
	0x67, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x2a, 0x04, 0x30, 0x00, 0x30, 0x01, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6c,
	0x61, 0x67, 0x22, 0xc7, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x52, 0x65, 0x71, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x9a, 0x01, 0x02, 0x08, 0x01, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x78, 0x0a, 0x18,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x42,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20,
	0x00, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x34, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0x49, 0x0a, 0x10,
	0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x73, 0x70,
	0x12, 0x35, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x52, 0x0a, 0x0d, 0x49, 0x6e, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x50, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x20,
	0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20, 0x00, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x37, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x41, 0x72, 0x65, 0x61, 0x73, 0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x0b,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x3f, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x41, 0x72, 0x65,
	0x61, 0x73, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x91, 0x01, 0x0a, 0x08, 0x41, 0x72, 0x65, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x72, 0x65, 0x61,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65,
	0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x2d, 0x0a, 0x0e, 0x49,
	0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2b, 0x0a, 0x0e, 0x49, 0x73,
	0x45, 0x78, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0x35, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x63, 0x63, 0x49, 0x6e, 0x73, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a,
	0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x22, 0x4b,
	0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x49, 0x6e, 0x73, 0x74, 0x4c, 0x69, 0x6e,
	0x6b, 0x52, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x73,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb2, 0x01, 0x0a, 0x0d,
	0x42, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75,
	0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x69,
	0x73, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x69, 0x73, 0x4c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x38, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x6b, 0x65, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x08, 0x6b, 0x65, 0x79, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x4a, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x36,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x24, 0x0a, 0x0e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xf5, 0x02, 0x0a,
	0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24,
	0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x1a, 0x08, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x30,
	0x04, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x08, 0x63, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x0d, 0x64,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x26, 0x0a, 0x0a,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x0c, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x12, 0x22, 0x0a, 0x08, 0x73, 0x6d, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x6d, 0x73,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0x43, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x36, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x22, 0x2d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x69, 0x63, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x69, 0x63, 0x55, 0x72, 0x6c,
	0x22, 0xe7, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x69, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73,
	0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0xa3, 0x04, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x73, 0x70, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x56, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74,
	0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x2e,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x5a, 0x0a, 0x0e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5c, 0x0a, 0x10, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x5b, 0x0a, 0x0e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73,
	0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xc8, 0x02, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x22, 0x45, 0x0a, 0x09, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x2f, 0x0a, 0x0b, 0x49, 0x6e, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73,
	0x74, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x0b, 0x49, 0x6e, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x8a, 0x01, 0x0a,
	0x11, 0x49, 0x6e, 0x73, 0x74, 0x41, 0x70, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x4b, 0x0a, 0x11, 0x49, 0x6e, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x73, 0x70, 0x12, 0x36,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x41, 0x70, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x5e, 0x0a, 0x0e, 0x49, 0x6e, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x73, 0x74, 0x41, 0x70, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x9e, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x1a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x52, 0x6f,
	0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x47, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa6, 0x01, 0x0a, 0x0c, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0d, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x0e, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x12, 0x4d, 0x0a, 0x0f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x65, 0x61, 0x63, 0x68, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x61, 0x63, 0x68,
	0x65, 0x72, 0x52, 0x0f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x61, 0x63,
	0x68, 0x65, 0x72, 0x22, 0x33, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x65, 0x0a, 0x0f, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x61, 0x63, 0x68, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x32,
	0x82, 0x0e, 0x0a, 0x0b, 0x49, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x4e, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x41, 0x72, 0x65, 0x61, 0x73, 0x12, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x72, 0x65, 0x61, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x72, 0x65, 0x61, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x57, 0x0a, 0x0b, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x12, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x63, 0x63, 0x49, 0x6e, 0x73, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x49, 0x6e, 0x73, 0x74, 0x4c, 0x69, 0x6e, 0x6b,
	0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63,
	0x49, 0x6e, 0x73, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5d, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x12, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69,
	0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x63, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74,
	0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61,
	0x75, 0x6e, 0x63, 0x68, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x22, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73,
	0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69,
	0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49,
	0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00, 0x12, 0x5f,
	0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x42, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12,
	0x53, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x43, 0x61, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6c, 0x61,
	0x67, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72,
	0x73, 0x74, 0x43, 0x61, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6c, 0x61, 0x67,
	0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x67, 0x0a,
	0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74,
	0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73,
	0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x49, 0x6e, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69,
	0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x46, 0x75, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x51,
	0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x12, 0x63, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x41, 0x70, 0x70, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73,
	0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x41, 0x70, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x73, 0x74, 0x41, 0x70, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x2b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x42, 0x17, 0x5a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x74,
	0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_institution_v1_institution_proto_rawDescOnce sync.Once
	file_institution_v1_institution_proto_rawDescData = file_institution_v1_institution_proto_rawDesc
)

func file_institution_v1_institution_proto_rawDescGZIP() []byte {
	file_institution_v1_institution_proto_rawDescOnce.Do(func() {
		file_institution_v1_institution_proto_rawDescData = protoimpl.X.CompressGZIP(file_institution_v1_institution_proto_rawDescData)
	})
	return file_institution_v1_institution_proto_rawDescData
}

var file_institution_v1_institution_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_institution_v1_institution_proto_goTypes = []interface{}{
	(*UpdateInstInfoReq)(nil),            // 0: api.institution.v1.UpdateInstInfoReq
	(*GetInstFullInfoReq)(nil),           // 1: api.institution.v1.GetInstFullInfoReq
	(*GetInstFullInfoRsp)(nil),           // 2: api.institution.v1.GetInstFullInfoRsp
	(*UpdateFirstCardUpdateFlagReq)(nil), // 3: api.institution.v1.UpdateFirstCardUpdateFlagReq
	(*UpdateAccessSetUpdateFlagReq)(nil), // 4: api.institution.v1.UpdateAccessSetUpdateFlagReq
	(*UpdateInstParamReq)(nil),           // 5: api.institution.v1.UpdateInstParamReq
	(*UpdateInstParamByCodeReq)(nil),     // 6: api.institution.v1.UpdateInstParamByCodeReq
	(*ListInstParamReq)(nil),             // 7: api.institution.v1.ListInstParamReq
	(*ListInstParamRsp)(nil),             // 8: api.institution.v1.ListInstParamRsp
	(*InstParamInfo)(nil),                // 9: api.institution.v1.InstParamInfo
	(*GetInstParamReq)(nil),              // 10: api.institution.v1.GetInstParamReq
	(*GetAreasReq)(nil),                  // 11: api.institution.v1.GetAreasReq
	(*GetAreasRsp)(nil),                  // 12: api.institution.v1.GetAreasRsp
	(*AreaInfo)(nil),                     // 13: api.institution.v1.AreaInfo
	(*IsExistInstReq)(nil),               // 14: api.institution.v1.IsExistInstReq
	(*IsExistInstRsp)(nil),               // 15: api.institution.v1.IsExistInstRsp
	(*ListAccInstLinkReq)(nil),           // 16: api.institution.v1.ListAccInstLinkReq
	(*ListAccInstLinkRsp)(nil),           // 17: api.institution.v1.ListAccInstLinkRsp
	(*BriefInstInfo)(nil),                // 18: api.institution.v1.BriefInstInfo
	(*GetEnterpriseReq)(nil),             // 19: api.institution.v1.GetEnterpriseReq
	(*GetEnterpriseRsp)(nil),             // 20: api.institution.v1.GetEnterpriseRsp
	(*EnterpriseInfo)(nil),               // 21: api.institution.v1.EnterpriseInfo
	(*CreateInstReq)(nil),                // 22: api.institution.v1.CreateInstReq
	(*CreateInstRsp)(nil),                // 23: api.institution.v1.CreateInstRsp
	(*GetLaunchScreenReq)(nil),           // 24: api.institution.v1.GetLaunchScreenReq
	(*GetLaunchScreenRsp)(nil),           // 25: api.institution.v1.GetLaunchScreenRsp
	(*GetUserInfoReq)(nil),               // 26: api.institution.v1.GetUserInfoReq
	(*GetUserInfoRsp)(nil),               // 27: api.institution.v1.GetUserInfoRsp
	(*UserInfo)(nil),                     // 28: api.institution.v1.UserInfo
	(*ClassInfo)(nil),                    // 29: api.institution.v1.ClassInfo
	(*InstInfoReq)(nil),                  // 30: api.institution.v1.InstInfoReq
	(*InstInfoRsp)(nil),                  // 31: api.institution.v1.InstInfoRsp
	(*InstAppSettingReq)(nil),            // 32: api.institution.v1.InstAppSettingReq
	(*InstAppSettingRsp)(nil),            // 33: api.institution.v1.InstAppSettingRsp
	(*InstAppSetting)(nil),               // 34: api.institution.v1.InstAppSetting
	(*UpdateInstAppSettingReq)(nil),      // 35: api.institution.v1.UpdateInstAppSettingReq
	(*UserRelationReq)(nil),              // 36: api.institution.v1.UserRelationReq
	(*UserRelationRsp)(nil),              // 37: api.institution.v1.UserRelationRsp
	(*UserRelation)(nil),                 // 38: api.institution.v1.UserRelation
	(*RelationClass)(nil),                // 39: api.institution.v1.RelationClass
	(*RelationTeacher)(nil),              // 40: api.institution.v1.RelationTeacher
	nil,                                  // 41: api.institution.v1.UpdateInstParamReq.ParamsEntry
	nil,                                  // 42: api.institution.v1.GetUserInfoRsp.StaffInfoEntry
	nil,                                  // 43: api.institution.v1.GetUserInfoRsp.StudentInfoEntry
	nil,                                  // 44: api.institution.v1.GetUserInfoRsp.ClassInfoEntry
	(*emptypb.Empty)(nil),                // 45: google.protobuf.Empty
}
var file_institution_v1_institution_proto_depIdxs = []int32{
	41, // 0: api.institution.v1.UpdateInstParamReq.params:type_name -> api.institution.v1.UpdateInstParamReq.ParamsEntry
	9,  // 1: api.institution.v1.ListInstParamRsp.list:type_name -> api.institution.v1.InstParamInfo
	13, // 2: api.institution.v1.GetAreasRsp.data:type_name -> api.institution.v1.AreaInfo
	18, // 3: api.institution.v1.ListAccInstLinkRsp.data:type_name -> api.institution.v1.BriefInstInfo
	21, // 4: api.institution.v1.GetEnterpriseRsp.data:type_name -> api.institution.v1.EnterpriseInfo
	42, // 5: api.institution.v1.GetUserInfoRsp.staff_info:type_name -> api.institution.v1.GetUserInfoRsp.StaffInfoEntry
	43, // 6: api.institution.v1.GetUserInfoRsp.student_info:type_name -> api.institution.v1.GetUserInfoRsp.StudentInfoEntry
	44, // 7: api.institution.v1.GetUserInfoRsp.class_info:type_name -> api.institution.v1.GetUserInfoRsp.ClassInfoEntry
	34, // 8: api.institution.v1.InstAppSettingRsp.data:type_name -> api.institution.v1.InstAppSetting
	38, // 9: api.institution.v1.UserRelationRsp.data:type_name -> api.institution.v1.UserRelation
	39, // 10: api.institution.v1.UserRelation.relationClass:type_name -> api.institution.v1.RelationClass
	40, // 11: api.institution.v1.UserRelation.relationTeacher:type_name -> api.institution.v1.RelationTeacher
	28, // 12: api.institution.v1.GetUserInfoRsp.StaffInfoEntry.value:type_name -> api.institution.v1.UserInfo
	28, // 13: api.institution.v1.GetUserInfoRsp.StudentInfoEntry.value:type_name -> api.institution.v1.UserInfo
	29, // 14: api.institution.v1.GetUserInfoRsp.ClassInfoEntry.value:type_name -> api.institution.v1.ClassInfo
	11, // 15: api.institution.v1.Institution.GetAreas:input_type -> api.institution.v1.GetAreasReq
	14, // 16: api.institution.v1.Institution.IsExistInst:input_type -> api.institution.v1.IsExistInstReq
	16, // 17: api.institution.v1.Institution.ListAccInstLink:input_type -> api.institution.v1.ListAccInstLinkReq
	19, // 18: api.institution.v1.Institution.GetEnterprise:input_type -> api.institution.v1.GetEnterpriseReq
	22, // 19: api.institution.v1.Institution.CreateInst:input_type -> api.institution.v1.CreateInstReq
	24, // 20: api.institution.v1.Institution.GetLaunchScreen:input_type -> api.institution.v1.GetLaunchScreenReq
	26, // 21: api.institution.v1.Institution.GetUserInfoByIds:input_type -> api.institution.v1.GetUserInfoReq
	7,  // 22: api.institution.v1.Institution.ListInstParam:input_type -> api.institution.v1.ListInstParamReq
	10, // 23: api.institution.v1.Institution.GetInstParam:input_type -> api.institution.v1.GetInstParamReq
	6,  // 24: api.institution.v1.Institution.UpdateInstParamByCode:input_type -> api.institution.v1.UpdateInstParamByCodeReq
	5,  // 25: api.institution.v1.Institution.UpdateInstParam:input_type -> api.institution.v1.UpdateInstParamReq
	3,  // 26: api.institution.v1.Institution.UpdateFirstCardUpdateFlag:input_type -> api.institution.v1.UpdateFirstCardUpdateFlagReq
	4,  // 27: api.institution.v1.Institution.UpdateAccessSetUpdateFlag:input_type -> api.institution.v1.UpdateAccessSetUpdateFlagReq
	30, // 28: api.institution.v1.Institution.GetInstInfo:input_type -> api.institution.v1.InstInfoReq
	1,  // 29: api.institution.v1.Institution.GetInstFullInfo:input_type -> api.institution.v1.GetInstFullInfoReq
	0,  // 30: api.institution.v1.Institution.UpdateInstInfo:input_type -> api.institution.v1.UpdateInstInfoReq
	32, // 31: api.institution.v1.Institution.GetInstAppSetting:input_type -> api.institution.v1.InstAppSettingReq
	35, // 32: api.institution.v1.Institution.UpdateInstAppSetting:input_type -> api.institution.v1.UpdateInstAppSettingReq
	36, // 33: api.institution.v1.Institution.GetUserRelation:input_type -> api.institution.v1.UserRelationReq
	12, // 34: api.institution.v1.Institution.GetAreas:output_type -> api.institution.v1.GetAreasRsp
	15, // 35: api.institution.v1.Institution.IsExistInst:output_type -> api.institution.v1.IsExistInstRsp
	17, // 36: api.institution.v1.Institution.ListAccInstLink:output_type -> api.institution.v1.ListAccInstLinkRsp
	20, // 37: api.institution.v1.Institution.GetEnterprise:output_type -> api.institution.v1.GetEnterpriseRsp
	23, // 38: api.institution.v1.Institution.CreateInst:output_type -> api.institution.v1.CreateInstRsp
	25, // 39: api.institution.v1.Institution.GetLaunchScreen:output_type -> api.institution.v1.GetLaunchScreenRsp
	27, // 40: api.institution.v1.Institution.GetUserInfoByIds:output_type -> api.institution.v1.GetUserInfoRsp
	8,  // 41: api.institution.v1.Institution.ListInstParam:output_type -> api.institution.v1.ListInstParamRsp
	9,  // 42: api.institution.v1.Institution.GetInstParam:output_type -> api.institution.v1.InstParamInfo
	45, // 43: api.institution.v1.Institution.UpdateInstParamByCode:output_type -> google.protobuf.Empty
	45, // 44: api.institution.v1.Institution.UpdateInstParam:output_type -> google.protobuf.Empty
	45, // 45: api.institution.v1.Institution.UpdateFirstCardUpdateFlag:output_type -> google.protobuf.Empty
	45, // 46: api.institution.v1.Institution.UpdateAccessSetUpdateFlag:output_type -> google.protobuf.Empty
	31, // 47: api.institution.v1.Institution.GetInstInfo:output_type -> api.institution.v1.InstInfoRsp
	2,  // 48: api.institution.v1.Institution.GetInstFullInfo:output_type -> api.institution.v1.GetInstFullInfoRsp
	45, // 49: api.institution.v1.Institution.UpdateInstInfo:output_type -> google.protobuf.Empty
	33, // 50: api.institution.v1.Institution.GetInstAppSetting:output_type -> api.institution.v1.InstAppSettingRsp
	45, // 51: api.institution.v1.Institution.UpdateInstAppSetting:output_type -> google.protobuf.Empty
	37, // 52: api.institution.v1.Institution.GetUserRelation:output_type -> api.institution.v1.UserRelationRsp
	34, // [34:53] is the sub-list for method output_type
	15, // [15:34] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_institution_v1_institution_proto_init() }
func file_institution_v1_institution_proto_init() {
	if File_institution_v1_institution_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_institution_v1_institution_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateInstInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstFullInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstFullInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFirstCardUpdateFlagReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccessSetUpdateFlagReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateInstParamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateInstParamByCodeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInstParamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInstParamRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstParamInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstParamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAreasReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAreasRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreaInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsExistInstReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsExistInstRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccInstLinkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccInstLinkRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BriefInstInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterpriseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInstReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInstRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLaunchScreenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLaunchScreenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstAppSettingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstAppSettingRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstAppSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateInstAppSettingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserRelationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserRelationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserRelation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelationClass); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_institution_v1_institution_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelationTeacher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_institution_v1_institution_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_institution_v1_institution_proto_goTypes,
		DependencyIndexes: file_institution_v1_institution_proto_depIdxs,
		MessageInfos:      file_institution_v1_institution_proto_msgTypes,
	}.Build()
	File_institution_v1_institution_proto = out.File
	file_institution_v1_institution_proto_rawDesc = nil
	file_institution_v1_institution_proto_goTypes = nil
	file_institution_v1_institution_proto_depIdxs = nil
}
