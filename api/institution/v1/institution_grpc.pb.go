// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: institution/v1/institution.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Institution_GetAreas_FullMethodName                  = "/api.institution.v1.Institution/GetAreas"
	Institution_IsExistInst_FullMethodName               = "/api.institution.v1.Institution/IsExistInst"
	Institution_ListAccInstLink_FullMethodName           = "/api.institution.v1.Institution/ListAccInstLink"
	Institution_GetEnterprise_FullMethodName             = "/api.institution.v1.Institution/GetEnterprise"
	Institution_CreateInst_FullMethodName                = "/api.institution.v1.Institution/CreateInst"
	Institution_GetLaunchScreen_FullMethodName           = "/api.institution.v1.Institution/GetLaunchScreen"
	Institution_GetUserInfoByIds_FullMethodName          = "/api.institution.v1.Institution/GetUserInfoByIds"
	Institution_ListInstParam_FullMethodName             = "/api.institution.v1.Institution/ListInstParam"
	Institution_GetInstParam_FullMethodName              = "/api.institution.v1.Institution/GetInstParam"
	Institution_UpdateInstParamByCode_FullMethodName     = "/api.institution.v1.Institution/UpdateInstParamByCode"
	Institution_UpdateInstParam_FullMethodName           = "/api.institution.v1.Institution/UpdateInstParam"
	Institution_UpdateFirstCardUpdateFlag_FullMethodName = "/api.institution.v1.Institution/UpdateFirstCardUpdateFlag"
	Institution_UpdateAccessSetUpdateFlag_FullMethodName = "/api.institution.v1.Institution/UpdateAccessSetUpdateFlag"
	Institution_GetInstInfo_FullMethodName               = "/api.institution.v1.Institution/GetInstInfo"
	Institution_GetInstFullInfo_FullMethodName           = "/api.institution.v1.Institution/GetInstFullInfo"
	Institution_UpdateInstInfo_FullMethodName            = "/api.institution.v1.Institution/UpdateInstInfo"
	Institution_GetInstAppSetting_FullMethodName         = "/api.institution.v1.Institution/GetInstAppSetting"
	Institution_UpdateInstAppSetting_FullMethodName      = "/api.institution.v1.Institution/UpdateInstAppSetting"
	Institution_GetUserRelation_FullMethodName           = "/api.institution.v1.Institution/GetUserRelation"
)

// InstitutionClient is the client API for Institution service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type InstitutionClient interface {
	// 获取地区列表
	GetAreas(ctx context.Context, in *GetAreasReq, opts ...grpc.CallOption) (*GetAreasRsp, error)
	// 机构是否存在
	IsExistInst(ctx context.Context, in *IsExistInstReq, opts ...grpc.CallOption) (*IsExistInstRsp, error)
	// 获取账号关联的机构列表
	ListAccInstLink(ctx context.Context, in *ListAccInstLinkReq, opts ...grpc.CallOption) (*ListAccInstLinkRsp, error)
	// 根据关键字匹配企业列表
	GetEnterprise(ctx context.Context, in *GetEnterpriseReq, opts ...grpc.CallOption) (*GetEnterpriseRsp, error)
	// 创建机构
	CreateInst(ctx context.Context, in *CreateInstReq, opts ...grpc.CallOption) (*CreateInstRsp, error)
	// 获取启动页信息
	GetLaunchScreen(ctx context.Context, in *GetLaunchScreenReq, opts ...grpc.CallOption) (*GetLaunchScreenRsp, error)
	// 获取学校用户信息，基于用户id
	GetUserInfoByIds(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoRsp, error)
	// 获取学校系统参数列表
	ListInstParam(ctx context.Context, in *ListInstParamReq, opts ...grpc.CallOption) (*ListInstParamRsp, error)
	// 获取学校系统参数
	GetInstParam(ctx context.Context, in *GetInstParamReq, opts ...grpc.CallOption) (*InstParamInfo, error)
	// 更新学校单个系统参数
	UpdateInstParamByCode(ctx context.Context, in *UpdateInstParamByCodeReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新学校系统参数
	UpdateInstParam(ctx context.Context, in *UpdateInstParamReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新门禁首卡更新标识
	UpdateFirstCardUpdateFlag(ctx context.Context, in *UpdateFirstCardUpdateFlagReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新出入园设置更新标识
	UpdateAccessSetUpdateFlag(ctx context.Context, in *UpdateAccessSetUpdateFlagReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 学校信息
	GetInstInfo(ctx context.Context, in *InstInfoReq, opts ...grpc.CallOption) (*InstInfoRsp, error)
	// 获取学校详细信息
	GetInstFullInfo(ctx context.Context, in *GetInstFullInfoReq, opts ...grpc.CallOption) (*GetInstFullInfoRsp, error)
	// 修改学校信息
	UpdateInstInfo(ctx context.Context, in *UpdateInstInfoReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取学校app设置
	GetInstAppSetting(ctx context.Context, in *InstAppSettingReq, opts ...grpc.CallOption) (*InstAppSettingRsp, error)
	// 更新学校app设置
	UpdateInstAppSetting(ctx context.Context, in *UpdateInstAppSettingReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 当前用户的关联信息
	GetUserRelation(ctx context.Context, in *UserRelationReq, opts ...grpc.CallOption) (*UserRelationRsp, error)
}

type institutionClient struct {
	cc grpc.ClientConnInterface
}

func NewInstitutionClient(cc grpc.ClientConnInterface) InstitutionClient {
	return &institutionClient{cc}
}

func (c *institutionClient) GetAreas(ctx context.Context, in *GetAreasReq, opts ...grpc.CallOption) (*GetAreasRsp, error) {
	out := new(GetAreasRsp)
	err := c.cc.Invoke(ctx, Institution_GetAreas_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) IsExistInst(ctx context.Context, in *IsExistInstReq, opts ...grpc.CallOption) (*IsExistInstRsp, error) {
	out := new(IsExistInstRsp)
	err := c.cc.Invoke(ctx, Institution_IsExistInst_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) ListAccInstLink(ctx context.Context, in *ListAccInstLinkReq, opts ...grpc.CallOption) (*ListAccInstLinkRsp, error) {
	out := new(ListAccInstLinkRsp)
	err := c.cc.Invoke(ctx, Institution_ListAccInstLink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) GetEnterprise(ctx context.Context, in *GetEnterpriseReq, opts ...grpc.CallOption) (*GetEnterpriseRsp, error) {
	out := new(GetEnterpriseRsp)
	err := c.cc.Invoke(ctx, Institution_GetEnterprise_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) CreateInst(ctx context.Context, in *CreateInstReq, opts ...grpc.CallOption) (*CreateInstRsp, error) {
	out := new(CreateInstRsp)
	err := c.cc.Invoke(ctx, Institution_CreateInst_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) GetLaunchScreen(ctx context.Context, in *GetLaunchScreenReq, opts ...grpc.CallOption) (*GetLaunchScreenRsp, error) {
	out := new(GetLaunchScreenRsp)
	err := c.cc.Invoke(ctx, Institution_GetLaunchScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) GetUserInfoByIds(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoRsp, error) {
	out := new(GetUserInfoRsp)
	err := c.cc.Invoke(ctx, Institution_GetUserInfoByIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) ListInstParam(ctx context.Context, in *ListInstParamReq, opts ...grpc.CallOption) (*ListInstParamRsp, error) {
	out := new(ListInstParamRsp)
	err := c.cc.Invoke(ctx, Institution_ListInstParam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) GetInstParam(ctx context.Context, in *GetInstParamReq, opts ...grpc.CallOption) (*InstParamInfo, error) {
	out := new(InstParamInfo)
	err := c.cc.Invoke(ctx, Institution_GetInstParam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) UpdateInstParamByCode(ctx context.Context, in *UpdateInstParamByCodeReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Institution_UpdateInstParamByCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) UpdateInstParam(ctx context.Context, in *UpdateInstParamReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Institution_UpdateInstParam_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) UpdateFirstCardUpdateFlag(ctx context.Context, in *UpdateFirstCardUpdateFlagReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Institution_UpdateFirstCardUpdateFlag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) UpdateAccessSetUpdateFlag(ctx context.Context, in *UpdateAccessSetUpdateFlagReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Institution_UpdateAccessSetUpdateFlag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) GetInstInfo(ctx context.Context, in *InstInfoReq, opts ...grpc.CallOption) (*InstInfoRsp, error) {
	out := new(InstInfoRsp)
	err := c.cc.Invoke(ctx, Institution_GetInstInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) GetInstFullInfo(ctx context.Context, in *GetInstFullInfoReq, opts ...grpc.CallOption) (*GetInstFullInfoRsp, error) {
	out := new(GetInstFullInfoRsp)
	err := c.cc.Invoke(ctx, Institution_GetInstFullInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) UpdateInstInfo(ctx context.Context, in *UpdateInstInfoReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Institution_UpdateInstInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) GetInstAppSetting(ctx context.Context, in *InstAppSettingReq, opts ...grpc.CallOption) (*InstAppSettingRsp, error) {
	out := new(InstAppSettingRsp)
	err := c.cc.Invoke(ctx, Institution_GetInstAppSetting_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) UpdateInstAppSetting(ctx context.Context, in *UpdateInstAppSettingReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Institution_UpdateInstAppSetting_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *institutionClient) GetUserRelation(ctx context.Context, in *UserRelationReq, opts ...grpc.CallOption) (*UserRelationRsp, error) {
	out := new(UserRelationRsp)
	err := c.cc.Invoke(ctx, Institution_GetUserRelation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InstitutionServer is the server API for Institution service.
// All implementations must embed UnimplementedInstitutionServer
// for forward compatibility
type InstitutionServer interface {
	// 获取地区列表
	GetAreas(context.Context, *GetAreasReq) (*GetAreasRsp, error)
	// 机构是否存在
	IsExistInst(context.Context, *IsExistInstReq) (*IsExistInstRsp, error)
	// 获取账号关联的机构列表
	ListAccInstLink(context.Context, *ListAccInstLinkReq) (*ListAccInstLinkRsp, error)
	// 根据关键字匹配企业列表
	GetEnterprise(context.Context, *GetEnterpriseReq) (*GetEnterpriseRsp, error)
	// 创建机构
	CreateInst(context.Context, *CreateInstReq) (*CreateInstRsp, error)
	// 获取启动页信息
	GetLaunchScreen(context.Context, *GetLaunchScreenReq) (*GetLaunchScreenRsp, error)
	// 获取学校用户信息，基于用户id
	GetUserInfoByIds(context.Context, *GetUserInfoReq) (*GetUserInfoRsp, error)
	// 获取学校系统参数列表
	ListInstParam(context.Context, *ListInstParamReq) (*ListInstParamRsp, error)
	// 获取学校系统参数
	GetInstParam(context.Context, *GetInstParamReq) (*InstParamInfo, error)
	// 更新学校单个系统参数
	UpdateInstParamByCode(context.Context, *UpdateInstParamByCodeReq) (*emptypb.Empty, error)
	// 更新学校系统参数
	UpdateInstParam(context.Context, *UpdateInstParamReq) (*emptypb.Empty, error)
	// 更新门禁首卡更新标识
	UpdateFirstCardUpdateFlag(context.Context, *UpdateFirstCardUpdateFlagReq) (*emptypb.Empty, error)
	// 更新出入园设置更新标识
	UpdateAccessSetUpdateFlag(context.Context, *UpdateAccessSetUpdateFlagReq) (*emptypb.Empty, error)
	// 学校信息
	GetInstInfo(context.Context, *InstInfoReq) (*InstInfoRsp, error)
	// 获取学校详细信息
	GetInstFullInfo(context.Context, *GetInstFullInfoReq) (*GetInstFullInfoRsp, error)
	// 修改学校信息
	UpdateInstInfo(context.Context, *UpdateInstInfoReq) (*emptypb.Empty, error)
	// 获取学校app设置
	GetInstAppSetting(context.Context, *InstAppSettingReq) (*InstAppSettingRsp, error)
	// 更新学校app设置
	UpdateInstAppSetting(context.Context, *UpdateInstAppSettingReq) (*emptypb.Empty, error)
	// 当前用户的关联信息
	GetUserRelation(context.Context, *UserRelationReq) (*UserRelationRsp, error)
	mustEmbedUnimplementedInstitutionServer()
}

// UnimplementedInstitutionServer must be embedded to have forward compatible implementations.
type UnimplementedInstitutionServer struct {
}

func (UnimplementedInstitutionServer) GetAreas(context.Context, *GetAreasReq) (*GetAreasRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAreas not implemented")
}
func (UnimplementedInstitutionServer) IsExistInst(context.Context, *IsExistInstReq) (*IsExistInstRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsExistInst not implemented")
}
func (UnimplementedInstitutionServer) ListAccInstLink(context.Context, *ListAccInstLinkReq) (*ListAccInstLinkRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAccInstLink not implemented")
}
func (UnimplementedInstitutionServer) GetEnterprise(context.Context, *GetEnterpriseReq) (*GetEnterpriseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterprise not implemented")
}
func (UnimplementedInstitutionServer) CreateInst(context.Context, *CreateInstReq) (*CreateInstRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInst not implemented")
}
func (UnimplementedInstitutionServer) GetLaunchScreen(context.Context, *GetLaunchScreenReq) (*GetLaunchScreenRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLaunchScreen not implemented")
}
func (UnimplementedInstitutionServer) GetUserInfoByIds(context.Context, *GetUserInfoReq) (*GetUserInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfoByIds not implemented")
}
func (UnimplementedInstitutionServer) ListInstParam(context.Context, *ListInstParamReq) (*ListInstParamRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstParam not implemented")
}
func (UnimplementedInstitutionServer) GetInstParam(context.Context, *GetInstParamReq) (*InstParamInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstParam not implemented")
}
func (UnimplementedInstitutionServer) UpdateInstParamByCode(context.Context, *UpdateInstParamByCodeReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstParamByCode not implemented")
}
func (UnimplementedInstitutionServer) UpdateInstParam(context.Context, *UpdateInstParamReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstParam not implemented")
}
func (UnimplementedInstitutionServer) UpdateFirstCardUpdateFlag(context.Context, *UpdateFirstCardUpdateFlagReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFirstCardUpdateFlag not implemented")
}
func (UnimplementedInstitutionServer) UpdateAccessSetUpdateFlag(context.Context, *UpdateAccessSetUpdateFlagReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAccessSetUpdateFlag not implemented")
}
func (UnimplementedInstitutionServer) GetInstInfo(context.Context, *InstInfoReq) (*InstInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstInfo not implemented")
}
func (UnimplementedInstitutionServer) GetInstFullInfo(context.Context, *GetInstFullInfoReq) (*GetInstFullInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstFullInfo not implemented")
}
func (UnimplementedInstitutionServer) UpdateInstInfo(context.Context, *UpdateInstInfoReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstInfo not implemented")
}
func (UnimplementedInstitutionServer) GetInstAppSetting(context.Context, *InstAppSettingReq) (*InstAppSettingRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstAppSetting not implemented")
}
func (UnimplementedInstitutionServer) UpdateInstAppSetting(context.Context, *UpdateInstAppSettingReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstAppSetting not implemented")
}
func (UnimplementedInstitutionServer) GetUserRelation(context.Context, *UserRelationReq) (*UserRelationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserRelation not implemented")
}
func (UnimplementedInstitutionServer) mustEmbedUnimplementedInstitutionServer() {}

// UnsafeInstitutionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InstitutionServer will
// result in compilation errors.
type UnsafeInstitutionServer interface {
	mustEmbedUnimplementedInstitutionServer()
}

func RegisterInstitutionServer(s grpc.ServiceRegistrar, srv InstitutionServer) {
	s.RegisterService(&Institution_ServiceDesc, srv)
}

func _Institution_GetAreas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAreasReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).GetAreas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_GetAreas_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).GetAreas(ctx, req.(*GetAreasReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_IsExistInst_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsExistInstReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).IsExistInst(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_IsExistInst_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).IsExistInst(ctx, req.(*IsExistInstReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_ListAccInstLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAccInstLinkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).ListAccInstLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_ListAccInstLink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).ListAccInstLink(ctx, req.(*ListAccInstLinkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_GetEnterprise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).GetEnterprise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_GetEnterprise_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).GetEnterprise(ctx, req.(*GetEnterpriseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_CreateInst_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInstReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).CreateInst(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_CreateInst_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).CreateInst(ctx, req.(*CreateInstReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_GetLaunchScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLaunchScreenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).GetLaunchScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_GetLaunchScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).GetLaunchScreen(ctx, req.(*GetLaunchScreenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_GetUserInfoByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).GetUserInfoByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_GetUserInfoByIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).GetUserInfoByIds(ctx, req.(*GetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_ListInstParam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstParamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).ListInstParam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_ListInstParam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).ListInstParam(ctx, req.(*ListInstParamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_GetInstParam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstParamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).GetInstParam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_GetInstParam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).GetInstParam(ctx, req.(*GetInstParamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_UpdateInstParamByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstParamByCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).UpdateInstParamByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_UpdateInstParamByCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).UpdateInstParamByCode(ctx, req.(*UpdateInstParamByCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_UpdateInstParam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstParamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).UpdateInstParam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_UpdateInstParam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).UpdateInstParam(ctx, req.(*UpdateInstParamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_UpdateFirstCardUpdateFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFirstCardUpdateFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).UpdateFirstCardUpdateFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_UpdateFirstCardUpdateFlag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).UpdateFirstCardUpdateFlag(ctx, req.(*UpdateFirstCardUpdateFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_UpdateAccessSetUpdateFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAccessSetUpdateFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).UpdateAccessSetUpdateFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_UpdateAccessSetUpdateFlag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).UpdateAccessSetUpdateFlag(ctx, req.(*UpdateAccessSetUpdateFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_GetInstInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).GetInstInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_GetInstInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).GetInstInfo(ctx, req.(*InstInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_GetInstFullInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstFullInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).GetInstFullInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_GetInstFullInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).GetInstFullInfo(ctx, req.(*GetInstFullInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_UpdateInstInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).UpdateInstInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_UpdateInstInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).UpdateInstInfo(ctx, req.(*UpdateInstInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_GetInstAppSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstAppSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).GetInstAppSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_GetInstAppSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).GetInstAppSetting(ctx, req.(*InstAppSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_UpdateInstAppSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstAppSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).UpdateInstAppSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_UpdateInstAppSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).UpdateInstAppSetting(ctx, req.(*UpdateInstAppSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Institution_GetUserRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserRelationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstitutionServer).GetUserRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Institution_GetUserRelation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstitutionServer).GetUserRelation(ctx, req.(*UserRelationReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Institution_ServiceDesc is the grpc.ServiceDesc for Institution service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Institution_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.institution.v1.Institution",
	HandlerType: (*InstitutionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAreas",
			Handler:    _Institution_GetAreas_Handler,
		},
		{
			MethodName: "IsExistInst",
			Handler:    _Institution_IsExistInst_Handler,
		},
		{
			MethodName: "ListAccInstLink",
			Handler:    _Institution_ListAccInstLink_Handler,
		},
		{
			MethodName: "GetEnterprise",
			Handler:    _Institution_GetEnterprise_Handler,
		},
		{
			MethodName: "CreateInst",
			Handler:    _Institution_CreateInst_Handler,
		},
		{
			MethodName: "GetLaunchScreen",
			Handler:    _Institution_GetLaunchScreen_Handler,
		},
		{
			MethodName: "GetUserInfoByIds",
			Handler:    _Institution_GetUserInfoByIds_Handler,
		},
		{
			MethodName: "ListInstParam",
			Handler:    _Institution_ListInstParam_Handler,
		},
		{
			MethodName: "GetInstParam",
			Handler:    _Institution_GetInstParam_Handler,
		},
		{
			MethodName: "UpdateInstParamByCode",
			Handler:    _Institution_UpdateInstParamByCode_Handler,
		},
		{
			MethodName: "UpdateInstParam",
			Handler:    _Institution_UpdateInstParam_Handler,
		},
		{
			MethodName: "UpdateFirstCardUpdateFlag",
			Handler:    _Institution_UpdateFirstCardUpdateFlag_Handler,
		},
		{
			MethodName: "UpdateAccessSetUpdateFlag",
			Handler:    _Institution_UpdateAccessSetUpdateFlag_Handler,
		},
		{
			MethodName: "GetInstInfo",
			Handler:    _Institution_GetInstInfo_Handler,
		},
		{
			MethodName: "GetInstFullInfo",
			Handler:    _Institution_GetInstFullInfo_Handler,
		},
		{
			MethodName: "UpdateInstInfo",
			Handler:    _Institution_UpdateInstInfo_Handler,
		},
		{
			MethodName: "GetInstAppSetting",
			Handler:    _Institution_GetInstAppSetting_Handler,
		},
		{
			MethodName: "UpdateInstAppSetting",
			Handler:    _Institution_UpdateInstAppSetting_Handler,
		},
		{
			MethodName: "GetUserRelation",
			Handler:    _Institution_GetUserRelation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "institution/v1/institution.proto",
}
