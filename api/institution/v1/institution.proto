syntax = "proto3";

package api.institution.v1;

option go_package = "api/institution/v1;v1";

import "google/protobuf/empty.proto";
import "validate/validate.proto";

//生成proto代码
//kratos proto client api/institution/v1/institution.proto

//生成service代码
//kratos proto server api/institution/v1/institution.proto

//机构（学校服务）
service Institution {
  // 获取地区列表
  rpc GetAreas(GetAreasReq) returns (GetAreasRsp) {}

  // 机构是否存在
  rpc IsExistInst(IsExistInstReq) returns(IsExistInstRsp){}

  // 获取账号关联的机构列表
  rpc ListAccInstLink(ListAccInstLinkReq) returns(ListAccInstLinkRsp){}

  // 根据关键字匹配企业列表
  rpc GetEnterprise(GetEnterpriseReq) returns(GetEnterpriseRsp){}

  // 创建机构
  rpc CreateInst(CreateInstReq) returns (CreateInstRsp){}

  // 获取机构信息
//  rpc GetInstInfo(GetInstInfoReq) returns (GetInstInfoRsp){}

  // 获取启动页信息
  rpc GetLaunchScreen(GetLaunchScreenReq) returns(GetLaunchScreenRsp){}

  // 获取学校用户信息，基于用户id
  rpc GetUserInfoByIds (GetUserInfoReq) returns (GetUserInfoRsp){};

  // 获取学校系统参数列表
  rpc ListInstParam(ListInstParamReq) returns (ListInstParamRsp){};

  // 获取学校系统参数
  rpc GetInstParam(GetInstParamReq) returns (InstParamInfo){};

  // 更新学校单个系统参数
  rpc UpdateInstParamByCode(UpdateInstParamByCodeReq) returns (google.protobuf.Empty) {};

  // 更新学校系统参数
  rpc UpdateInstParam(UpdateInstParamReq) returns (google.protobuf.Empty) {};

  // 更新门禁首卡更新标识
  rpc UpdateFirstCardUpdateFlag(UpdateFirstCardUpdateFlagReq) returns (google.protobuf.Empty) {};

  // 更新出入园设置更新标识
  rpc UpdateAccessSetUpdateFlag(UpdateAccessSetUpdateFlagReq) returns (google.protobuf.Empty) {};

  // 学校信息
  rpc GetInstInfo(InstInfoReq) returns(InstInfoRsp){};

  // 获取学校详细信息
  rpc GetInstFullInfo(GetInstFullInfoReq) returns(GetInstFullInfoRsp){};

  // 修改学校信息
  rpc UpdateInstInfo(UpdateInstInfoReq) returns(google.protobuf.Empty){};

  // 获取学校app设置
  rpc GetInstAppSetting(InstAppSettingReq) returns(InstAppSettingRsp){};

  // 更新学校app设置
  rpc UpdateInstAppSetting(UpdateInstAppSettingReq) returns(google.protobuf.Empty){};

  // 当前用户的关联信息
  rpc GetUserRelation(UserRelationReq) returns(UserRelationRsp){};
}

message UpdateInstInfoReq {
  // 学校id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学校简称
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 15}];
  // 学校名字
  string full_name = 3 [(validate.rules).string = {min_len: 1, max_len: 30}];
  //类型  1-公办幼儿园、2-民办幼儿园（普惠）、3-民办幼儿园、4-托育中心
  int32 type = 4 [(validate.rules).int32 = {in: [1, 2, 3, 4]}];
  //幼儿园地址省份code
  string province_code = 5 [(validate.rules).string = {min_len: 1}];
  //幼儿园地址城市code
  string city_code = 6 [(validate.rules).string = {min_len: 1}];
  //幼儿园地址区县code
  string district_code = 7 [(validate.rules).string = {min_len: 1}];
  //详细地址
  string address = 8 [(validate.rules).string = {min_len: 1}];
  //园长姓名
  string admin_name = 9 [(validate.rules).string = {min_len: 1}];
  //园长手机号
  string admin_mobile = 10 [(validate.rules).string = {min_len: 1}];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 11 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 12 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 13 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 14 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message GetInstFullInfoReq {
  // 学校id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}
message GetInstFullInfoRsp {
  // 园所id
  int64 id = 1;
  //名称
  string name = 2;
  // 全称
  string full_name = 3;
  //类型  1-公办幼儿园、2-民办幼儿园（普惠）、3-民办幼儿园、4-托育中心
  int32 type = 4;
  //幼儿园地址省份code
  string province_code = 5;
  //幼儿园地址城市code
  string city_code = 6;
  //幼儿园地址区县code
  string district_code = 7;
  //详细地址
  string address = 8;
  //园长姓名
  string admin_name = 9;
  //园长手机号
  string admin_mobile = 10;
}

message UpdateFirstCardUpdateFlagReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 门禁首卡更新标识 1-更新 0-未更新
  uint32 update_flag = 2 [(validate.rules).uint32 = {in: [0,1]}];
}
message UpdateAccessSetUpdateFlagReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 出入园设置更新标识 1-更新 0-未更新
  uint32 update_flag = 2 [(validate.rules).uint32 = {in: [0,1]}];
}

// 更新学校系统参数请求参数
message UpdateInstParamReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 参数值列表
  map<uint32, string> params = 2 [(validate.rules).map.min_pairs = 1];
}

// 更新学校单个系统参数请求参数
message UpdateInstParamByCodeReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 参数编号
  uint32 code = 2 [(validate.rules).uint32.gt = 0];
  // 参数值
  string value = 3 [(validate.rules).string = {min_len: 1}];
}

// 获取学校系统参数列表请求参数
message ListInstParamReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
}

// 获取学校系统参数列表返回参数
message ListInstParamRsp {
  // 学校系统参数列表
  repeated InstParamInfo list = 1;
}

message InstParamInfo {
  // 学校id
  int64 inst_id = 1;
  // 参数编号
  uint32 code = 2;
  // 参数值
  string value = 3;
}

// 获取学校系统参数请求参数
message GetInstParamReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 参数编号
  uint32 code = 2 [(validate.rules).uint32.gt = 0];
}

// 获取地区列表
message GetAreasReq {
  // 上级code  顶级传0
  string parent_code = 1 [(validate.rules).string = {min_len: 1}];
}

message GetAreasRsp{
  repeated AreaInfo data = 1;
}
message AreaInfo {
  // 地区名称
  string name = 1;
  // 地区code
  string area_code = 2;
  // 父级code
  string parent_code = 3;
  // 地区简称
  string short_name = 4;
  // 层级
  int32 level = 5;
}

// 机构是否存在请求参数
message IsExistInstReq {
  //机构名称
  string name = 1 [(validate.rules).string = {min_len: 1}];
}

// 机构是否存在返回参数
message IsExistInstRsp {
  // 机构是否存在（1：存在，0：不存在）
  int32 is_exist = 1;
}

//获取手机号关联的机构列表
message ListAccInstLinkReq{
  //手机号码
  string mobile = 1 [(validate.rules).string = {min_len: 1}];
}
message ListAccInstLinkRsp{
  // 手机号绑定的机构列表
  repeated BriefInstInfo data = 1;
}
//机构简要信息
message BriefInstInfo {
  // 机构ID
  int64 inst_id = 1;
  // 机构名称
  string name = 2;
  // 全称
  string full_name = 3;
  // 机构图像
  string logo = 4;
  //  是否最近登录（1：是，0：否）
  int32 is_last_login = 5;
  // 创建时间
  string create_time = 6;
}

//根据关键词获取企业列表
message GetEnterpriseReq{
  // 关键词
  string key_words = 1 [(validate.rules).string = {min_len: 1}];
}
message GetEnterpriseRsp{
  // 企业列表
  repeated EnterpriseInfo data = 1;
}
message EnterpriseInfo {
  // 企业名称
  string name = 1;
}

//创建机构
message CreateInstReq{
  //名称
  string inst_name = 1 [(validate.rules).string = {min_len: 1}];
  //类型 1-公办幼儿园、2-民办幼儿园（普惠）、3-民办幼儿园、4-托育中心
  int32 type = 2 [(validate.rules).int32 = {in: [1, 2, 3, 4]}];
  //幼儿园地址省份code
  string province_code = 3 [(validate.rules).string = {min_len: 1}];
  //幼儿园地址城市code
  string city_code = 4 [(validate.rules).string = {min_len: 1}];
  //幼儿园地址区县code
  string district_code = 5 [(validate.rules).string = {min_len: 1}];
  //详细地址
  string address = 6 [(validate.rules).string = {min_len: 1}];
  //园长姓名
  string admin_name = 7 [(validate.rules).string = {min_len: 1}];
  //园长手机号
  string admin_mobile = 8 [(validate.rules).string = {min_len: 1}];
  //短信验证码
  int32 sms_code = 9 [(validate.rules).int32.gt = 0];
}
message CreateInstRsp{
  //账号
  string mobile = 1;
  //默认密码
  string password = 2;
}

//获取APP启动页图片
message GetLaunchScreenReq {
  int64 inst_id = 1 [(validate.rules).int64.gte = 1];
}
message GetLaunchScreenRsp{
  // 启动图片链接地址
  string pic_url = 1;
}


//------->>>>>>>------获取用户信息------<<<<<<<GetUserInfo>>>>>>---MaWei@2023-06-05 14:17----<<<<<<----//
message GetUserInfoReq {
    // 学校id
    int64 inst_id = 1;
    // 提取类型 （1.班级 2.学生 3.老师 4.全园 5.混合）
    int32 type = 2;
    // 是否只返回id
    int32 is_info = 3;
    // 是否返回父母
    int32 is_parent = 4;
    // 用户id
    int64 user_id = 5;
    // 员工ids
    repeated int64 staff_ids = 6;
    // 学生ids
    repeated int64 student_ids = 7;
    // 班级id
    repeated int64 class_ids = 8;
}

message GetUserInfoRsp {
    // 员工信息
    map<int64, UserInfo> staff_info = 1;
    // 学生信息
    map<int64, UserInfo> student_info = 2;
    // 班级信息
    map<int64, ClassInfo> class_info = 3;
}
message UserInfo {
    // 用户id
    int64 user_id = 1;
    // 用户姓名
    string user_name = 2;
    // 用户头像
    string user_avatar = 3;
    // 用户班级
    string class_name = 4;
    // 用户班级id
    int64 class_id = 5;
    // 家长id
    int64 parent_id = 6;
    // 家长姓名
    string parent_name = 7;
    // 家长电话
    string mobile = 8;
    // 家长头像
    string parent_avatar = 9;
    // 关系
    int32 relation = 10;
    // 称谓
    string alias = 11;
}
// 班级信息
message ClassInfo {
    // 班级id
    int64 class_id = 1;
    // 班级名称
    string class_name = 2;
}

message InstInfoReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
}
message InstInfoRsp {
    // 学校id
    int64 inst_id = 1;
    // 名称
    string name = 2;
    // 名称全名
    string full_name = 3;
}

message InstAppSettingReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 用户id
  int64 user_id = 2 [(validate.rules).int64.gt = 0];
  // 配置id
  int64 setting_id = 3;
  //  参数code
  int32 code = 4;
}
message InstAppSettingRsp {
  // 数据
  repeated InstAppSetting data = 1;
}
message InstAppSetting {
  // id
  int64 id = 1;
  // code 
  int32 code = 2;
  // 值
  string value = 3;
  // 名称
  string name = 4;
}
message UpdateInstAppSettingReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 用户id
  int64 user_id = 2 [(validate.rules).int64.gt = 0];
  // 参数code
  int64 code = 3;
  // 值
  string value = 4;
}

message UserRelationReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 用户id
  int64 user_id = 2 [(validate.rules).int64.gt = 0];
  // 用户类型
  int32 user_role = 3 [(validate.rules).int32 = {in: [1, 2, 3]}];
  // 学生id
  int64 student_id = 4;
}
message UserRelationRsp {
    //返回数据
    UserRelation data = 3;
}
message UserRelation {
    // 关联的班次
    repeated RelationClass relationClass = 14 ;
    // 关联的老师和园长
    repeated RelationTeacher relationTeacher = 15 ;
}
message RelationClass {
    // 班次id
    int64 id = 1 ;
    // 班次名称
    string name = 2 ;
}
message RelationTeacher {
    // 班次id
    int64 id = 1 ;
    // 班次名称
    string name = 2 ;
    // 头像
    string avatar = 3 ;
    // 角色
    int32 roleId = 4 ;
}
