// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.19.1
// source: api/config/v1/sys_config.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSysConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *GetSysConfigReq) Reset() {
	*x = GetSysConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_config_v1_sys_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSysConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSysConfigReq) ProtoMessage() {}

func (x *GetSysConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_config_v1_sys_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSysConfigReq.ProtoReflect.Descriptor instead.
func (*GetSysConfigReq) Descriptor() ([]byte, []int) {
	return file_api_config_v1_sys_config_proto_rawDescGZIP(), []int{0}
}

func (x *GetSysConfigReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type GetSysConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 掌心智校参数值
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// 掌心宝贝参数值
	ValueBaby string `protobuf:"bytes,3,opt,name=value_baby,json=valueBaby,proto3" json:"value_baby,omitempty"`
	// 参数描述
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *GetSysConfigRsp) Reset() {
	*x = GetSysConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_config_v1_sys_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSysConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSysConfigRsp) ProtoMessage() {}

func (x *GetSysConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_config_v1_sys_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSysConfigRsp.ProtoReflect.Descriptor instead.
func (*GetSysConfigRsp) Descriptor() ([]byte, []int) {
	return file_api_config_v1_sys_config_proto_rawDescGZIP(), []int{1}
}

func (x *GetSysConfigRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetSysConfigRsp) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *GetSysConfigRsp) GetValueBaby() string {
	if x != nil {
		return x.ValueBaby
	}
	return ""
}

func (x *GetSysConfigRsp) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

var File_api_config_v1_sys_config_proto protoreflect.FileDescriptor

var file_api_config_v1_sys_config_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x79, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x22, 0x25, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x53, 0x79, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x7c, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x62, 0x61, 0x62, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x61, 0x62, 0x79, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x55, 0x0a, 0x09, 0x53, 0x79, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x48, 0x0a,
	0x0c, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x12, 0x5a, 0x10, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_config_v1_sys_config_proto_rawDescOnce sync.Once
	file_api_config_v1_sys_config_proto_rawDescData = file_api_config_v1_sys_config_proto_rawDesc
)

func file_api_config_v1_sys_config_proto_rawDescGZIP() []byte {
	file_api_config_v1_sys_config_proto_rawDescOnce.Do(func() {
		file_api_config_v1_sys_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_config_v1_sys_config_proto_rawDescData)
	})
	return file_api_config_v1_sys_config_proto_rawDescData
}

var file_api_config_v1_sys_config_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_config_v1_sys_config_proto_goTypes = []interface{}{
	(*GetSysConfigReq)(nil), // 0: config.v1.GetSysConfigReq
	(*GetSysConfigRsp)(nil), // 1: config.v1.GetSysConfigRsp
}
var file_api_config_v1_sys_config_proto_depIdxs = []int32{
	0, // 0: config.v1.SysConfig.GetSysConfig:input_type -> config.v1.GetSysConfigReq
	1, // 1: config.v1.SysConfig.GetSysConfig:output_type -> config.v1.GetSysConfigRsp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_config_v1_sys_config_proto_init() }
func file_api_config_v1_sys_config_proto_init() {
	if File_api_config_v1_sys_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_config_v1_sys_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSysConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_config_v1_sys_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSysConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_config_v1_sys_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_config_v1_sys_config_proto_goTypes,
		DependencyIndexes: file_api_config_v1_sys_config_proto_depIdxs,
		MessageInfos:      file_api_config_v1_sys_config_proto_msgTypes,
	}.Build()
	File_api_config_v1_sys_config_proto = out.File
	file_api_config_v1_sys_config_proto_rawDesc = nil
	file_api_config_v1_sys_config_proto_goTypes = nil
	file_api_config_v1_sys_config_proto_depIdxs = nil
}
