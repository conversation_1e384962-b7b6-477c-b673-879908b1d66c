// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: api/config/v1/sys_config.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SysConfig_GetSysConfig_FullMethodName = "/config.v1.SysConfig/GetSysConfig"
)

// SysConfigClient is the client API for SysConfig service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SysConfigClient interface {
	// 获取系统配置
	GetSysConfig(ctx context.Context, in *GetSysConfigReq, opts ...grpc.CallOption) (*GetSysConfigRsp, error)
}

type sysConfigClient struct {
	cc grpc.ClientConnInterface
}

func NewSysConfigClient(cc grpc.ClientConnInterface) SysConfigClient {
	return &sysConfigClient{cc}
}

func (c *sysConfigClient) GetSysConfig(ctx context.Context, in *GetSysConfigReq, opts ...grpc.CallOption) (*GetSysConfigRsp, error) {
	out := new(GetSysConfigRsp)
	err := c.cc.Invoke(ctx, SysConfig_GetSysConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SysConfigServer is the server API for SysConfig service.
// All implementations must embed UnimplementedSysConfigServer
// for forward compatibility
type SysConfigServer interface {
	// 获取系统配置
	GetSysConfig(context.Context, *GetSysConfigReq) (*GetSysConfigRsp, error)
	mustEmbedUnimplementedSysConfigServer()
}

// UnimplementedSysConfigServer must be embedded to have forward compatible implementations.
type UnimplementedSysConfigServer struct {
}

func (UnimplementedSysConfigServer) GetSysConfig(context.Context, *GetSysConfigReq) (*GetSysConfigRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSysConfig not implemented")
}
func (UnimplementedSysConfigServer) mustEmbedUnimplementedSysConfigServer() {}

// UnsafeSysConfigServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SysConfigServer will
// result in compilation errors.
type UnsafeSysConfigServer interface {
	mustEmbedUnimplementedSysConfigServer()
}

func RegisterSysConfigServer(s grpc.ServiceRegistrar, srv SysConfigServer) {
	s.RegisterService(&SysConfig_ServiceDesc, srv)
}

func _SysConfig_GetSysConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSysConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SysConfigServer).GetSysConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SysConfig_GetSysConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SysConfigServer).GetSysConfig(ctx, req.(*GetSysConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SysConfig_ServiceDesc is the grpc.ServiceDesc for SysConfig service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SysConfig_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "config.v1.SysConfig",
	HandlerType: (*SysConfigServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSysConfig",
			Handler:    _SysConfig_GetSysConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/config/v1/sys_config.proto",
}
