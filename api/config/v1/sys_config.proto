syntax = "proto3";

package config.v1;

option go_package = "api/config/v1;v1";

//生成proto代码
//kratos proto client api/config/v1/sys_config.proto


service SysConfig {
    // 获取系统配置
    rpc GetSysConfig(GetSysConfigReq) returns (GetSysConfigRsp) {}
}

message GetSysConfigReq {
    // code
    int32 code = 1;
}

message GetSysConfigRsp {
    // code
    int32 code = 1;
    // 掌心智校参数值
    string value = 2;
    // 掌心宝贝参数值
    string value_baby = 3;
    // 参数描述
    string description = 4;
}