syntax = "proto3";

package api.file.v1;

import "google/protobuf/empty.proto";
import "validate/validate.proto";

option go_package = "api/file/v1;v1";


// 文件相关服务
service File {
  // 获取导入记录列表
  rpc ListImportRecord(ListImportRecordReq) returns (ListImportRecordRsp) {}

  // 获取导出记录列表
  rpc ListExportRecord(ListExportRecordReq) returns (ListExportRecordRsp) {}

  // 创建导入记录
  rpc CreateImportRecord(CreateImportRecordReq) returns (CreateImportRecordRsp) {}

  // 创建导出记录
  rpc CreateExportRecord(CreateExportRecordReq) returns (CreateExportRecordRsp) {}

  // 更新导入记录状态
  rpc UpdateImportRecordStatus(UpdateImportRecordStatusReq) returns (UpdateImportRecordStatusRsp) {}

  // 更新导出记录状态
  rpc UpdateExportRecordStatus(UpdateExportRecordStatusReq) returns (UpdateExportRecordStatusRsp) {}

  // 处理导入记录
  rpc HandleImportRecord(HandleImportRecordReq) returns (HandleImportRecordRsp) {}

  // 处理导出记录
  rpc HandleExportRecord(HandleExportRecordReq) returns (HandleExportRecordRsp) {}
}

// 获取导入记录列表请求参数
message ListImportRecordReq {
  // 当前页
  uint32 page = 1;
  // 每页条数
  uint32 per_page = 2;
  // 学校id
  int64 inst_id = 3;
  // 角色（1：教职工，3：家长）
  uint32 user_role = 4;
  // 用户id
  int64 user_id = 5;
  // 类型，参见：https://cola0ozpn6.feishu.cn/wiki/Stg8w8xLCi1FHLk0xJmc1I9Hnxc?sheet=3d8452
  string type = 6;
  // 状态（1：待处理，2：处理中，3：成功，4：失败）
  uint32 status = 7;
}

// 获取导入记录列表返回参数
message ListImportRecordRsp {
  // 总条数
  int64 total = 1;
  // 当前页
  uint32 page = 2;
  // 每页条数
  uint32 per_page = 3;
  // 记录列表
  repeated ImportRecord list = 4;
}

// 导入记录信息
message ImportRecord {
  // 记录id
  int64 record_id = 1;
  // 用户id
  int64 user_id = 2;
  // 角色(1-老师，3-家长)
  uint32 user_role = 3;
  // 学生id
  int64 student_id = 4;
  // 导入文件名
  string file_name = 5;
  // 类型
  string type = 6;
  // 导入成功记录数
  uint32 successful_num = 7;
  // 导入失败记录数
  uint32 Failure_num = 8;
  // 原文件url
  string original_file_url = 9;
  // 失败记录文件url
  string failed_file_url = 10;
  // 状态（1：待处理，2：处理中，3：成功，4：失败）
  uint32 status = 11;
  // 失败原因
  string failed_reason = 12;
  // 创建时间
  int64 create_time = 13;
}


// 获取导出记录列表请求参数
message ListExportRecordReq {
  // 当前页
  uint32 page = 1;
  // 每页条数
  uint32 per_page = 2;
  // 学校id
  int64 inst_id = 3;
  // 角色（1：教职工，3：家长）
  uint32 user_role = 4;
  // 用户id
  int64 user_id = 5;
  // 类型，参见：https://cola0ozpn6.feishu.cn/wiki/Stg8w8xLCi1FHLk0xJmc1I9Hnxc?sheet=t8cVN2
  string type = 6;
  // 状态（1：待处理，2：处理中，3：成功，4：失败）
  uint32 status = 7;
}

// 获取导出记录列表返回参数
message ListExportRecordRsp {
  // 总条数
  int64 total = 1;
  // 当前页
  uint32 page = 2;
  // 每页条数
  uint32 per_page = 3;
  // 导出记录列表
  repeated ExportRecord list = 4;
}

// 导出记录信息
message ExportRecord {
  // 记录id
  int64 record_id = 1;
  // 用户id
  int64 user_id = 2;
  // 角色(1-老师，3-家长)
  uint32 user_role = 3;
  // 学生id
  int64 student_id = 4;
  // 所属模块
  string type = 5;
  // 下载url
  string download_url = 6;
  // 状态（1：待处理，2：处理中，3：成功，4：失败）
  uint32 status = 7;
  // 失败原因
  string failed_reason = 8;
  // 创建时间
  int64 create_time = 9;
}

// 创建导入记录请求参数
message CreateImportRecordReq {
  // 用户id
  int64 user_id = 1 [(validate.rules).int64.gt = 0];
  // 角色(1-老师，3-家长)
  uint32 user_role = 2 [(validate.rules).uint32 = {in: [1, 3]}];
  // 学生id
  int64 student_id = 3;
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 导入文件名
  string file_name = 5 [(validate.rules).string.min_len = 1];
  // 类型
  string type = 6 [(validate.rules).string = {min_len: 1, max_len: 50}];
  // 原文件url
  string original_file_url = 7 [(validate.rules).string.min_len = 10];
  // ip
  string ip = 8  [(validate.rules).string.max_len = 20];
  // 渠道（1：管理后台，2：园丁端，3：家长端）
  uint32 channel = 9  [(validate.rules).uint32 = {in: [1, 2, 3]}];
}

// 创建导入记录返回参数
message CreateImportRecordRsp {

}

// 创建导出记录请求参数
message CreateExportRecordReq {
  // 用户id
  int64 user_id = 1 [(validate.rules).int64.gt = 0];
  // 角色(1-老师，3-家长)
  uint32 user_role = 2 [(validate.rules).uint32 = {in: [1, 3]}];
  // 学生id
  int64 student_id = 3;
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 类型
  string type = 5 [(validate.rules).string = {min_len: 1, max_len: 50}];
  // 请求查询参数
  string param = 6 [(validate.rules).string.min_len = 1];
  // ip
  string ip = 7  [(validate.rules).string.max_len = 20];
  // 渠道（1：管理后台，2：园丁端，3：家长端）
  uint32 channel = 8  [(validate.rules).uint32 = {in: [1, 2, 3]}];
}

// 创建导出记录返回参数
message CreateExportRecordRsp {

}

// 更新导入记录状态请求参数
message UpdateImportRecordStatusReq {
  // 记录id
  int64 record_id = 1 [(validate.rules).int64.gt = 0];
  // 状态（1：成功，2：失败）
  uint32 status = 2 [(validate.rules).uint32 = {in: [1,2]}];
  // 成功数量
  uint32 successful_num = 3;
  // 失败数量
  uint32 failed_num = 4;
  // 失败记录文件url
  string failed_file_url = 5;
  // 失败原因
  string failed_reason = 6;
}

// 更新导入记录状态返回参数
message UpdateImportRecordStatusRsp {

}

// 更新导出记录状态请求参数
message UpdateExportRecordStatusReq {
  // 记录id
  int64 record_id = 1 [(validate.rules).int64.gt = 0];
  // 状态（1：成功，2：失败）
  uint32 status = 2 [(validate.rules).uint32 = {in: [1,2]}];
  // 失败原因
  string failed_reason = 3;
  // 下载url
  string download_url = 4;
}

// 更新导出记录状态返回参数
message UpdateExportRecordStatusRsp {

}

// 处理导入记录请求参数
message HandleImportRecordReq {
}

// 处理导出记录返回参数
message HandleImportRecordRsp {
}

// 处理导出记录请求参数
message HandleExportRecordReq {
}

// 处理导出记录返回参数
message HandleExportRecordRsp {
}