// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/file/v1/file.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListImportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImportRecordReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImportRecordReqMultiError, or nil if none found.
func (m *ListImportRecordReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImportRecordReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PerPage

	// no validation rules for InstId

	// no validation rules for UserRole

	// no validation rules for UserId

	// no validation rules for Type

	// no validation rules for Status

	if len(errors) > 0 {
		return ListImportRecordReqMultiError(errors)
	}

	return nil
}

// ListImportRecordReqMultiError is an error wrapping multiple validation
// errors returned by ListImportRecordReq.ValidateAll() if the designated
// constraints aren't met.
type ListImportRecordReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImportRecordReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImportRecordReqMultiError) AllErrors() []error { return m }

// ListImportRecordReqValidationError is the validation error returned by
// ListImportRecordReq.Validate if the designated constraints aren't met.
type ListImportRecordReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImportRecordReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImportRecordReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImportRecordReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImportRecordReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImportRecordReqValidationError) ErrorName() string {
	return "ListImportRecordReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListImportRecordReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImportRecordReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImportRecordReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImportRecordReqValidationError{}

// Validate checks the field values on ListImportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListImportRecordRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListImportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListImportRecordRspMultiError, or nil if none found.
func (m *ListImportRecordRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListImportRecordRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	// no validation rules for Page

	// no validation rules for PerPage

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListImportRecordRspValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListImportRecordRspValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListImportRecordRspValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListImportRecordRspMultiError(errors)
	}

	return nil
}

// ListImportRecordRspMultiError is an error wrapping multiple validation
// errors returned by ListImportRecordRsp.ValidateAll() if the designated
// constraints aren't met.
type ListImportRecordRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListImportRecordRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListImportRecordRspMultiError) AllErrors() []error { return m }

// ListImportRecordRspValidationError is the validation error returned by
// ListImportRecordRsp.Validate if the designated constraints aren't met.
type ListImportRecordRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListImportRecordRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListImportRecordRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListImportRecordRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListImportRecordRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListImportRecordRspValidationError) ErrorName() string {
	return "ListImportRecordRspValidationError"
}

// Error satisfies the builtin error interface
func (e ListImportRecordRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListImportRecordRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListImportRecordRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListImportRecordRspValidationError{}

// Validate checks the field values on ImportRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImportRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImportRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImportRecordMultiError, or
// nil if none found.
func (m *ImportRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *ImportRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecordId

	// no validation rules for UserId

	// no validation rules for UserRole

	// no validation rules for StudentId

	// no validation rules for FileName

	// no validation rules for Type

	// no validation rules for SuccessfulNum

	// no validation rules for FailureNum

	// no validation rules for OriginalFileUrl

	// no validation rules for FailedFileUrl

	// no validation rules for Status

	// no validation rules for FailedReason

	// no validation rules for CreateTime

	if len(errors) > 0 {
		return ImportRecordMultiError(errors)
	}

	return nil
}

// ImportRecordMultiError is an error wrapping multiple validation errors
// returned by ImportRecord.ValidateAll() if the designated constraints aren't met.
type ImportRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImportRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImportRecordMultiError) AllErrors() []error { return m }

// ImportRecordValidationError is the validation error returned by
// ImportRecord.Validate if the designated constraints aren't met.
type ImportRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImportRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImportRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImportRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImportRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImportRecordValidationError) ErrorName() string { return "ImportRecordValidationError" }

// Error satisfies the builtin error interface
func (e ImportRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImportRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImportRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImportRecordValidationError{}

// Validate checks the field values on ListExportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExportRecordReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExportRecordReqMultiError, or nil if none found.
func (m *ListExportRecordReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExportRecordReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PerPage

	// no validation rules for InstId

	// no validation rules for UserRole

	// no validation rules for UserId

	// no validation rules for Type

	// no validation rules for Status

	if len(errors) > 0 {
		return ListExportRecordReqMultiError(errors)
	}

	return nil
}

// ListExportRecordReqMultiError is an error wrapping multiple validation
// errors returned by ListExportRecordReq.ValidateAll() if the designated
// constraints aren't met.
type ListExportRecordReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExportRecordReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExportRecordReqMultiError) AllErrors() []error { return m }

// ListExportRecordReqValidationError is the validation error returned by
// ListExportRecordReq.Validate if the designated constraints aren't met.
type ListExportRecordReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExportRecordReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExportRecordReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExportRecordReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExportRecordReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExportRecordReqValidationError) ErrorName() string {
	return "ListExportRecordReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListExportRecordReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExportRecordReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExportRecordReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExportRecordReqValidationError{}

// Validate checks the field values on ListExportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExportRecordRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExportRecordRspMultiError, or nil if none found.
func (m *ListExportRecordRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExportRecordRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	// no validation rules for Page

	// no validation rules for PerPage

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListExportRecordRspValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListExportRecordRspValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListExportRecordRspValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListExportRecordRspMultiError(errors)
	}

	return nil
}

// ListExportRecordRspMultiError is an error wrapping multiple validation
// errors returned by ListExportRecordRsp.ValidateAll() if the designated
// constraints aren't met.
type ListExportRecordRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExportRecordRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExportRecordRspMultiError) AllErrors() []error { return m }

// ListExportRecordRspValidationError is the validation error returned by
// ListExportRecordRsp.Validate if the designated constraints aren't met.
type ListExportRecordRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExportRecordRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExportRecordRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExportRecordRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExportRecordRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExportRecordRspValidationError) ErrorName() string {
	return "ListExportRecordRspValidationError"
}

// Error satisfies the builtin error interface
func (e ListExportRecordRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExportRecordRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExportRecordRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExportRecordRspValidationError{}

// Validate checks the field values on ExportRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExportRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExportRecordMultiError, or
// nil if none found.
func (m *ExportRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecordId

	// no validation rules for UserId

	// no validation rules for UserRole

	// no validation rules for StudentId

	// no validation rules for Type

	// no validation rules for DownloadUrl

	// no validation rules for Status

	// no validation rules for FailedReason

	// no validation rules for CreateTime

	if len(errors) > 0 {
		return ExportRecordMultiError(errors)
	}

	return nil
}

// ExportRecordMultiError is an error wrapping multiple validation errors
// returned by ExportRecord.ValidateAll() if the designated constraints aren't met.
type ExportRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportRecordMultiError) AllErrors() []error { return m }

// ExportRecordValidationError is the validation error returned by
// ExportRecord.Validate if the designated constraints aren't met.
type ExportRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportRecordValidationError) ErrorName() string { return "ExportRecordValidationError" }

// Error satisfies the builtin error interface
func (e ExportRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportRecordValidationError{}

// Validate checks the field values on CreateImportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateImportRecordReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateImportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateImportRecordReqMultiError, or nil if none found.
func (m *CreateImportRecordReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateImportRecordReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := CreateImportRecordReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateImportRecordReq_UserRole_InLookup[m.GetUserRole()]; !ok {
		err := CreateImportRecordReqValidationError{
			field:  "UserRole",
			reason: "value must be in list [1 3]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for StudentId

	if m.GetInstId() <= 0 {
		err := CreateImportRecordReqValidationError{
			field:  "InstId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFileName()) < 1 {
		err := CreateImportRecordReqValidationError{
			field:  "FileName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetType()); l < 1 || l > 50 {
		err := CreateImportRecordReqValidationError{
			field:  "Type",
			reason: "value length must be between 1 and 50 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOriginalFileUrl()) < 10 {
		err := CreateImportRecordReqValidationError{
			field:  "OriginalFileUrl",
			reason: "value length must be at least 10 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIp()) > 20 {
		err := CreateImportRecordReqValidationError{
			field:  "Ip",
			reason: "value length must be at most 20 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateImportRecordReq_Channel_InLookup[m.GetChannel()]; !ok {
		err := CreateImportRecordReqValidationError{
			field:  "Channel",
			reason: "value must be in list [1 2 3]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateImportRecordReqMultiError(errors)
	}

	return nil
}

// CreateImportRecordReqMultiError is an error wrapping multiple validation
// errors returned by CreateImportRecordReq.ValidateAll() if the designated
// constraints aren't met.
type CreateImportRecordReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateImportRecordReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateImportRecordReqMultiError) AllErrors() []error { return m }

// CreateImportRecordReqValidationError is the validation error returned by
// CreateImportRecordReq.Validate if the designated constraints aren't met.
type CreateImportRecordReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateImportRecordReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateImportRecordReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateImportRecordReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateImportRecordReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateImportRecordReqValidationError) ErrorName() string {
	return "CreateImportRecordReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateImportRecordReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateImportRecordReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateImportRecordReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateImportRecordReqValidationError{}

var _CreateImportRecordReq_UserRole_InLookup = map[uint32]struct{}{
	1: {},
	3: {},
}

var _CreateImportRecordReq_Channel_InLookup = map[uint32]struct{}{
	1: {},
	2: {},
	3: {},
}

// Validate checks the field values on CreateImportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateImportRecordRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateImportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateImportRecordRspMultiError, or nil if none found.
func (m *CreateImportRecordRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateImportRecordRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateImportRecordRspMultiError(errors)
	}

	return nil
}

// CreateImportRecordRspMultiError is an error wrapping multiple validation
// errors returned by CreateImportRecordRsp.ValidateAll() if the designated
// constraints aren't met.
type CreateImportRecordRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateImportRecordRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateImportRecordRspMultiError) AllErrors() []error { return m }

// CreateImportRecordRspValidationError is the validation error returned by
// CreateImportRecordRsp.Validate if the designated constraints aren't met.
type CreateImportRecordRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateImportRecordRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateImportRecordRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateImportRecordRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateImportRecordRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateImportRecordRspValidationError) ErrorName() string {
	return "CreateImportRecordRspValidationError"
}

// Error satisfies the builtin error interface
func (e CreateImportRecordRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateImportRecordRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateImportRecordRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateImportRecordRspValidationError{}

// Validate checks the field values on CreateExportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateExportRecordReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateExportRecordReqMultiError, or nil if none found.
func (m *CreateExportRecordReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExportRecordReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := CreateExportRecordReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateExportRecordReq_UserRole_InLookup[m.GetUserRole()]; !ok {
		err := CreateExportRecordReqValidationError{
			field:  "UserRole",
			reason: "value must be in list [1 3]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for StudentId

	if m.GetInstId() <= 0 {
		err := CreateExportRecordReqValidationError{
			field:  "InstId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetType()); l < 1 || l > 50 {
		err := CreateExportRecordReqValidationError{
			field:  "Type",
			reason: "value length must be between 1 and 50 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetParam()) < 1 {
		err := CreateExportRecordReqValidationError{
			field:  "Param",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIp()) > 20 {
		err := CreateExportRecordReqValidationError{
			field:  "Ip",
			reason: "value length must be at most 20 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateExportRecordReq_Channel_InLookup[m.GetChannel()]; !ok {
		err := CreateExportRecordReqValidationError{
			field:  "Channel",
			reason: "value must be in list [1 2 3]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateExportRecordReqMultiError(errors)
	}

	return nil
}

// CreateExportRecordReqMultiError is an error wrapping multiple validation
// errors returned by CreateExportRecordReq.ValidateAll() if the designated
// constraints aren't met.
type CreateExportRecordReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExportRecordReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExportRecordReqMultiError) AllErrors() []error { return m }

// CreateExportRecordReqValidationError is the validation error returned by
// CreateExportRecordReq.Validate if the designated constraints aren't met.
type CreateExportRecordReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExportRecordReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExportRecordReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExportRecordReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExportRecordReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExportRecordReqValidationError) ErrorName() string {
	return "CreateExportRecordReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExportRecordReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExportRecordReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExportRecordReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExportRecordReqValidationError{}

var _CreateExportRecordReq_UserRole_InLookup = map[uint32]struct{}{
	1: {},
	3: {},
}

var _CreateExportRecordReq_Channel_InLookup = map[uint32]struct{}{
	1: {},
	2: {},
	3: {},
}

// Validate checks the field values on CreateExportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateExportRecordRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateExportRecordRspMultiError, or nil if none found.
func (m *CreateExportRecordRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExportRecordRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateExportRecordRspMultiError(errors)
	}

	return nil
}

// CreateExportRecordRspMultiError is an error wrapping multiple validation
// errors returned by CreateExportRecordRsp.ValidateAll() if the designated
// constraints aren't met.
type CreateExportRecordRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExportRecordRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExportRecordRspMultiError) AllErrors() []error { return m }

// CreateExportRecordRspValidationError is the validation error returned by
// CreateExportRecordRsp.Validate if the designated constraints aren't met.
type CreateExportRecordRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExportRecordRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExportRecordRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExportRecordRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExportRecordRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExportRecordRspValidationError) ErrorName() string {
	return "CreateExportRecordRspValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExportRecordRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExportRecordRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExportRecordRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExportRecordRspValidationError{}

// Validate checks the field values on UpdateImportRecordStatusReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateImportRecordStatusReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateImportRecordStatusReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateImportRecordStatusReqMultiError, or nil if none found.
func (m *UpdateImportRecordStatusReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateImportRecordStatusReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRecordId() <= 0 {
		err := UpdateImportRecordStatusReqValidationError{
			field:  "RecordId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UpdateImportRecordStatusReq_Status_InLookup[m.GetStatus()]; !ok {
		err := UpdateImportRecordStatusReqValidationError{
			field:  "Status",
			reason: "value must be in list [1 2]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SuccessfulNum

	// no validation rules for FailedNum

	// no validation rules for FailedFileUrl

	// no validation rules for FailedReason

	if len(errors) > 0 {
		return UpdateImportRecordStatusReqMultiError(errors)
	}

	return nil
}

// UpdateImportRecordStatusReqMultiError is an error wrapping multiple
// validation errors returned by UpdateImportRecordStatusReq.ValidateAll() if
// the designated constraints aren't met.
type UpdateImportRecordStatusReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateImportRecordStatusReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateImportRecordStatusReqMultiError) AllErrors() []error { return m }

// UpdateImportRecordStatusReqValidationError is the validation error returned
// by UpdateImportRecordStatusReq.Validate if the designated constraints
// aren't met.
type UpdateImportRecordStatusReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateImportRecordStatusReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateImportRecordStatusReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateImportRecordStatusReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateImportRecordStatusReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateImportRecordStatusReqValidationError) ErrorName() string {
	return "UpdateImportRecordStatusReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateImportRecordStatusReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateImportRecordStatusReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateImportRecordStatusReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateImportRecordStatusReqValidationError{}

var _UpdateImportRecordStatusReq_Status_InLookup = map[uint32]struct{}{
	1: {},
	2: {},
}

// Validate checks the field values on UpdateImportRecordStatusRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateImportRecordStatusRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateImportRecordStatusRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateImportRecordStatusRspMultiError, or nil if none found.
func (m *UpdateImportRecordStatusRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateImportRecordStatusRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateImportRecordStatusRspMultiError(errors)
	}

	return nil
}

// UpdateImportRecordStatusRspMultiError is an error wrapping multiple
// validation errors returned by UpdateImportRecordStatusRsp.ValidateAll() if
// the designated constraints aren't met.
type UpdateImportRecordStatusRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateImportRecordStatusRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateImportRecordStatusRspMultiError) AllErrors() []error { return m }

// UpdateImportRecordStatusRspValidationError is the validation error returned
// by UpdateImportRecordStatusRsp.Validate if the designated constraints
// aren't met.
type UpdateImportRecordStatusRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateImportRecordStatusRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateImportRecordStatusRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateImportRecordStatusRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateImportRecordStatusRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateImportRecordStatusRspValidationError) ErrorName() string {
	return "UpdateImportRecordStatusRspValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateImportRecordStatusRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateImportRecordStatusRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateImportRecordStatusRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateImportRecordStatusRspValidationError{}

// Validate checks the field values on UpdateExportRecordStatusReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateExportRecordStatusReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateExportRecordStatusReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateExportRecordStatusReqMultiError, or nil if none found.
func (m *UpdateExportRecordStatusReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateExportRecordStatusReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRecordId() <= 0 {
		err := UpdateExportRecordStatusReqValidationError{
			field:  "RecordId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UpdateExportRecordStatusReq_Status_InLookup[m.GetStatus()]; !ok {
		err := UpdateExportRecordStatusReqValidationError{
			field:  "Status",
			reason: "value must be in list [1 2]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FailedReason

	// no validation rules for DownloadUrl

	if len(errors) > 0 {
		return UpdateExportRecordStatusReqMultiError(errors)
	}

	return nil
}

// UpdateExportRecordStatusReqMultiError is an error wrapping multiple
// validation errors returned by UpdateExportRecordStatusReq.ValidateAll() if
// the designated constraints aren't met.
type UpdateExportRecordStatusReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateExportRecordStatusReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateExportRecordStatusReqMultiError) AllErrors() []error { return m }

// UpdateExportRecordStatusReqValidationError is the validation error returned
// by UpdateExportRecordStatusReq.Validate if the designated constraints
// aren't met.
type UpdateExportRecordStatusReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateExportRecordStatusReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateExportRecordStatusReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateExportRecordStatusReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateExportRecordStatusReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateExportRecordStatusReqValidationError) ErrorName() string {
	return "UpdateExportRecordStatusReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateExportRecordStatusReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateExportRecordStatusReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateExportRecordStatusReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateExportRecordStatusReqValidationError{}

var _UpdateExportRecordStatusReq_Status_InLookup = map[uint32]struct{}{
	1: {},
	2: {},
}

// Validate checks the field values on UpdateExportRecordStatusRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateExportRecordStatusRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateExportRecordStatusRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateExportRecordStatusRspMultiError, or nil if none found.
func (m *UpdateExportRecordStatusRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateExportRecordStatusRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateExportRecordStatusRspMultiError(errors)
	}

	return nil
}

// UpdateExportRecordStatusRspMultiError is an error wrapping multiple
// validation errors returned by UpdateExportRecordStatusRsp.ValidateAll() if
// the designated constraints aren't met.
type UpdateExportRecordStatusRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateExportRecordStatusRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateExportRecordStatusRspMultiError) AllErrors() []error { return m }

// UpdateExportRecordStatusRspValidationError is the validation error returned
// by UpdateExportRecordStatusRsp.Validate if the designated constraints
// aren't met.
type UpdateExportRecordStatusRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateExportRecordStatusRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateExportRecordStatusRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateExportRecordStatusRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateExportRecordStatusRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateExportRecordStatusRspValidationError) ErrorName() string {
	return "UpdateExportRecordStatusRspValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateExportRecordStatusRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateExportRecordStatusRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateExportRecordStatusRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateExportRecordStatusRspValidationError{}

// Validate checks the field values on HandleImportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleImportRecordReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleImportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleImportRecordReqMultiError, or nil if none found.
func (m *HandleImportRecordReq) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleImportRecordReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HandleImportRecordReqMultiError(errors)
	}

	return nil
}

// HandleImportRecordReqMultiError is an error wrapping multiple validation
// errors returned by HandleImportRecordReq.ValidateAll() if the designated
// constraints aren't met.
type HandleImportRecordReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleImportRecordReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleImportRecordReqMultiError) AllErrors() []error { return m }

// HandleImportRecordReqValidationError is the validation error returned by
// HandleImportRecordReq.Validate if the designated constraints aren't met.
type HandleImportRecordReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleImportRecordReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleImportRecordReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleImportRecordReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleImportRecordReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleImportRecordReqValidationError) ErrorName() string {
	return "HandleImportRecordReqValidationError"
}

// Error satisfies the builtin error interface
func (e HandleImportRecordReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleImportRecordReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleImportRecordReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleImportRecordReqValidationError{}

// Validate checks the field values on HandleImportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleImportRecordRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleImportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleImportRecordRspMultiError, or nil if none found.
func (m *HandleImportRecordRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleImportRecordRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HandleImportRecordRspMultiError(errors)
	}

	return nil
}

// HandleImportRecordRspMultiError is an error wrapping multiple validation
// errors returned by HandleImportRecordRsp.ValidateAll() if the designated
// constraints aren't met.
type HandleImportRecordRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleImportRecordRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleImportRecordRspMultiError) AllErrors() []error { return m }

// HandleImportRecordRspValidationError is the validation error returned by
// HandleImportRecordRsp.Validate if the designated constraints aren't met.
type HandleImportRecordRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleImportRecordRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleImportRecordRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleImportRecordRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleImportRecordRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleImportRecordRspValidationError) ErrorName() string {
	return "HandleImportRecordRspValidationError"
}

// Error satisfies the builtin error interface
func (e HandleImportRecordRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleImportRecordRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleImportRecordRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleImportRecordRspValidationError{}

// Validate checks the field values on HandleExportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleExportRecordReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleExportRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleExportRecordReqMultiError, or nil if none found.
func (m *HandleExportRecordReq) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleExportRecordReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HandleExportRecordReqMultiError(errors)
	}

	return nil
}

// HandleExportRecordReqMultiError is an error wrapping multiple validation
// errors returned by HandleExportRecordReq.ValidateAll() if the designated
// constraints aren't met.
type HandleExportRecordReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleExportRecordReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleExportRecordReqMultiError) AllErrors() []error { return m }

// HandleExportRecordReqValidationError is the validation error returned by
// HandleExportRecordReq.Validate if the designated constraints aren't met.
type HandleExportRecordReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleExportRecordReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleExportRecordReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleExportRecordReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleExportRecordReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleExportRecordReqValidationError) ErrorName() string {
	return "HandleExportRecordReqValidationError"
}

// Error satisfies the builtin error interface
func (e HandleExportRecordReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleExportRecordReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleExportRecordReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleExportRecordReqValidationError{}

// Validate checks the field values on HandleExportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleExportRecordRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleExportRecordRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleExportRecordRspMultiError, or nil if none found.
func (m *HandleExportRecordRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleExportRecordRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HandleExportRecordRspMultiError(errors)
	}

	return nil
}

// HandleExportRecordRspMultiError is an error wrapping multiple validation
// errors returned by HandleExportRecordRsp.ValidateAll() if the designated
// constraints aren't met.
type HandleExportRecordRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleExportRecordRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleExportRecordRspMultiError) AllErrors() []error { return m }

// HandleExportRecordRspValidationError is the validation error returned by
// HandleExportRecordRsp.Validate if the designated constraints aren't met.
type HandleExportRecordRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleExportRecordRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleExportRecordRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleExportRecordRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleExportRecordRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleExportRecordRspValidationError) ErrorName() string {
	return "HandleExportRecordRspValidationError"
}

// Error satisfies the builtin error interface
func (e HandleExportRecordRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleExportRecordRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleExportRecordRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleExportRecordRspValidationError{}
