// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.17.3
// source: file/v1/file.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取导入记录列表请求参数
type ListImportRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页
	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 角色（1：教职工，3：家长）
	UserRole uint32 `protobuf:"varint,4,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 类型，参见：https://cola0ozpn6.feishu.cn/wiki/Stg8w8xLCi1FHLk0xJmc1I9Hnxc?sheet=3d8452
	Type string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	// 状态（1：待处理，2：处理中，3：成功，4：失败）
	Status uint32 `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ListImportRecordReq) Reset() {
	*x = ListImportRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImportRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImportRecordReq) ProtoMessage() {}

func (x *ListImportRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImportRecordReq.ProtoReflect.Descriptor instead.
func (*ListImportRecordReq) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{0}
}

func (x *ListImportRecordReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListImportRecordReq) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListImportRecordReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListImportRecordReq) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *ListImportRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ListImportRecordReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ListImportRecordReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 获取导入记录列表返回参数
type ListImportRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 当前页
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,3,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 记录列表
	List []*ImportRecord `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListImportRecordRsp) Reset() {
	*x = ListImportRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListImportRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImportRecordRsp) ProtoMessage() {}

func (x *ListImportRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImportRecordRsp.ProtoReflect.Descriptor instead.
func (*ListImportRecordRsp) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{1}
}

func (x *ListImportRecordRsp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListImportRecordRsp) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListImportRecordRsp) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListImportRecordRsp) GetList() []*ImportRecord {
	if x != nil {
		return x.List
	}
	return nil
}

// 导入记录信息
type ImportRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录id
	RecordId int64 `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 角色(1-老师，3-家长)
	UserRole uint32 `protobuf:"varint,3,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,4,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 导入文件名
	FileName string `protobuf:"bytes,5,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// 类型
	Type string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	// 导入成功记录数
	SuccessfulNum uint32 `protobuf:"varint,7,opt,name=successful_num,json=successfulNum,proto3" json:"successful_num,omitempty"`
	// 导入失败记录数
	FailureNum uint32 `protobuf:"varint,8,opt,name=Failure_num,json=FailureNum,proto3" json:"Failure_num,omitempty"`
	// 原文件url
	OriginalFileUrl string `protobuf:"bytes,9,opt,name=original_file_url,json=originalFileUrl,proto3" json:"original_file_url,omitempty"`
	// 失败记录文件url
	FailedFileUrl string `protobuf:"bytes,10,opt,name=failed_file_url,json=failedFileUrl,proto3" json:"failed_file_url,omitempty"`
	// 状态（1：待处理，2：处理中，3：成功，4：失败）
	Status uint32 `protobuf:"varint,11,opt,name=status,proto3" json:"status,omitempty"`
	// 失败原因
	FailedReason string `protobuf:"bytes,12,opt,name=failed_reason,json=failedReason,proto3" json:"failed_reason,omitempty"`
	// 创建时间
	CreateTime int64 `protobuf:"varint,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *ImportRecord) Reset() {
	*x = ImportRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportRecord) ProtoMessage() {}

func (x *ImportRecord) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportRecord.ProtoReflect.Descriptor instead.
func (*ImportRecord) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{2}
}

func (x *ImportRecord) GetRecordId() int64 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *ImportRecord) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ImportRecord) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *ImportRecord) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *ImportRecord) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ImportRecord) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ImportRecord) GetSuccessfulNum() uint32 {
	if x != nil {
		return x.SuccessfulNum
	}
	return 0
}

func (x *ImportRecord) GetFailureNum() uint32 {
	if x != nil {
		return x.FailureNum
	}
	return 0
}

func (x *ImportRecord) GetOriginalFileUrl() string {
	if x != nil {
		return x.OriginalFileUrl
	}
	return ""
}

func (x *ImportRecord) GetFailedFileUrl() string {
	if x != nil {
		return x.FailedFileUrl
	}
	return ""
}

func (x *ImportRecord) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ImportRecord) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

func (x *ImportRecord) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

// 获取导出记录列表请求参数
type ListExportRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页
	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 角色（1：教职工，3：家长）
	UserRole uint32 `protobuf:"varint,4,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 类型，参见：https://cola0ozpn6.feishu.cn/wiki/Stg8w8xLCi1FHLk0xJmc1I9Hnxc?sheet=t8cVN2
	Type string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	// 状态（1：待处理，2：处理中，3：成功，4：失败）
	Status uint32 `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ListExportRecordReq) Reset() {
	*x = ListExportRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExportRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExportRecordReq) ProtoMessage() {}

func (x *ListExportRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExportRecordReq.ProtoReflect.Descriptor instead.
func (*ListExportRecordReq) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{3}
}

func (x *ListExportRecordReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListExportRecordReq) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListExportRecordReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListExportRecordReq) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *ListExportRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ListExportRecordReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ListExportRecordReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 获取导出记录列表返回参数
type ListExportRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 当前页
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,3,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 导出记录列表
	List []*ExportRecord `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListExportRecordRsp) Reset() {
	*x = ListExportRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExportRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExportRecordRsp) ProtoMessage() {}

func (x *ListExportRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExportRecordRsp.ProtoReflect.Descriptor instead.
func (*ListExportRecordRsp) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{4}
}

func (x *ListExportRecordRsp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListExportRecordRsp) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListExportRecordRsp) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListExportRecordRsp) GetList() []*ExportRecord {
	if x != nil {
		return x.List
	}
	return nil
}

// 导出记录信息
type ExportRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录id
	RecordId int64 `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 角色(1-老师，3-家长)
	UserRole uint32 `protobuf:"varint,3,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,4,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 所属模块
	Type string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	// 下载url
	DownloadUrl string `protobuf:"bytes,6,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	// 状态（1：待处理，2：处理中，3：成功，4：失败）
	Status uint32 `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	// 失败原因
	FailedReason string `protobuf:"bytes,8,opt,name=failed_reason,json=failedReason,proto3" json:"failed_reason,omitempty"`
	// 创建时间
	CreateTime int64 `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *ExportRecord) Reset() {
	*x = ExportRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportRecord) ProtoMessage() {}

func (x *ExportRecord) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportRecord.ProtoReflect.Descriptor instead.
func (*ExportRecord) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{5}
}

func (x *ExportRecord) GetRecordId() int64 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *ExportRecord) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ExportRecord) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *ExportRecord) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *ExportRecord) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ExportRecord) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *ExportRecord) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ExportRecord) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

func (x *ExportRecord) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

// 创建导入记录请求参数
type CreateImportRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 角色(1-老师，3-家长)
	UserRole uint32 `protobuf:"varint,2,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,3,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 导入文件名
	FileName string `protobuf:"bytes,5,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// 类型
	Type string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	// 原文件url
	OriginalFileUrl string `protobuf:"bytes,7,opt,name=original_file_url,json=originalFileUrl,proto3" json:"original_file_url,omitempty"`
	// ip
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
	// 渠道（1：管理后台，2：园丁端，3：家长端）
	Channel uint32 `protobuf:"varint,9,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *CreateImportRecordReq) Reset() {
	*x = CreateImportRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateImportRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateImportRecordReq) ProtoMessage() {}

func (x *CreateImportRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateImportRecordReq.ProtoReflect.Descriptor instead.
func (*CreateImportRecordReq) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{6}
}

func (x *CreateImportRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreateImportRecordReq) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *CreateImportRecordReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *CreateImportRecordReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *CreateImportRecordReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *CreateImportRecordReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateImportRecordReq) GetOriginalFileUrl() string {
	if x != nil {
		return x.OriginalFileUrl
	}
	return ""
}

func (x *CreateImportRecordReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CreateImportRecordReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

// 创建导入记录返回参数
type CreateImportRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateImportRecordRsp) Reset() {
	*x = CreateImportRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateImportRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateImportRecordRsp) ProtoMessage() {}

func (x *CreateImportRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateImportRecordRsp.ProtoReflect.Descriptor instead.
func (*CreateImportRecordRsp) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{7}
}

// 创建导出记录请求参数
type CreateExportRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 角色(1-老师，3-家长)
	UserRole uint32 `protobuf:"varint,2,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,3,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 类型
	Type string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	// 请求查询参数
	Param string `protobuf:"bytes,6,opt,name=param,proto3" json:"param,omitempty"`
	// ip
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
	// 渠道（1：管理后台，2：园丁端，3：家长端）
	Channel uint32 `protobuf:"varint,8,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *CreateExportRecordReq) Reset() {
	*x = CreateExportRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExportRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExportRecordReq) ProtoMessage() {}

func (x *CreateExportRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExportRecordReq.ProtoReflect.Descriptor instead.
func (*CreateExportRecordReq) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{8}
}

func (x *CreateExportRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreateExportRecordReq) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *CreateExportRecordReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *CreateExportRecordReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *CreateExportRecordReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateExportRecordReq) GetParam() string {
	if x != nil {
		return x.Param
	}
	return ""
}

func (x *CreateExportRecordReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CreateExportRecordReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

// 创建导出记录返回参数
type CreateExportRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateExportRecordRsp) Reset() {
	*x = CreateExportRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExportRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExportRecordRsp) ProtoMessage() {}

func (x *CreateExportRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExportRecordRsp.ProtoReflect.Descriptor instead.
func (*CreateExportRecordRsp) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{9}
}

// 更新导入记录状态请求参数
type UpdateImportRecordStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录id
	RecordId int64 `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	// 状态（1：成功，2：失败）
	Status uint32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	// 成功数量
	SuccessfulNum uint32 `protobuf:"varint,3,opt,name=successful_num,json=successfulNum,proto3" json:"successful_num,omitempty"`
	// 失败数量
	FailedNum uint32 `protobuf:"varint,4,opt,name=failed_num,json=failedNum,proto3" json:"failed_num,omitempty"`
	// 失败记录文件url
	FailedFileUrl string `protobuf:"bytes,5,opt,name=failed_file_url,json=failedFileUrl,proto3" json:"failed_file_url,omitempty"`
	// 失败原因
	FailedReason string `protobuf:"bytes,6,opt,name=failed_reason,json=failedReason,proto3" json:"failed_reason,omitempty"`
}

func (x *UpdateImportRecordStatusReq) Reset() {
	*x = UpdateImportRecordStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateImportRecordStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateImportRecordStatusReq) ProtoMessage() {}

func (x *UpdateImportRecordStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateImportRecordStatusReq.ProtoReflect.Descriptor instead.
func (*UpdateImportRecordStatusReq) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateImportRecordStatusReq) GetRecordId() int64 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *UpdateImportRecordStatusReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateImportRecordStatusReq) GetSuccessfulNum() uint32 {
	if x != nil {
		return x.SuccessfulNum
	}
	return 0
}

func (x *UpdateImportRecordStatusReq) GetFailedNum() uint32 {
	if x != nil {
		return x.FailedNum
	}
	return 0
}

func (x *UpdateImportRecordStatusReq) GetFailedFileUrl() string {
	if x != nil {
		return x.FailedFileUrl
	}
	return ""
}

func (x *UpdateImportRecordStatusReq) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

// 更新导入记录状态返回参数
type UpdateImportRecordStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateImportRecordStatusRsp) Reset() {
	*x = UpdateImportRecordStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateImportRecordStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateImportRecordStatusRsp) ProtoMessage() {}

func (x *UpdateImportRecordStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateImportRecordStatusRsp.ProtoReflect.Descriptor instead.
func (*UpdateImportRecordStatusRsp) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{11}
}

// 更新导出记录状态请求参数
type UpdateExportRecordStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 记录id
	RecordId int64 `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	// 状态（1：成功，2：失败）
	Status uint32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	// 失败原因
	FailedReason string `protobuf:"bytes,3,opt,name=failed_reason,json=failedReason,proto3" json:"failed_reason,omitempty"`
	// 下载url
	DownloadUrl string `protobuf:"bytes,4,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
}

func (x *UpdateExportRecordStatusReq) Reset() {
	*x = UpdateExportRecordStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExportRecordStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExportRecordStatusReq) ProtoMessage() {}

func (x *UpdateExportRecordStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExportRecordStatusReq.ProtoReflect.Descriptor instead.
func (*UpdateExportRecordStatusReq) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateExportRecordStatusReq) GetRecordId() int64 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *UpdateExportRecordStatusReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateExportRecordStatusReq) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

func (x *UpdateExportRecordStatusReq) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

// 更新导出记录状态返回参数
type UpdateExportRecordStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateExportRecordStatusRsp) Reset() {
	*x = UpdateExportRecordStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExportRecordStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExportRecordStatusRsp) ProtoMessage() {}

func (x *UpdateExportRecordStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExportRecordStatusRsp.ProtoReflect.Descriptor instead.
func (*UpdateExportRecordStatusRsp) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{13}
}

// 处理导入记录请求参数
type HandleImportRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HandleImportRecordReq) Reset() {
	*x = HandleImportRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleImportRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleImportRecordReq) ProtoMessage() {}

func (x *HandleImportRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleImportRecordReq.ProtoReflect.Descriptor instead.
func (*HandleImportRecordReq) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{14}
}

// 处理导出记录返回参数
type HandleImportRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HandleImportRecordRsp) Reset() {
	*x = HandleImportRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleImportRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleImportRecordRsp) ProtoMessage() {}

func (x *HandleImportRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleImportRecordRsp.ProtoReflect.Descriptor instead.
func (*HandleImportRecordRsp) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{15}
}

// 处理导出记录请求参数
type HandleExportRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HandleExportRecordReq) Reset() {
	*x = HandleExportRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleExportRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleExportRecordReq) ProtoMessage() {}

func (x *HandleExportRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleExportRecordReq.ProtoReflect.Descriptor instead.
func (*HandleExportRecordReq) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{16}
}

// 处理导出记录返回参数
type HandleExportRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HandleExportRecordRsp) Reset() {
	*x = HandleExportRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_file_v1_file_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleExportRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleExportRecordRsp) ProtoMessage() {}

func (x *HandleExportRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_file_v1_file_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleExportRecordRsp.ProtoReflect.Descriptor instead.
func (*HandleExportRecordRsp) Descriptor() ([]byte, []int) {
	return file_file_v1_file_proto_rawDescGZIP(), []int{17}
}

var File_file_v1_file_proto protoreflect.FileDescriptor

var file_file_v1_file_proto_rawDesc = []byte{
	0x0a, 0x12, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbf, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x89, 0x01, 0x0a, 0x13, 0x4c, 0x69,
	0x73, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73,
	0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70,
	0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xab, 0x03, 0x0a, 0x0c, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0d, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x4e, 0x75, 0x6d,
	0x12, 0x1f, 0x0a, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4e, 0x75,
	0x6d, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x26, 0x0a,
	0x0f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xbf, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x6e, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x89, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61,
	0x67, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x95, 0x02, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xdc, 0x02, 0x0a, 0x15, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x6f,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30,
	0x01, 0x30, 0x03, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x0a, 0x52, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61,
	0x6c, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x52, 0x02, 0x69,
	0x70, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x17, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73,
	0x70, 0x22, 0xa0, 0x02, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x03, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x05, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x12, 0x25, 0x0a,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b,
	0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x17, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0xf9, 0x01,
	0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x09, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x66, 0x75, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a,
	0x0a, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0f,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x46, 0x69, 0x6c,
	0x65, 0x55, 0x72, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x1d, 0x0a, 0x1b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x22, 0xae, 0x01, 0x0a, 0x1b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x22, 0x1d, 0x0a, 0x1b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x22, 0x17, 0x0a, 0x15, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x22, 0x17, 0x0a, 0x15, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0x17, 0x0a, 0x15, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x22, 0x17, 0x0a, 0x15, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x32, 0x9e, 0x06, 0x0a,
	0x04, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x58, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x58, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5e, 0x0a, 0x12, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5e, 0x0a, 0x12, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x18, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x18, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5e, 0x0a,
	0x12, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5e, 0x0a,
	0x12, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x10, 0x5a,
	0x0e, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_file_v1_file_proto_rawDescOnce sync.Once
	file_file_v1_file_proto_rawDescData = file_file_v1_file_proto_rawDesc
)

func file_file_v1_file_proto_rawDescGZIP() []byte {
	file_file_v1_file_proto_rawDescOnce.Do(func() {
		file_file_v1_file_proto_rawDescData = protoimpl.X.CompressGZIP(file_file_v1_file_proto_rawDescData)
	})
	return file_file_v1_file_proto_rawDescData
}

var file_file_v1_file_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_file_v1_file_proto_goTypes = []interface{}{
	(*ListImportRecordReq)(nil),         // 0: api.file.v1.ListImportRecordReq
	(*ListImportRecordRsp)(nil),         // 1: api.file.v1.ListImportRecordRsp
	(*ImportRecord)(nil),                // 2: api.file.v1.ImportRecord
	(*ListExportRecordReq)(nil),         // 3: api.file.v1.ListExportRecordReq
	(*ListExportRecordRsp)(nil),         // 4: api.file.v1.ListExportRecordRsp
	(*ExportRecord)(nil),                // 5: api.file.v1.ExportRecord
	(*CreateImportRecordReq)(nil),       // 6: api.file.v1.CreateImportRecordReq
	(*CreateImportRecordRsp)(nil),       // 7: api.file.v1.CreateImportRecordRsp
	(*CreateExportRecordReq)(nil),       // 8: api.file.v1.CreateExportRecordReq
	(*CreateExportRecordRsp)(nil),       // 9: api.file.v1.CreateExportRecordRsp
	(*UpdateImportRecordStatusReq)(nil), // 10: api.file.v1.UpdateImportRecordStatusReq
	(*UpdateImportRecordStatusRsp)(nil), // 11: api.file.v1.UpdateImportRecordStatusRsp
	(*UpdateExportRecordStatusReq)(nil), // 12: api.file.v1.UpdateExportRecordStatusReq
	(*UpdateExportRecordStatusRsp)(nil), // 13: api.file.v1.UpdateExportRecordStatusRsp
	(*HandleImportRecordReq)(nil),       // 14: api.file.v1.HandleImportRecordReq
	(*HandleImportRecordRsp)(nil),       // 15: api.file.v1.HandleImportRecordRsp
	(*HandleExportRecordReq)(nil),       // 16: api.file.v1.HandleExportRecordReq
	(*HandleExportRecordRsp)(nil),       // 17: api.file.v1.HandleExportRecordRsp
}
var file_file_v1_file_proto_depIdxs = []int32{
	2,  // 0: api.file.v1.ListImportRecordRsp.list:type_name -> api.file.v1.ImportRecord
	5,  // 1: api.file.v1.ListExportRecordRsp.list:type_name -> api.file.v1.ExportRecord
	0,  // 2: api.file.v1.File.ListImportRecord:input_type -> api.file.v1.ListImportRecordReq
	3,  // 3: api.file.v1.File.ListExportRecord:input_type -> api.file.v1.ListExportRecordReq
	6,  // 4: api.file.v1.File.CreateImportRecord:input_type -> api.file.v1.CreateImportRecordReq
	8,  // 5: api.file.v1.File.CreateExportRecord:input_type -> api.file.v1.CreateExportRecordReq
	10, // 6: api.file.v1.File.UpdateImportRecordStatus:input_type -> api.file.v1.UpdateImportRecordStatusReq
	12, // 7: api.file.v1.File.UpdateExportRecordStatus:input_type -> api.file.v1.UpdateExportRecordStatusReq
	14, // 8: api.file.v1.File.HandleImportRecord:input_type -> api.file.v1.HandleImportRecordReq
	16, // 9: api.file.v1.File.HandleExportRecord:input_type -> api.file.v1.HandleExportRecordReq
	1,  // 10: api.file.v1.File.ListImportRecord:output_type -> api.file.v1.ListImportRecordRsp
	4,  // 11: api.file.v1.File.ListExportRecord:output_type -> api.file.v1.ListExportRecordRsp
	7,  // 12: api.file.v1.File.CreateImportRecord:output_type -> api.file.v1.CreateImportRecordRsp
	9,  // 13: api.file.v1.File.CreateExportRecord:output_type -> api.file.v1.CreateExportRecordRsp
	11, // 14: api.file.v1.File.UpdateImportRecordStatus:output_type -> api.file.v1.UpdateImportRecordStatusRsp
	13, // 15: api.file.v1.File.UpdateExportRecordStatus:output_type -> api.file.v1.UpdateExportRecordStatusRsp
	15, // 16: api.file.v1.File.HandleImportRecord:output_type -> api.file.v1.HandleImportRecordRsp
	17, // 17: api.file.v1.File.HandleExportRecord:output_type -> api.file.v1.HandleExportRecordRsp
	10, // [10:18] is the sub-list for method output_type
	2,  // [2:10] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_file_v1_file_proto_init() }
func file_file_v1_file_proto_init() {
	if File_file_v1_file_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_file_v1_file_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListImportRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListImportRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExportRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExportRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateImportRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateImportRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExportRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExportRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateImportRecordStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateImportRecordStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExportRecordStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExportRecordStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleImportRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleImportRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleExportRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_file_v1_file_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleExportRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_file_v1_file_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_file_v1_file_proto_goTypes,
		DependencyIndexes: file_file_v1_file_proto_depIdxs,
		MessageInfos:      file_file_v1_file_proto_msgTypes,
	}.Build()
	File_file_v1_file_proto = out.File
	file_file_v1_file_proto_rawDesc = nil
	file_file_v1_file_proto_goTypes = nil
	file_file_v1_file_proto_depIdxs = nil
}
