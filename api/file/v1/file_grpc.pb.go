// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: file/v1/file.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	File_ListImportRecord_FullMethodName         = "/api.file.v1.File/ListImportRecord"
	File_ListExportRecord_FullMethodName         = "/api.file.v1.File/ListExportRecord"
	File_CreateImportRecord_FullMethodName       = "/api.file.v1.File/CreateImportRecord"
	File_CreateExportRecord_FullMethodName       = "/api.file.v1.File/CreateExportRecord"
	File_UpdateImportRecordStatus_FullMethodName = "/api.file.v1.File/UpdateImportRecordStatus"
	File_UpdateExportRecordStatus_FullMethodName = "/api.file.v1.File/UpdateExportRecordStatus"
	File_HandleImportRecord_FullMethodName       = "/api.file.v1.File/HandleImportRecord"
	File_HandleExportRecord_FullMethodName       = "/api.file.v1.File/HandleExportRecord"
)

// FileClient is the client API for File service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FileClient interface {
	// 获取导入记录列表
	ListImportRecord(ctx context.Context, in *ListImportRecordReq, opts ...grpc.CallOption) (*ListImportRecordRsp, error)
	// 获取导出记录列表
	ListExportRecord(ctx context.Context, in *ListExportRecordReq, opts ...grpc.CallOption) (*ListExportRecordRsp, error)
	// 创建导入记录
	CreateImportRecord(ctx context.Context, in *CreateImportRecordReq, opts ...grpc.CallOption) (*CreateImportRecordRsp, error)
	// 创建导出记录
	CreateExportRecord(ctx context.Context, in *CreateExportRecordReq, opts ...grpc.CallOption) (*CreateExportRecordRsp, error)
	// 更新导入记录状态
	UpdateImportRecordStatus(ctx context.Context, in *UpdateImportRecordStatusReq, opts ...grpc.CallOption) (*UpdateImportRecordStatusRsp, error)
	// 更新导出记录状态
	UpdateExportRecordStatus(ctx context.Context, in *UpdateExportRecordStatusReq, opts ...grpc.CallOption) (*UpdateExportRecordStatusRsp, error)
	// 处理导入记录
	HandleImportRecord(ctx context.Context, in *HandleImportRecordReq, opts ...grpc.CallOption) (*HandleImportRecordRsp, error)
	// 处理导出记录
	HandleExportRecord(ctx context.Context, in *HandleExportRecordReq, opts ...grpc.CallOption) (*HandleExportRecordRsp, error)
}

type fileClient struct {
	cc grpc.ClientConnInterface
}

func NewFileClient(cc grpc.ClientConnInterface) FileClient {
	return &fileClient{cc}
}

func (c *fileClient) ListImportRecord(ctx context.Context, in *ListImportRecordReq, opts ...grpc.CallOption) (*ListImportRecordRsp, error) {
	out := new(ListImportRecordRsp)
	err := c.cc.Invoke(ctx, File_ListImportRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileClient) ListExportRecord(ctx context.Context, in *ListExportRecordReq, opts ...grpc.CallOption) (*ListExportRecordRsp, error) {
	out := new(ListExportRecordRsp)
	err := c.cc.Invoke(ctx, File_ListExportRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileClient) CreateImportRecord(ctx context.Context, in *CreateImportRecordReq, opts ...grpc.CallOption) (*CreateImportRecordRsp, error) {
	out := new(CreateImportRecordRsp)
	err := c.cc.Invoke(ctx, File_CreateImportRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileClient) CreateExportRecord(ctx context.Context, in *CreateExportRecordReq, opts ...grpc.CallOption) (*CreateExportRecordRsp, error) {
	out := new(CreateExportRecordRsp)
	err := c.cc.Invoke(ctx, File_CreateExportRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileClient) UpdateImportRecordStatus(ctx context.Context, in *UpdateImportRecordStatusReq, opts ...grpc.CallOption) (*UpdateImportRecordStatusRsp, error) {
	out := new(UpdateImportRecordStatusRsp)
	err := c.cc.Invoke(ctx, File_UpdateImportRecordStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileClient) UpdateExportRecordStatus(ctx context.Context, in *UpdateExportRecordStatusReq, opts ...grpc.CallOption) (*UpdateExportRecordStatusRsp, error) {
	out := new(UpdateExportRecordStatusRsp)
	err := c.cc.Invoke(ctx, File_UpdateExportRecordStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileClient) HandleImportRecord(ctx context.Context, in *HandleImportRecordReq, opts ...grpc.CallOption) (*HandleImportRecordRsp, error) {
	out := new(HandleImportRecordRsp)
	err := c.cc.Invoke(ctx, File_HandleImportRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileClient) HandleExportRecord(ctx context.Context, in *HandleExportRecordReq, opts ...grpc.CallOption) (*HandleExportRecordRsp, error) {
	out := new(HandleExportRecordRsp)
	err := c.cc.Invoke(ctx, File_HandleExportRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileServer is the server API for File service.
// All implementations must embed UnimplementedFileServer
// for forward compatibility
type FileServer interface {
	// 获取导入记录列表
	ListImportRecord(context.Context, *ListImportRecordReq) (*ListImportRecordRsp, error)
	// 获取导出记录列表
	ListExportRecord(context.Context, *ListExportRecordReq) (*ListExportRecordRsp, error)
	// 创建导入记录
	CreateImportRecord(context.Context, *CreateImportRecordReq) (*CreateImportRecordRsp, error)
	// 创建导出记录
	CreateExportRecord(context.Context, *CreateExportRecordReq) (*CreateExportRecordRsp, error)
	// 更新导入记录状态
	UpdateImportRecordStatus(context.Context, *UpdateImportRecordStatusReq) (*UpdateImportRecordStatusRsp, error)
	// 更新导出记录状态
	UpdateExportRecordStatus(context.Context, *UpdateExportRecordStatusReq) (*UpdateExportRecordStatusRsp, error)
	// 处理导入记录
	HandleImportRecord(context.Context, *HandleImportRecordReq) (*HandleImportRecordRsp, error)
	// 处理导出记录
	HandleExportRecord(context.Context, *HandleExportRecordReq) (*HandleExportRecordRsp, error)
	mustEmbedUnimplementedFileServer()
}

// UnimplementedFileServer must be embedded to have forward compatible implementations.
type UnimplementedFileServer struct {
}

func (UnimplementedFileServer) ListImportRecord(context.Context, *ListImportRecordReq) (*ListImportRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListImportRecord not implemented")
}
func (UnimplementedFileServer) ListExportRecord(context.Context, *ListExportRecordReq) (*ListExportRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExportRecord not implemented")
}
func (UnimplementedFileServer) CreateImportRecord(context.Context, *CreateImportRecordReq) (*CreateImportRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateImportRecord not implemented")
}
func (UnimplementedFileServer) CreateExportRecord(context.Context, *CreateExportRecordReq) (*CreateExportRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateExportRecord not implemented")
}
func (UnimplementedFileServer) UpdateImportRecordStatus(context.Context, *UpdateImportRecordStatusReq) (*UpdateImportRecordStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateImportRecordStatus not implemented")
}
func (UnimplementedFileServer) UpdateExportRecordStatus(context.Context, *UpdateExportRecordStatusReq) (*UpdateExportRecordStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExportRecordStatus not implemented")
}
func (UnimplementedFileServer) HandleImportRecord(context.Context, *HandleImportRecordReq) (*HandleImportRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleImportRecord not implemented")
}
func (UnimplementedFileServer) HandleExportRecord(context.Context, *HandleExportRecordReq) (*HandleExportRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleExportRecord not implemented")
}
func (UnimplementedFileServer) mustEmbedUnimplementedFileServer() {}

// UnsafeFileServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileServer will
// result in compilation errors.
type UnsafeFileServer interface {
	mustEmbedUnimplementedFileServer()
}

func RegisterFileServer(s grpc.ServiceRegistrar, srv FileServer) {
	s.RegisterService(&File_ServiceDesc, srv)
}

func _File_ListImportRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListImportRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServer).ListImportRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: File_ListImportRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServer).ListImportRecord(ctx, req.(*ListImportRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _File_ListExportRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExportRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServer).ListExportRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: File_ListExportRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServer).ListExportRecord(ctx, req.(*ListExportRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _File_CreateImportRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateImportRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServer).CreateImportRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: File_CreateImportRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServer).CreateImportRecord(ctx, req.(*CreateImportRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _File_CreateExportRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateExportRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServer).CreateExportRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: File_CreateExportRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServer).CreateExportRecord(ctx, req.(*CreateExportRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _File_UpdateImportRecordStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateImportRecordStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServer).UpdateImportRecordStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: File_UpdateImportRecordStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServer).UpdateImportRecordStatus(ctx, req.(*UpdateImportRecordStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _File_UpdateExportRecordStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateExportRecordStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServer).UpdateExportRecordStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: File_UpdateExportRecordStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServer).UpdateExportRecordStatus(ctx, req.(*UpdateExportRecordStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _File_HandleImportRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleImportRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServer).HandleImportRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: File_HandleImportRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServer).HandleImportRecord(ctx, req.(*HandleImportRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _File_HandleExportRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleExportRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServer).HandleExportRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: File_HandleExportRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServer).HandleExportRecord(ctx, req.(*HandleExportRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

// File_ServiceDesc is the grpc.ServiceDesc for File service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var File_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.file.v1.File",
	HandlerType: (*FileServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListImportRecord",
			Handler:    _File_ListImportRecord_Handler,
		},
		{
			MethodName: "ListExportRecord",
			Handler:    _File_ListExportRecord_Handler,
		},
		{
			MethodName: "CreateImportRecord",
			Handler:    _File_CreateImportRecord_Handler,
		},
		{
			MethodName: "CreateExportRecord",
			Handler:    _File_CreateExportRecord_Handler,
		},
		{
			MethodName: "UpdateImportRecordStatus",
			Handler:    _File_UpdateImportRecordStatus_Handler,
		},
		{
			MethodName: "UpdateExportRecordStatus",
			Handler:    _File_UpdateExportRecordStatus_Handler,
		},
		{
			MethodName: "HandleImportRecord",
			Handler:    _File_HandleImportRecord_Handler,
		},
		{
			MethodName: "HandleExportRecord",
			Handler:    _File_HandleExportRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "file/v1/file.proto",
}
