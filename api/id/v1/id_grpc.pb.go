// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: id/v1/id.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	IDGenerator_NextID_FullMethodName = "/id.v1.IDGenerator/NextID"
)

// IDGeneratorClient is the client API for IDGenerator service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type IDGeneratorClient interface {
	// 获取单个id
	NextID(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*wrapperspb.UInt64Value, error)
}

type iDGeneratorClient struct {
	cc grpc.ClientConnInterface
}

func NewIDGeneratorClient(cc grpc.ClientConnInterface) IDGeneratorClient {
	return &iDGeneratorClient{cc}
}

func (c *iDGeneratorClient) NextID(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*wrapperspb.UInt64Value, error) {
	out := new(wrapperspb.UInt64Value)
	err := c.cc.Invoke(ctx, IDGenerator_NextID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IDGeneratorServer is the server API for IDGenerator service.
// All implementations must embed UnimplementedIDGeneratorServer
// for forward compatibility
type IDGeneratorServer interface {
	// 获取单个id
	NextID(context.Context, *emptypb.Empty) (*wrapperspb.UInt64Value, error)
	mustEmbedUnimplementedIDGeneratorServer()
}

// UnimplementedIDGeneratorServer must be embedded to have forward compatible implementations.
type UnimplementedIDGeneratorServer struct {
}

func (UnimplementedIDGeneratorServer) NextID(context.Context, *emptypb.Empty) (*wrapperspb.UInt64Value, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NextID not implemented")
}
func (UnimplementedIDGeneratorServer) mustEmbedUnimplementedIDGeneratorServer() {}

// UnsafeIDGeneratorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to IDGeneratorServer will
// result in compilation errors.
type UnsafeIDGeneratorServer interface {
	mustEmbedUnimplementedIDGeneratorServer()
}

func RegisterIDGeneratorServer(s grpc.ServiceRegistrar, srv IDGeneratorServer) {
	s.RegisterService(&IDGenerator_ServiceDesc, srv)
}

func _IDGenerator_NextID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IDGeneratorServer).NextID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IDGenerator_NextID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IDGeneratorServer).NextID(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// IDGenerator_ServiceDesc is the grpc.ServiceDesc for IDGenerator service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var IDGenerator_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "id.v1.IDGenerator",
	HandlerType: (*IDGeneratorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NextID",
			Handler:    _IDGenerator_NextID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "id/v1/id.proto",
}
