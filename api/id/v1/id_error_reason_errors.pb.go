// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package v1

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 员工已存在
func IsContactStaffAlreadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_STAFF_ALREADY_EXISTS.String() && e.Code == 400
}

// 员工已存在
func ErrorContactStaffAlreadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_STAFF_ALREADY_EXISTS.String(), fmt.Sprintf(format, args...))
}
