// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.17.3
// source: student/v1/student.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FindStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否返回班级信息
	ReturnClassInfo bool `protobuf:"varint,1,opt,name=return_class_info,json=returnClassInfo,proto3" json:"return_class_info,omitempty"`
	// 是否返回家长信息
	ReturnParentInfo bool `protobuf:"varint,2,opt,name=return_parent_info,json=returnParentInfo,proto3" json:"return_parent_info,omitempty"`
	// 学校id，必填。
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 学生id
	StudentIds []int64 `protobuf:"varint,4,rep,packed,name=student_ids,json=studentIds,proto3" json:"student_ids,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,5,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 班级id
	ClassIds []int64 `protobuf:"varint,6,rep,packed,name=class_ids,json=classIds,proto3" json:"class_ids,omitempty"`
}

func (x *FindStudentReq) Reset() {
	*x = FindStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStudentReq) ProtoMessage() {}

func (x *FindStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStudentReq.ProtoReflect.Descriptor instead.
func (*FindStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{0}
}

func (x *FindStudentReq) GetReturnClassInfo() bool {
	if x != nil {
		return x.ReturnClassInfo
	}
	return false
}

func (x *FindStudentReq) GetReturnParentInfo() bool {
	if x != nil {
		return x.ReturnParentInfo
	}
	return false
}

func (x *FindStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *FindStudentReq) GetStudentIds() []int64 {
	if x != nil {
		return x.StudentIds
	}
	return nil
}

func (x *FindStudentReq) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *FindStudentReq) GetClassIds() []int64 {
	if x != nil {
		return x.ClassIds
	}
	return nil
}

type FindStudentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生列表
	List []*StudentInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *FindStudentRsp) Reset() {
	*x = FindStudentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStudentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStudentRsp) ProtoMessage() {}

func (x *FindStudentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStudentRsp.ProtoReflect.Descriptor instead.
func (*FindStudentRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{1}
}

func (x *FindStudentRsp) GetList() []*StudentInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type ListUnInvitedFamilyGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校ID
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 学生ID
	StuId int64 `protobuf:"varint,2,opt,name=stu_id,json=stuId,proto3" json:"stu_id,omitempty"`
}

func (x *ListUnInvitedFamilyGroupReq) Reset() {
	*x = ListUnInvitedFamilyGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnInvitedFamilyGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnInvitedFamilyGroupReq) ProtoMessage() {}

func (x *ListUnInvitedFamilyGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnInvitedFamilyGroupReq.ProtoReflect.Descriptor instead.
func (*ListUnInvitedFamilyGroupReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{2}
}

func (x *ListUnInvitedFamilyGroupReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListUnInvitedFamilyGroupReq) GetStuId() int64 {
	if x != nil {
		return x.StuId
	}
	return 0
}

type ListUnInvitedFamilyGroupRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []uint32 `protobuf:"varint,1,rep,packed,name=list,proto3" json:"list,omitempty"`
}

func (x *ListUnInvitedFamilyGroupRsp) Reset() {
	*x = ListUnInvitedFamilyGroupRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnInvitedFamilyGroupRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnInvitedFamilyGroupRsp) ProtoMessage() {}

func (x *ListUnInvitedFamilyGroupRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnInvitedFamilyGroupRsp.ProtoReflect.Descriptor instead.
func (*ListUnInvitedFamilyGroupRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{3}
}

func (x *ListUnInvitedFamilyGroupRsp) GetList() []uint32 {
	if x != nil {
		return x.List
	}
	return nil
}

type SearchStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 搜索关键词
	Keyword string `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
}

func (x *SearchStudentReq) Reset() {
	*x = SearchStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchStudentReq) ProtoMessage() {}

func (x *SearchStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchStudentReq.ProtoReflect.Descriptor instead.
func (*SearchStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{4}
}

func (x *SearchStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *SearchStudentReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type SearchStudentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索结果列表
	List []*SimpleStudentInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SearchStudentRsp) Reset() {
	*x = SearchStudentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchStudentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchStudentRsp) ProtoMessage() {}

func (x *SearchStudentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchStudentRsp.ProtoReflect.Descriptor instead.
func (*SearchStudentRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{5}
}

func (x *SearchStudentRsp) GetList() []*SimpleStudentInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type SimpleStudentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SimpleStudentInfo) Reset() {
	*x = SimpleStudentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimpleStudentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleStudentInfo) ProtoMessage() {}

func (x *SimpleStudentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleStudentInfo.ProtoReflect.Descriptor instead.
func (*SimpleStudentInfo) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{6}
}

func (x *SimpleStudentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SimpleStudentInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetStudentActivateInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 当前登录用户账号ID
	AccId int64 `protobuf:"varint,2,opt,name=acc_id,json=accId,proto3" json:"acc_id,omitempty"`
}

func (x *GetStudentActivateInfoReq) Reset() {
	*x = GetStudentActivateInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStudentActivateInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStudentActivateInfoReq) ProtoMessage() {}

func (x *GetStudentActivateInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStudentActivateInfoReq.ProtoReflect.Descriptor instead.
func (*GetStudentActivateInfoReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{7}
}

func (x *GetStudentActivateInfoReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetStudentActivateInfoReq) GetAccId() int64 {
	if x != nil {
		return x.AccId
	}
	return 0
}

type GetStudentActivateInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 激活数
	ActivatedCount uint32 `protobuf:"varint,1,opt,name=activated_count,json=activatedCount,proto3" json:"activated_count,omitempty"`
	// 未激活数
	UnactivateCount uint32 `protobuf:"varint,2,opt,name=unactivate_count,json=unactivateCount,proto3" json:"unactivate_count,omitempty"`
}

func (x *GetStudentActivateInfoRsp) Reset() {
	*x = GetStudentActivateInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStudentActivateInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStudentActivateInfoRsp) ProtoMessage() {}

func (x *GetStudentActivateInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStudentActivateInfoRsp.ProtoReflect.Descriptor instead.
func (*GetStudentActivateInfoRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{8}
}

func (x *GetStudentActivateInfoRsp) GetActivatedCount() uint32 {
	if x != nil {
		return x.ActivatedCount
	}
	return 0
}

func (x *GetStudentActivateInfoRsp) GetUnactivateCount() uint32 {
	if x != nil {
		return x.UnactivateCount
	}
	return 0
}

type GetFullStudentTreeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生是否离园
	IsLeave uint32 `protobuf:"varint,1,opt,name=is_leave,json=isLeave,proto3" json:"is_leave,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *GetFullStudentTreeReq) Reset() {
	*x = GetFullStudentTreeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFullStudentTreeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFullStudentTreeReq) ProtoMessage() {}

func (x *GetFullStudentTreeReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFullStudentTreeReq.ProtoReflect.Descriptor instead.
func (*GetFullStudentTreeReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{9}
}

func (x *GetFullStudentTreeReq) GetIsLeave() uint32 {
	if x != nil {
		return x.IsLeave
	}
	return 0
}

func (x *GetFullStudentTreeReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

type GetFullStudentTreeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Child []*GetFullStudentTreeRsp_Dept `protobuf:"bytes,1,rep,name=child,proto3" json:"child,omitempty"`
}

func (x *GetFullStudentTreeRsp) Reset() {
	*x = GetFullStudentTreeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFullStudentTreeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFullStudentTreeRsp) ProtoMessage() {}

func (x *GetFullStudentTreeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFullStudentTreeRsp.ProtoReflect.Descriptor instead.
func (*GetFullStudentTreeRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{10}
}

func (x *GetFullStudentTreeRsp) GetChild() []*GetFullStudentTreeRsp_Dept {
	if x != nil {
		return x.Child
	}
	return nil
}

// 获取学生信息请求参数
type GetStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *GetStudentReq) Reset() {
	*x = GetStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStudentReq) ProtoMessage() {}

func (x *GetStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStudentReq.ProtoReflect.Descriptor instead.
func (*GetStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{11}
}

func (x *GetStudentReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 获取学生信息返回参数
type GetStudentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生姓名，限30字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	// 多音字
	Polyphone string `protobuf:"bytes,5,opt,name=polyphone,proto3" json:"polyphone,omitempty"`
	// 出生日期，时间戳，单位：秒
	Birthday int64 `protobuf:"varint,6,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// 入园时间，时间戳，单位：秒
	EntryDate int64 `protobuf:"varint,7,opt,name=entry_date,json=entryDate,proto3" json:"entry_date,omitempty"`
	// 是否离园（0：否，1：是）
	IsLeave uint32 `protobuf:"varint,8,opt,name=is_leave,json=isLeave,proto3" json:"is_leave,omitempty"`
	// 离园原因
	LeaveReason string `protobuf:"bytes,9,opt,name=leave_reason,json=leaveReason,proto3" json:"leave_reason,omitempty"`
	// 离园时间
	LeaveTime int64 `protobuf:"varint,10,opt,name=leave_time,json=leaveTime,proto3" json:"leave_time,omitempty"`
	// 所在班级信息
	ClassInfo *ClassInfo `protobuf:"bytes,11,opt,name=class_info,json=classInfo,proto3" json:"class_info,omitempty"`
	// 联系人列表
	ParentList []*ContactInfo `protobuf:"bytes,12,rep,name=parent_list,json=parentList,proto3" json:"parent_list,omitempty"`
	// 考勤卡列表
	CardList []string `protobuf:"bytes,13,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
}

func (x *GetStudentRsp) Reset() {
	*x = GetStudentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStudentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStudentRsp) ProtoMessage() {}

func (x *GetStudentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStudentRsp.ProtoReflect.Descriptor instead.
func (*GetStudentRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{12}
}

func (x *GetStudentRsp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetStudentRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetStudentRsp) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetStudentRsp) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *GetStudentRsp) GetPolyphone() string {
	if x != nil {
		return x.Polyphone
	}
	return ""
}

func (x *GetStudentRsp) GetBirthday() int64 {
	if x != nil {
		return x.Birthday
	}
	return 0
}

func (x *GetStudentRsp) GetEntryDate() int64 {
	if x != nil {
		return x.EntryDate
	}
	return 0
}

func (x *GetStudentRsp) GetIsLeave() uint32 {
	if x != nil {
		return x.IsLeave
	}
	return 0
}

func (x *GetStudentRsp) GetLeaveReason() string {
	if x != nil {
		return x.LeaveReason
	}
	return ""
}

func (x *GetStudentRsp) GetLeaveTime() int64 {
	if x != nil {
		return x.LeaveTime
	}
	return 0
}

func (x *GetStudentRsp) GetClassInfo() *ClassInfo {
	if x != nil {
		return x.ClassInfo
	}
	return nil
}

func (x *GetStudentRsp) GetParentList() []*ContactInfo {
	if x != nil {
		return x.ParentList
	}
	return nil
}

func (x *GetStudentRsp) GetCardList() []string {
	if x != nil {
		return x.CardList
	}
	return nil
}

type ClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId string `protobuf:"bytes,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,2,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级id
	GradeId string `protobuf:"bytes,3,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,4,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
}

func (x *ClassInfo) Reset() {
	*x = ClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassInfo) ProtoMessage() {}

func (x *ClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassInfo.ProtoReflect.Descriptor instead.
func (*ClassInfo) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{13}
}

func (x *ClassInfo) GetClassId() string {
	if x != nil {
		return x.ClassId
	}
	return ""
}

func (x *ClassInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *ClassInfo) GetGradeId() string {
	if x != nil {
		return x.GradeId
	}
	return ""
}

func (x *ClassInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

// 创建学生请求参数
type CreateStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生姓名，限30字符
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,2,opt,name=gender,proto3" json:"gender,omitempty"`
	// 出生日期，时间戳，单位：秒
	Birthday int64 `protobuf:"varint,3,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// 入园时间，时间戳，单位：秒
	EntryDate int64 `protobuf:"varint,4,opt,name=entry_date,json=entryDate,proto3" json:"entry_date,omitempty"`
	// 所在班级id
	ClassId int64 `protobuf:"varint,5,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 多音字
	Polyphone string `protobuf:"bytes,6,opt,name=polyphone,proto3" json:"polyphone,omitempty"`
	// 家长列表
	ParentList []*ContactInfo `protobuf:"bytes,7,rep,name=parent_list,json=parentList,proto3" json:"parent_list,omitempty"`
	// 考勤卡，最多绑10张卡
	CardList []string `protobuf:"bytes,8,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
	// 来源
	Source uint32 `protobuf:"varint,9,opt,name=source,proto3" json:"source,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,10,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,11,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,12,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,13,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,14,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *CreateStudentReq) Reset() {
	*x = CreateStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStudentReq) ProtoMessage() {}

func (x *CreateStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStudentReq.ProtoReflect.Descriptor instead.
func (*CreateStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{14}
}

func (x *CreateStudentReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateStudentReq) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *CreateStudentReq) GetBirthday() int64 {
	if x != nil {
		return x.Birthday
	}
	return 0
}

func (x *CreateStudentReq) GetEntryDate() int64 {
	if x != nil {
		return x.EntryDate
	}
	return 0
}

func (x *CreateStudentReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *CreateStudentReq) GetPolyphone() string {
	if x != nil {
		return x.Polyphone
	}
	return ""
}

func (x *CreateStudentReq) GetParentList() []*ContactInfo {
	if x != nil {
		return x.ParentList
	}
	return nil
}

func (x *CreateStudentReq) GetCardList() []string {
	if x != nil {
		return x.CardList
	}
	return nil
}

func (x *CreateStudentReq) GetSource() uint32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *CreateStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *CreateStudentReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *CreateStudentReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateStudentReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *CreateStudentReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type ContactInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 关系(1：爸爸，2：妈妈，3：爷爷，4：奶奶，5：外公，6：外婆，7：伯父8：伯母，9：叔叔，10：婶婶，11：姑父，12：姑母，13：舅舅 14：舅妈，15：姐姐，16：哥哥，17：嫂嫂，18：姨妈，19：姑丈，20：其他)
	Relation uint32 `protobuf:"varint,2,opt,name=relation,proto3" json:"relation,omitempty"`
	// 家长姓名
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 手机区号，默认需填86
	ReCode string `protobuf:"bytes,4,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 是否是第一联系人
	IsFirst uint32 `protobuf:"varint,6,opt,name=is_first,json=isFirst,proto3" json:"is_first,omitempty"`
	// 是否已激活(0-未激活 1-已激活)
	IsActivated uint32 `protobuf:"varint,7,opt,name=is_activated,json=isActivated,proto3" json:"is_activated,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 最后一次登陆时间
	LastLoginTime int64 `protobuf:"varint,9,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time,omitempty"`
}

func (x *ContactInfo) Reset() {
	*x = ContactInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContactInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactInfo) ProtoMessage() {}

func (x *ContactInfo) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactInfo.ProtoReflect.Descriptor instead.
func (*ContactInfo) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{15}
}

func (x *ContactInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ContactInfo) GetRelation() uint32 {
	if x != nil {
		return x.Relation
	}
	return 0
}

func (x *ContactInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ContactInfo) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *ContactInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *ContactInfo) GetIsFirst() uint32 {
	if x != nil {
		return x.IsFirst
	}
	return 0
}

func (x *ContactInfo) GetIsActivated() uint32 {
	if x != nil {
		return x.IsActivated
	}
	return 0
}

func (x *ContactInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ContactInfo) GetLastLoginTime() int64 {
	if x != nil {
		return x.LastLoginTime
	}
	return 0
}

// 更新学生基本请求参数
type UpdateStudentBasicInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生姓名，限30字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,3,opt,name=gender,proto3" json:"gender,omitempty"`
	// 出生日期，时间戳，单位：秒
	Birthday int64 `protobuf:"varint,4,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// 入园时间，时间戳，单位：秒
	EntryDate int64 `protobuf:"varint,5,opt,name=entry_date,json=entryDate,proto3" json:"entry_date,omitempty"`
	// 多音字
	Polyphone string `protobuf:"bytes,6,opt,name=polyphone,proto3" json:"polyphone,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,8,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,9,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,10,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,11,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,12,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateStudentBasicInfoReq) Reset() {
	*x = UpdateStudentBasicInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStudentBasicInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStudentBasicInfoReq) ProtoMessage() {}

func (x *UpdateStudentBasicInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStudentBasicInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateStudentBasicInfoReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateStudentBasicInfoReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateStudentBasicInfoReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateStudentBasicInfoReq) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UpdateStudentBasicInfoReq) GetBirthday() int64 {
	if x != nil {
		return x.Birthday
	}
	return 0
}

func (x *UpdateStudentBasicInfoReq) GetEntryDate() int64 {
	if x != nil {
		return x.EntryDate
	}
	return 0
}

func (x *UpdateStudentBasicInfoReq) GetPolyphone() string {
	if x != nil {
		return x.Polyphone
	}
	return ""
}

func (x *UpdateStudentBasicInfoReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateStudentBasicInfoReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateStudentBasicInfoReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateStudentBasicInfoReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateStudentBasicInfoReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateStudentBasicInfoReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 学生批量删除请求参数
type BatchDeleteStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *BatchDeleteStudentReq) Reset() {
	*x = BatchDeleteStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteStudentReq) ProtoMessage() {}

func (x *BatchDeleteStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteStudentReq.ProtoReflect.Descriptor instead.
func (*BatchDeleteStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{17}
}

func (x *BatchDeleteStudentReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchDeleteStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *BatchDeleteStudentReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *BatchDeleteStudentReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *BatchDeleteStudentReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *BatchDeleteStudentReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 学生批量删除返回参数
type BatchDeleteStudentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 失败列表，若为空则表示全部成功
	FailureList []*StudentFailure `protobuf:"bytes,1,rep,name=failure_list,json=failureList,proto3" json:"failure_list,omitempty"`
}

func (x *BatchDeleteStudentRsp) Reset() {
	*x = BatchDeleteStudentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteStudentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteStudentRsp) ProtoMessage() {}

func (x *BatchDeleteStudentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteStudentRsp.ProtoReflect.Descriptor instead.
func (*BatchDeleteStudentRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{18}
}

func (x *BatchDeleteStudentRsp) GetFailureList() []*StudentFailure {
	if x != nil {
		return x.FailureList
	}
	return nil
}

type StudentFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 失败原因
	Reason string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *StudentFailure) Reset() {
	*x = StudentFailure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StudentFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StudentFailure) ProtoMessage() {}

func (x *StudentFailure) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StudentFailure.ProtoReflect.Descriptor instead.
func (*StudentFailure) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{19}
}

func (x *StudentFailure) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StudentFailure) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StudentFailure) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 学生批量调班请求参数
type BatchShiftStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 转班学生信息
	Students []*BatchShiftStudentReq_ShiftStudent `protobuf:"bytes,1,rep,name=students,proto3" json:"students,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *BatchShiftStudentReq) Reset() {
	*x = BatchShiftStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchShiftStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchShiftStudentReq) ProtoMessage() {}

func (x *BatchShiftStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchShiftStudentReq.ProtoReflect.Descriptor instead.
func (*BatchShiftStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{20}
}

func (x *BatchShiftStudentReq) GetStudents() []*BatchShiftStudentReq_ShiftStudent {
	if x != nil {
		return x.Students
	}
	return nil
}

func (x *BatchShiftStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *BatchShiftStudentReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *BatchShiftStudentReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *BatchShiftStudentReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *BatchShiftStudentReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 学生批量调班返回参数
type BatchShiftStudentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 失败列表，若为空则表示全部成功
	FailureList []*StudentFailure `protobuf:"bytes,1,rep,name=failure_list,json=failureList,proto3" json:"failure_list,omitempty"`
}

func (x *BatchShiftStudentRsp) Reset() {
	*x = BatchShiftStudentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchShiftStudentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchShiftStudentRsp) ProtoMessage() {}

func (x *BatchShiftStudentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchShiftStudentRsp.ProtoReflect.Descriptor instead.
func (*BatchShiftStudentRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{21}
}

func (x *BatchShiftStudentRsp) GetFailureList() []*StudentFailure {
	if x != nil {
		return x.FailureList
	}
	return nil
}

// 批量离园学生请求参数
type BatchLeaveStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 离职原因，限50字
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,6,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *BatchLeaveStudentReq) Reset() {
	*x = BatchLeaveStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchLeaveStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchLeaveStudentReq) ProtoMessage() {}

func (x *BatchLeaveStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchLeaveStudentReq.ProtoReflect.Descriptor instead.
func (*BatchLeaveStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{22}
}

func (x *BatchLeaveStudentReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchLeaveStudentReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *BatchLeaveStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *BatchLeaveStudentReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *BatchLeaveStudentReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *BatchLeaveStudentReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *BatchLeaveStudentReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 批量离园学生返回参数
type BatchLeaveStudentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 失败列表，若为空则表示全部成功
	FailureList []*StudentFailure `protobuf:"bytes,1,rep,name=failure_list,json=failureList,proto3" json:"failure_list,omitempty"`
}

func (x *BatchLeaveStudentRsp) Reset() {
	*x = BatchLeaveStudentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchLeaveStudentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchLeaveStudentRsp) ProtoMessage() {}

func (x *BatchLeaveStudentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchLeaveStudentRsp.ProtoReflect.Descriptor instead.
func (*BatchLeaveStudentRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{23}
}

func (x *BatchLeaveStudentRsp) GetFailureList() []*StudentFailure {
	if x != nil {
		return x.FailureList
	}
	return nil
}

// 学生批量重新入园请求参数
type BatchReEntryStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 班级id
	ClassId int64 `protobuf:"varint,2,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,6,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *BatchReEntryStudentReq) Reset() {
	*x = BatchReEntryStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchReEntryStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchReEntryStudentReq) ProtoMessage() {}

func (x *BatchReEntryStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchReEntryStudentReq.ProtoReflect.Descriptor instead.
func (*BatchReEntryStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{24}
}

func (x *BatchReEntryStudentReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchReEntryStudentReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *BatchReEntryStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *BatchReEntryStudentReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *BatchReEntryStudentReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *BatchReEntryStudentReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *BatchReEntryStudentReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 学生批量重新入园返回参数
type BatchReEntryStudentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 失败列表，若为空则表示全部成功
	FailureList []*StudentFailure `protobuf:"bytes,1,rep,name=failure_list,json=failureList,proto3" json:"failure_list,omitempty"`
}

func (x *BatchReEntryStudentRsp) Reset() {
	*x = BatchReEntryStudentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchReEntryStudentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchReEntryStudentRsp) ProtoMessage() {}

func (x *BatchReEntryStudentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchReEntryStudentRsp.ProtoReflect.Descriptor instead.
func (*BatchReEntryStudentRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{25}
}

func (x *BatchReEntryStudentRsp) GetFailureList() []*StudentFailure {
	if x != nil {
		return x.FailureList
	}
	return nil
}

// 获取在园学生列表请求参数
type ListCurrentStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页
	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,3,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 家长姓名
	ParentName string `protobuf:"bytes,4,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"`
	// 家长手机号码
	Mobile string `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 年级班级id
	DeptId int64 `protobuf:"varint,6,opt,name=dept_id,json=deptId,proto3" json:"dept_id,omitempty"`
	// 入园起始时间
	EntryStartDate int64 `protobuf:"varint,7,opt,name=entry_start_date,json=entryStartDate,proto3" json:"entry_start_date,omitempty"`
	// 入园结束时间
	EntryEndDate int64 `protobuf:"varint,8,opt,name=entry_end_date,json=entryEndDate,proto3" json:"entry_end_date,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,9,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *ListCurrentStudentReq) Reset() {
	*x = ListCurrentStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCurrentStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCurrentStudentReq) ProtoMessage() {}

func (x *ListCurrentStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCurrentStudentReq.ProtoReflect.Descriptor instead.
func (*ListCurrentStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{26}
}

func (x *ListCurrentStudentReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListCurrentStudentReq) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListCurrentStudentReq) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *ListCurrentStudentReq) GetParentName() string {
	if x != nil {
		return x.ParentName
	}
	return ""
}

func (x *ListCurrentStudentReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *ListCurrentStudentReq) GetDeptId() int64 {
	if x != nil {
		return x.DeptId
	}
	return 0
}

func (x *ListCurrentStudentReq) GetEntryStartDate() int64 {
	if x != nil {
		return x.EntryStartDate
	}
	return 0
}

func (x *ListCurrentStudentReq) GetEntryEndDate() int64 {
	if x != nil {
		return x.EntryEndDate
	}
	return 0
}

func (x *ListCurrentStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 查询在园学生返回参数
type ListCurrentStudentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 当前页
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,3,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学生信息列表
	List []*StudentInfo `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListCurrentStudentRsp) Reset() {
	*x = ListCurrentStudentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCurrentStudentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCurrentStudentRsp) ProtoMessage() {}

func (x *ListCurrentStudentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCurrentStudentRsp.ProtoReflect.Descriptor instead.
func (*ListCurrentStudentRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{27}
}

func (x *ListCurrentStudentRsp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListCurrentStudentRsp) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListCurrentStudentRsp) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListCurrentStudentRsp) GetList() []*StudentInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 学生信息
type StudentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 学生头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	// 所在班级信息
	ClassInfo *ClassInfo `protobuf:"bytes,5,opt,name=class_info,json=classInfo,proto3" json:"class_info,omitempty"`
	// 入园时间，时间戳，单位：秒
	EntryDate int64 `protobuf:"varint,6,opt,name=entry_date,json=entryDate,proto3" json:"entry_date,omitempty"`
	// 家长联系人列表
	ParentList []*ContactInfo `protobuf:"bytes,7,rep,name=parent_list,json=parentList,proto3" json:"parent_list,omitempty"`
	// 学生来源 (1-后台添加、2-扫码入班、3-园丁端APP添加)
	Source int32 `protobuf:"varint,8,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *StudentInfo) Reset() {
	*x = StudentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StudentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StudentInfo) ProtoMessage() {}

func (x *StudentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StudentInfo.ProtoReflect.Descriptor instead.
func (*StudentInfo) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{28}
}

func (x *StudentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StudentInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StudentInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *StudentInfo) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *StudentInfo) GetClassInfo() *ClassInfo {
	if x != nil {
		return x.ClassInfo
	}
	return nil
}

func (x *StudentInfo) GetEntryDate() int64 {
	if x != nil {
		return x.EntryDate
	}
	return 0
}

func (x *StudentInfo) GetParentList() []*ContactInfo {
	if x != nil {
		return x.ParentList
	}
	return nil
}

func (x *StudentInfo) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

// 获取毕业学生列表请求参数
type ListGraduateStudentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页
	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,3,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 离园起始时间
	LeaveStartDate int64 `protobuf:"varint,4,opt,name=leave_start_date,json=leaveStartDate,proto3" json:"leave_start_date,omitempty"`
	// 离园结束时间
	LeaveEndDate int64 `protobuf:"varint,5,opt,name=leave_end_date,json=leaveEndDate,proto3" json:"leave_end_date,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,6,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *ListGraduateStudentReq) Reset() {
	*x = ListGraduateStudentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGraduateStudentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGraduateStudentReq) ProtoMessage() {}

func (x *ListGraduateStudentReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGraduateStudentReq.ProtoReflect.Descriptor instead.
func (*ListGraduateStudentReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{29}
}

func (x *ListGraduateStudentReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGraduateStudentReq) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListGraduateStudentReq) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *ListGraduateStudentReq) GetLeaveStartDate() int64 {
	if x != nil {
		return x.LeaveStartDate
	}
	return 0
}

func (x *ListGraduateStudentReq) GetLeaveEndDate() int64 {
	if x != nil {
		return x.LeaveEndDate
	}
	return 0
}

func (x *ListGraduateStudentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 查询离园学生返回参数
type ListGraduateStudentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 当前页
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,3,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学生信息列表
	List []*GraduateStudentInfo `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListGraduateStudentRsp) Reset() {
	*x = ListGraduateStudentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGraduateStudentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGraduateStudentRsp) ProtoMessage() {}

func (x *ListGraduateStudentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGraduateStudentRsp.ProtoReflect.Descriptor instead.
func (*ListGraduateStudentRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{30}
}

func (x *ListGraduateStudentRsp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListGraduateStudentRsp) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGraduateStudentRsp) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListGraduateStudentRsp) GetList() []*GraduateStudentInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 离园学生信息
type GraduateStudentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 学生头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	// 所在班级信息
	ClassInfo *ClassInfo `protobuf:"bytes,5,opt,name=class_info,json=classInfo,proto3" json:"class_info,omitempty"`
	// 离园时间，时间戳，单位：秒
	LeaveDate int64 `protobuf:"varint,6,opt,name=leave_date,json=leaveDate,proto3" json:"leave_date,omitempty"`
	// 离园原因
	LeaveReason string `protobuf:"bytes,7,opt,name=leave_reason,json=leaveReason,proto3" json:"leave_reason,omitempty"`
}

func (x *GraduateStudentInfo) Reset() {
	*x = GraduateStudentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GraduateStudentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GraduateStudentInfo) ProtoMessage() {}

func (x *GraduateStudentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GraduateStudentInfo.ProtoReflect.Descriptor instead.
func (*GraduateStudentInfo) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{31}
}

func (x *GraduateStudentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GraduateStudentInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GraduateStudentInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GraduateStudentInfo) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *GraduateStudentInfo) GetClassInfo() *ClassInfo {
	if x != nil {
		return x.ClassInfo
	}
	return nil
}

func (x *GraduateStudentInfo) GetLeaveDate() int64 {
	if x != nil {
		return x.LeaveDate
	}
	return 0
}

func (x *GraduateStudentInfo) GetLeaveReason() string {
	if x != nil {
		return x.LeaveReason
	}
	return ""
}

type ListStudentOnClassReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 是否离园(0-在园 1-离园)
	IsLeave uint32 `protobuf:"varint,2,opt,name=is_leave,json=isLeave,proto3" json:"is_leave,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 当前登录用户账号ID
	AccId int64 `protobuf:"varint,4,opt,name=acc_id,json=accId,proto3" json:"acc_id,omitempty"`
}

func (x *ListStudentOnClassReq) Reset() {
	*x = ListStudentOnClassReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStudentOnClassReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStudentOnClassReq) ProtoMessage() {}

func (x *ListStudentOnClassReq) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStudentOnClassReq.ProtoReflect.Descriptor instead.
func (*ListStudentOnClassReq) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{32}
}

func (x *ListStudentOnClassReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *ListStudentOnClassReq) GetIsLeave() uint32 {
	if x != nil {
		return x.IsLeave
	}
	return 0
}

func (x *ListStudentOnClassReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListStudentOnClassReq) GetAccId() int64 {
	if x != nil {
		return x.AccId
	}
	return 0
}

type ListStudentOnClassRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生列表
	List []*ListStudentOnClassRsp_BasicInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListStudentOnClassRsp) Reset() {
	*x = ListStudentOnClassRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStudentOnClassRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStudentOnClassRsp) ProtoMessage() {}

func (x *ListStudentOnClassRsp) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStudentOnClassRsp.ProtoReflect.Descriptor instead.
func (*ListStudentOnClassRsp) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{33}
}

func (x *ListStudentOnClassRsp) GetList() []*ListStudentOnClassRsp_BasicInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type GetFullStudentTreeRsp_Dept struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level uint32                        `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"` // level可选值： 1-年级 2-班级 3-学生
	Id    string                        `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Name  string                        `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Child []*GetFullStudentTreeRsp_Dept `protobuf:"bytes,4,rep,name=child,proto3" json:"child,omitempty"`
}

func (x *GetFullStudentTreeRsp_Dept) Reset() {
	*x = GetFullStudentTreeRsp_Dept{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFullStudentTreeRsp_Dept) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFullStudentTreeRsp_Dept) ProtoMessage() {}

func (x *GetFullStudentTreeRsp_Dept) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFullStudentTreeRsp_Dept.ProtoReflect.Descriptor instead.
func (*GetFullStudentTreeRsp_Dept) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{10, 0}
}

func (x *GetFullStudentTreeRsp_Dept) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *GetFullStudentTreeRsp_Dept) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetFullStudentTreeRsp_Dept) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetFullStudentTreeRsp_Dept) GetChild() []*GetFullStudentTreeRsp_Dept {
	if x != nil {
		return x.Child
	}
	return nil
}

type BatchShiftStudentReq_ShiftStudent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	StudentId int64 `protobuf:"varint,1,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 新班级id
	NewClassId int64 `protobuf:"varint,2,opt,name=new_class_id,json=newClassId,proto3" json:"new_class_id,omitempty"`
}

func (x *BatchShiftStudentReq_ShiftStudent) Reset() {
	*x = BatchShiftStudentReq_ShiftStudent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchShiftStudentReq_ShiftStudent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchShiftStudentReq_ShiftStudent) ProtoMessage() {}

func (x *BatchShiftStudentReq_ShiftStudent) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchShiftStudentReq_ShiftStudent.ProtoReflect.Descriptor instead.
func (*BatchShiftStudentReq_ShiftStudent) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{20, 0}
}

func (x *BatchShiftStudentReq_ShiftStudent) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *BatchShiftStudentReq_ShiftStudent) GetNewClassId() int64 {
	if x != nil {
		return x.NewClassId
	}
	return 0
}

type ListStudentOnClassRsp_BasicInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 学生头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	// 学生关联的家长数
	ParentCount uint32 `protobuf:"varint,5,opt,name=parent_count,json=parentCount,proto3" json:"parent_count,omitempty"`
	// 激活状态(0-未激活 1-已激活)
	ActivateStatus uint32 `protobuf:"varint,6,opt,name=activate_status,json=activateStatus,proto3" json:"activate_status,omitempty"`
}

func (x *ListStudentOnClassRsp_BasicInfo) Reset() {
	*x = ListStudentOnClassRsp_BasicInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_student_v1_student_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStudentOnClassRsp_BasicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStudentOnClassRsp_BasicInfo) ProtoMessage() {}

func (x *ListStudentOnClassRsp_BasicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_student_v1_student_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStudentOnClassRsp_BasicInfo.ProtoReflect.Descriptor instead.
func (*ListStudentOnClassRsp_BasicInfo) Descriptor() ([]byte, []int) {
	return file_student_v1_student_proto_rawDescGZIP(), []int{33, 0}
}

func (x *ListStudentOnClassRsp_BasicInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListStudentOnClassRsp_BasicInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListStudentOnClassRsp_BasicInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ListStudentOnClassRsp_BasicInfo) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *ListStudentOnClassRsp_BasicInfo) GetParentCount() uint32 {
	if x != nil {
		return x.ParentCount
	}
	return 0
}

func (x *ListStudentOnClassRsp_BasicInfo) GetActivateStatus() uint32 {
	if x != nil {
		return x.ActivateStatus
	}
	return 0
}

var File_student_v1_student_proto protoreflect.FileDescriptor

var file_student_v1_student_proto_rawDesc = []byte{
	0x0a, 0x18, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xed, 0x01, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x2c, 0x0a, 0x12, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x72, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73,
	0x22, 0x41, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x5f, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x64, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x73, 0x74, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x73,
	0x74, 0x75, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x64, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x57, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69,
	0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x22, 0x49, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x37, 0x0a, 0x11, 0x53,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x54, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x63, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x63, 0x63, 0x49, 0x64, 0x22, 0x6f, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x29, 0x0a, 0x10, 0x75, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x75, 0x6e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x54, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x6c, 0x65, 0x61, 0x76, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x22, 0xde, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x05, 0x63,
	0x68, 0x69, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46,
	0x75, 0x6c, 0x6c, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x73,
	0x70, 0x2e, 0x44, 0x65, 0x70, 0x74, 0x52, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x1a, 0x82, 0x01,
	0x0a, 0x04, 0x44, 0x65, 0x70, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x40, 0x0a, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x65, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x44, 0x65, 0x70, 0x74, 0x52, 0x05, 0x63, 0x68, 0x69,
	0x6c, 0x64, 0x22, 0x4a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x07,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0xae,
	0x03, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f, 0x6c, 0x79, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6f, 0x6c, 0x79, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x69, 0x73, 0x5f, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x65, 0x61, 0x76,
	0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6c,
	0x65, 0x61, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x7f, 0x0a, 0x09, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xae, 0x04, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x00, 0x30, 0x01, 0x30,
	0x02, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72,
	0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69, 0x72,
	0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72, 0x79,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x09, 0x70, 0x6f, 0x6c, 0x79,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x32, 0x52, 0x09, 0x70, 0x6f, 0x6c, 0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12,
	0x46, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x10, 0x0a, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0d,
	0xfa, 0x42, 0x0a, 0x2a, 0x08, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x30, 0x04, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30,
	0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28,
	0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69,
	0x70, 0x22, 0xc5, 0x02, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x25, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x18, 0x14, 0x28, 0x01, 0x52, 0x08,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x07, 0x72, 0x65, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x03, 0x52, 0x06, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d,
	0x72, 0x0b, 0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31, 0x30, 0x7d, 0x24, 0x52, 0x06, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x00,
	0x30, 0x01, 0x52, 0x07, 0x69, 0x73, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x00, 0x30, 0x01, 0x52, 0x0b, 0x69, 0x73,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xae, 0x03, 0x0a, 0x19, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x69, 0x63,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x00, 0x30, 0x01, 0x30, 0x02, 0x52, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x25, 0x0a, 0x09, 0x70, 0x6f, 0x6c, 0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x09, 0x70, 0x6f, 0x6c,
	0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x20,
	0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xef, 0x01, 0x0a, 0x15, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03,
	0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x5a, 0x0a, 0x15,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x0b, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x4c, 0x0a, 0x0e, 0x53, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x8e, 0x03, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x68, 0x69, 0x66, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x57, 0x0a, 0x08, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x68, 0x69, 0x66, 0x74, 0x53, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x53, 0x68, 0x69, 0x66, 0x74, 0x53, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x08,
	0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14,
	0x52, 0x02, 0x69, 0x70, 0x1a, 0x61, 0x0a, 0x0c, 0x53, 0x68, 0x69, 0x66, 0x74, 0x53, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0c,
	0x6e, 0x65, 0x77, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6e, 0x65, 0x77,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x68, 0x69, 0x66, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x41, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x91, 0x02, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x61, 0x76,
	0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x32, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x59, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c,
	0x65, 0x61, 0x76, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x41,
	0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x94, 0x02, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x03,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x5b, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x41, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xad, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65,
	0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x64, 0x65, 0x70,
	0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x65,
	0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69,
	0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0x8d, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72,
	0x50, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x90, 0x02, 0x0a, 0x0b, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0a, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xee, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73,
	0x74, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x22, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x07, 0x70, 0x65, 0x72,
	0x50, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x65, 0x61, 0x76, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6c, 0x65, 0x61, 0x76, 0x65,
	0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0x96, 0x01, 0x0a, 0x16, 0x4c, 0x69,
	0x73, 0x74, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0xe5, 0x01, 0x0a, 0x13, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x38,
	0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x65, 0x61, 0x76,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x65,
	0x61, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x65, 0x61, 0x76, 0x65,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c,
	0x65, 0x61, 0x76, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x9a, 0x01, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x6c,
	0x65, 0x61, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a,
	0x04, 0x30, 0x00, 0x30, 0x01, 0x52, 0x07, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x12, 0x20,
	0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x63, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x61, 0x63, 0x63, 0x49, 0x64, 0x22, 0x8a, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x43, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0xab, 0x01, 0x0a, 0x09, 0x42, 0x61, 0x73, 0x69, 0x63,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x32, 0xc5, 0x0b, 0x0a, 0x07, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x12, 0x4c, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4b,
	0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x12,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x16, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x69,
	0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x12, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x61, 0x0a, 0x11, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x68, 0x69, 0x66, 0x74, 0x53, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x68, 0x69, 0x66,
	0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x68, 0x69, 0x66, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x11, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x61, 0x76,
	0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c,
	0x65, 0x61, 0x76, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x64, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x61,
	0x64, 0x75, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61, 0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x64,
	0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x4f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x53,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46,
	0x75, 0x6c, 0x6c, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a,
	0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x0d,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x64, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x64, 0x46, 0x61,
	0x6d, 0x69, 0x6c, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x55, 0x6e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x64, 0x46, 0x61, 0x6d, 0x69, 0x6c,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x0b, 0x46,
	0x69, 0x6e, 0x64, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x13, 0x5a, 0x11,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_student_v1_student_proto_rawDescOnce sync.Once
	file_student_v1_student_proto_rawDescData = file_student_v1_student_proto_rawDesc
)

func file_student_v1_student_proto_rawDescGZIP() []byte {
	file_student_v1_student_proto_rawDescOnce.Do(func() {
		file_student_v1_student_proto_rawDescData = protoimpl.X.CompressGZIP(file_student_v1_student_proto_rawDescData)
	})
	return file_student_v1_student_proto_rawDescData
}

var file_student_v1_student_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_student_v1_student_proto_goTypes = []interface{}{
	(*FindStudentReq)(nil),                    // 0: api.student.v1.FindStudentReq
	(*FindStudentRsp)(nil),                    // 1: api.student.v1.FindStudentRsp
	(*ListUnInvitedFamilyGroupReq)(nil),       // 2: api.student.v1.ListUnInvitedFamilyGroupReq
	(*ListUnInvitedFamilyGroupRsp)(nil),       // 3: api.student.v1.ListUnInvitedFamilyGroupRsp
	(*SearchStudentReq)(nil),                  // 4: api.student.v1.SearchStudentReq
	(*SearchStudentRsp)(nil),                  // 5: api.student.v1.SearchStudentRsp
	(*SimpleStudentInfo)(nil),                 // 6: api.student.v1.SimpleStudentInfo
	(*GetStudentActivateInfoReq)(nil),         // 7: api.student.v1.GetStudentActivateInfoReq
	(*GetStudentActivateInfoRsp)(nil),         // 8: api.student.v1.GetStudentActivateInfoRsp
	(*GetFullStudentTreeReq)(nil),             // 9: api.student.v1.GetFullStudentTreeReq
	(*GetFullStudentTreeRsp)(nil),             // 10: api.student.v1.GetFullStudentTreeRsp
	(*GetStudentReq)(nil),                     // 11: api.student.v1.GetStudentReq
	(*GetStudentRsp)(nil),                     // 12: api.student.v1.GetStudentRsp
	(*ClassInfo)(nil),                         // 13: api.student.v1.ClassInfo
	(*CreateStudentReq)(nil),                  // 14: api.student.v1.CreateStudentReq
	(*ContactInfo)(nil),                       // 15: api.student.v1.ContactInfo
	(*UpdateStudentBasicInfoReq)(nil),         // 16: api.student.v1.UpdateStudentBasicInfoReq
	(*BatchDeleteStudentReq)(nil),             // 17: api.student.v1.BatchDeleteStudentReq
	(*BatchDeleteStudentRsp)(nil),             // 18: api.student.v1.BatchDeleteStudentRsp
	(*StudentFailure)(nil),                    // 19: api.student.v1.StudentFailure
	(*BatchShiftStudentReq)(nil),              // 20: api.student.v1.BatchShiftStudentReq
	(*BatchShiftStudentRsp)(nil),              // 21: api.student.v1.BatchShiftStudentRsp
	(*BatchLeaveStudentReq)(nil),              // 22: api.student.v1.BatchLeaveStudentReq
	(*BatchLeaveStudentRsp)(nil),              // 23: api.student.v1.BatchLeaveStudentRsp
	(*BatchReEntryStudentReq)(nil),            // 24: api.student.v1.BatchReEntryStudentReq
	(*BatchReEntryStudentRsp)(nil),            // 25: api.student.v1.BatchReEntryStudentRsp
	(*ListCurrentStudentReq)(nil),             // 26: api.student.v1.ListCurrentStudentReq
	(*ListCurrentStudentRsp)(nil),             // 27: api.student.v1.ListCurrentStudentRsp
	(*StudentInfo)(nil),                       // 28: api.student.v1.StudentInfo
	(*ListGraduateStudentReq)(nil),            // 29: api.student.v1.ListGraduateStudentReq
	(*ListGraduateStudentRsp)(nil),            // 30: api.student.v1.ListGraduateStudentRsp
	(*GraduateStudentInfo)(nil),               // 31: api.student.v1.GraduateStudentInfo
	(*ListStudentOnClassReq)(nil),             // 32: api.student.v1.ListStudentOnClassReq
	(*ListStudentOnClassRsp)(nil),             // 33: api.student.v1.ListStudentOnClassRsp
	(*GetFullStudentTreeRsp_Dept)(nil),        // 34: api.student.v1.GetFullStudentTreeRsp.Dept
	(*BatchShiftStudentReq_ShiftStudent)(nil), // 35: api.student.v1.BatchShiftStudentReq.ShiftStudent
	(*ListStudentOnClassRsp_BasicInfo)(nil),   // 36: api.student.v1.ListStudentOnClassRsp.BasicInfo
	(*emptypb.Empty)(nil),                     // 37: google.protobuf.Empty
}
var file_student_v1_student_proto_depIdxs = []int32{
	28, // 0: api.student.v1.FindStudentRsp.list:type_name -> api.student.v1.StudentInfo
	6,  // 1: api.student.v1.SearchStudentRsp.list:type_name -> api.student.v1.SimpleStudentInfo
	34, // 2: api.student.v1.GetFullStudentTreeRsp.child:type_name -> api.student.v1.GetFullStudentTreeRsp.Dept
	13, // 3: api.student.v1.GetStudentRsp.class_info:type_name -> api.student.v1.ClassInfo
	15, // 4: api.student.v1.GetStudentRsp.parent_list:type_name -> api.student.v1.ContactInfo
	15, // 5: api.student.v1.CreateStudentReq.parent_list:type_name -> api.student.v1.ContactInfo
	19, // 6: api.student.v1.BatchDeleteStudentRsp.failure_list:type_name -> api.student.v1.StudentFailure
	35, // 7: api.student.v1.BatchShiftStudentReq.students:type_name -> api.student.v1.BatchShiftStudentReq.ShiftStudent
	19, // 8: api.student.v1.BatchShiftStudentRsp.failure_list:type_name -> api.student.v1.StudentFailure
	19, // 9: api.student.v1.BatchLeaveStudentRsp.failure_list:type_name -> api.student.v1.StudentFailure
	19, // 10: api.student.v1.BatchReEntryStudentRsp.failure_list:type_name -> api.student.v1.StudentFailure
	28, // 11: api.student.v1.ListCurrentStudentRsp.list:type_name -> api.student.v1.StudentInfo
	13, // 12: api.student.v1.StudentInfo.class_info:type_name -> api.student.v1.ClassInfo
	15, // 13: api.student.v1.StudentInfo.parent_list:type_name -> api.student.v1.ContactInfo
	31, // 14: api.student.v1.ListGraduateStudentRsp.list:type_name -> api.student.v1.GraduateStudentInfo
	13, // 15: api.student.v1.GraduateStudentInfo.class_info:type_name -> api.student.v1.ClassInfo
	36, // 16: api.student.v1.ListStudentOnClassRsp.list:type_name -> api.student.v1.ListStudentOnClassRsp.BasicInfo
	34, // 17: api.student.v1.GetFullStudentTreeRsp.Dept.child:type_name -> api.student.v1.GetFullStudentTreeRsp.Dept
	11, // 18: api.student.v1.Student.GetStudent:input_type -> api.student.v1.GetStudentReq
	14, // 19: api.student.v1.Student.CreateStudent:input_type -> api.student.v1.CreateStudentReq
	16, // 20: api.student.v1.Student.UpdateStudentBasicInfo:input_type -> api.student.v1.UpdateStudentBasicInfoReq
	17, // 21: api.student.v1.Student.BatchDeleteStudent:input_type -> api.student.v1.BatchDeleteStudentReq
	20, // 22: api.student.v1.Student.BatchShiftStudent:input_type -> api.student.v1.BatchShiftStudentReq
	22, // 23: api.student.v1.Student.BatchLeaveStudent:input_type -> api.student.v1.BatchLeaveStudentReq
	24, // 24: api.student.v1.Student.BatchReEntryStudent:input_type -> api.student.v1.BatchReEntryStudentReq
	26, // 25: api.student.v1.Student.ListCurrentStudent:input_type -> api.student.v1.ListCurrentStudentReq
	29, // 26: api.student.v1.Student.ListGraduateStudent:input_type -> api.student.v1.ListGraduateStudentReq
	32, // 27: api.student.v1.Student.ListStudentOnClass:input_type -> api.student.v1.ListStudentOnClassReq
	9,  // 28: api.student.v1.Student.GetFullStudentTree:input_type -> api.student.v1.GetFullStudentTreeReq
	7,  // 29: api.student.v1.Student.GetStudentActivateInfo:input_type -> api.student.v1.GetStudentActivateInfoReq
	4,  // 30: api.student.v1.Student.SearchStudent:input_type -> api.student.v1.SearchStudentReq
	2,  // 31: api.student.v1.Student.ListUnInvitedFamilyGroup:input_type -> api.student.v1.ListUnInvitedFamilyGroupReq
	0,  // 32: api.student.v1.Student.FindStudent:input_type -> api.student.v1.FindStudentReq
	12, // 33: api.student.v1.Student.GetStudent:output_type -> api.student.v1.GetStudentRsp
	37, // 34: api.student.v1.Student.CreateStudent:output_type -> google.protobuf.Empty
	37, // 35: api.student.v1.Student.UpdateStudentBasicInfo:output_type -> google.protobuf.Empty
	18, // 36: api.student.v1.Student.BatchDeleteStudent:output_type -> api.student.v1.BatchDeleteStudentRsp
	21, // 37: api.student.v1.Student.BatchShiftStudent:output_type -> api.student.v1.BatchShiftStudentRsp
	23, // 38: api.student.v1.Student.BatchLeaveStudent:output_type -> api.student.v1.BatchLeaveStudentRsp
	25, // 39: api.student.v1.Student.BatchReEntryStudent:output_type -> api.student.v1.BatchReEntryStudentRsp
	27, // 40: api.student.v1.Student.ListCurrentStudent:output_type -> api.student.v1.ListCurrentStudentRsp
	30, // 41: api.student.v1.Student.ListGraduateStudent:output_type -> api.student.v1.ListGraduateStudentRsp
	33, // 42: api.student.v1.Student.ListStudentOnClass:output_type -> api.student.v1.ListStudentOnClassRsp
	10, // 43: api.student.v1.Student.GetFullStudentTree:output_type -> api.student.v1.GetFullStudentTreeRsp
	8,  // 44: api.student.v1.Student.GetStudentActivateInfo:output_type -> api.student.v1.GetStudentActivateInfoRsp
	5,  // 45: api.student.v1.Student.SearchStudent:output_type -> api.student.v1.SearchStudentRsp
	3,  // 46: api.student.v1.Student.ListUnInvitedFamilyGroup:output_type -> api.student.v1.ListUnInvitedFamilyGroupRsp
	1,  // 47: api.student.v1.Student.FindStudent:output_type -> api.student.v1.FindStudentRsp
	33, // [33:48] is the sub-list for method output_type
	18, // [18:33] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_student_v1_student_proto_init() }
func file_student_v1_student_proto_init() {
	if File_student_v1_student_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_student_v1_student_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStudentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnInvitedFamilyGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnInvitedFamilyGroupRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchStudentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimpleStudentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStudentActivateInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStudentActivateInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFullStudentTreeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFullStudentTreeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStudentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContactInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStudentBasicInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteStudentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StudentFailure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchShiftStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchShiftStudentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchLeaveStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchLeaveStudentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchReEntryStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchReEntryStudentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCurrentStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCurrentStudentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StudentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGraduateStudentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGraduateStudentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GraduateStudentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStudentOnClassReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStudentOnClassRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFullStudentTreeRsp_Dept); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchShiftStudentReq_ShiftStudent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_student_v1_student_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStudentOnClassRsp_BasicInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_student_v1_student_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_student_v1_student_proto_goTypes,
		DependencyIndexes: file_student_v1_student_proto_depIdxs,
		MessageInfos:      file_student_v1_student_proto_msgTypes,
	}.Build()
	File_student_v1_student_proto = out.File
	file_student_v1_student_proto_rawDesc = nil
	file_student_v1_student_proto_goTypes = nil
	file_student_v1_student_proto_depIdxs = nil
}
