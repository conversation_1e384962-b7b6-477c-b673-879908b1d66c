// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package v1

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 学生不存在或已删除
func IsContactStudentNotExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_STUDENT_NOT_EXISTS.String() && e.Code == 400
}

// 学生不存在或已删除
func ErrorContactStudentNotExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_STUDENT_NOT_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 同一班级下存在同名学生
func IsContactStudentNameAlreadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_STUDENT_NAME_ALREADY_EXISTS.String() && e.Code == 400
}

// 同一班级下存在同名学生
func ErrorContactStudentNameAlreadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_STUDENT_NAME_ALREADY_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 家长手机号重复
func IsContactParentMobileAlreadyExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_PARENT_MOBILE_ALREADY_EXISTS.String() && e.Code == 400
}

// 家长手机号重复
func ErrorContactParentMobileAlreadyExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_PARENT_MOBILE_ALREADY_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 学生家长关联记录不存在
func IsContactStudentParentRelNotExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_STUDENT_PARENT_REL_NOT_EXISTS.String() && e.Code == 400
}

// 学生家长关联记录不存在
func ErrorContactStudentParentRelNotExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_STUDENT_PARENT_REL_NOT_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 家长不存在
func IsContactParentNotExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_PARENT_NOT_EXISTS.String() && e.Code == 400
}

// 家长不存在
func ErrorContactParentNotExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_PARENT_NOT_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 学生与班级关联记录不存在
func IsContactStudentClassRelNotExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_STUDENT_CLASS_REL_NOT_EXISTS.String() && e.Code == 400
}

// 学生与班级关联记录不存在
func ErrorContactStudentClassRelNotExists(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_STUDENT_CLASS_REL_NOT_EXISTS.String(), fmt.Sprintf(format, args...))
}

// 调班前后班级相同
func IsContactShiftClassSame(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_SHIFT_CLASS_SAME.String() && e.Code == 400
}

// 调班前后班级相同
func ErrorContactShiftClassSame(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_SHIFT_CLASS_SAME.String(), fmt.Sprintf(format, args...))
}

// 学生已离园
func IsContactStudentHadLeaved(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_STUDENT_HAD_LEAVED.String() && e.Code == 400
}

// 学生已离园
func ErrorContactStudentHadLeaved(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_STUDENT_HAD_LEAVED.String(), fmt.Sprintf(format, args...))
}

// 学生未离园
func IsContactStudentNotLeave(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_STUDENT_NOT_LEAVE.String() && e.Code == 400
}

// 学生未离园
func ErrorContactStudentNotLeave(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_STUDENT_NOT_LEAVE.String(), fmt.Sprintf(format, args...))
}

// 没有该班级的权限
func IsContactNoClassAuth(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_NO_CLASS_AUTH.String() && e.Code == 400
}

// 没有该班级的权限
func ErrorContactNoClassAuth(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_NO_CLASS_AUTH.String(), fmt.Sprintf(format, args...))
}

// 关系已存在
func IsContactRelationExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CONTACT_RELATION_EXIST.String() && e.Code == 400
}

// 关系已存在
func ErrorContactRelationExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CONTACT_RELATION_EXIST.String(), fmt.Sprintf(format, args...))
}
