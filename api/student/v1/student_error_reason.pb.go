// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.17.3
// source: student/v1/student_error_reason.proto

package v1

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	// 学生不存在或已删除
	ErrorReason_CONTACT_STUDENT_NOT_EXISTS ErrorReason = 0
	// 同一班级下存在同名学生
	ErrorReason_CONTACT_STUDENT_NAME_ALREADY_EXISTS ErrorReason = 1
	// 家长手机号重复
	ErrorReason_CONTACT_PARENT_MOBILE_ALREADY_EXISTS ErrorReason = 2
	// 学生家长关联记录不存在
	ErrorReason_CONTACT_STUDENT_PARENT_REL_NOT_EXISTS ErrorReason = 3
	// 家长不存在
	ErrorReason_CONTACT_PARENT_NOT_EXISTS ErrorReason = 4
	// 学生与班级关联记录不存在
	ErrorReason_CONTACT_STUDENT_CLASS_REL_NOT_EXISTS ErrorReason = 5
	// 调班前后班级相同
	ErrorReason_CONTACT_SHIFT_CLASS_SAME ErrorReason = 6
	// 学生已离园
	ErrorReason_CONTACT_STUDENT_HAD_LEAVED ErrorReason = 7
	// 学生未离园
	ErrorReason_CONTACT_STUDENT_NOT_LEAVE ErrorReason = 8
	// 没有该班级的权限
	ErrorReason_CONTACT_NO_CLASS_AUTH ErrorReason = 9
	// 关系已存在
	ErrorReason_CONTACT_RELATION_EXIST ErrorReason = 10
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0:  "CONTACT_STUDENT_NOT_EXISTS",
		1:  "CONTACT_STUDENT_NAME_ALREADY_EXISTS",
		2:  "CONTACT_PARENT_MOBILE_ALREADY_EXISTS",
		3:  "CONTACT_STUDENT_PARENT_REL_NOT_EXISTS",
		4:  "CONTACT_PARENT_NOT_EXISTS",
		5:  "CONTACT_STUDENT_CLASS_REL_NOT_EXISTS",
		6:  "CONTACT_SHIFT_CLASS_SAME",
		7:  "CONTACT_STUDENT_HAD_LEAVED",
		8:  "CONTACT_STUDENT_NOT_LEAVE",
		9:  "CONTACT_NO_CLASS_AUTH",
		10: "CONTACT_RELATION_EXIST",
	}
	ErrorReason_value = map[string]int32{
		"CONTACT_STUDENT_NOT_EXISTS":            0,
		"CONTACT_STUDENT_NAME_ALREADY_EXISTS":   1,
		"CONTACT_PARENT_MOBILE_ALREADY_EXISTS":  2,
		"CONTACT_STUDENT_PARENT_REL_NOT_EXISTS": 3,
		"CONTACT_PARENT_NOT_EXISTS":             4,
		"CONTACT_STUDENT_CLASS_REL_NOT_EXISTS":  5,
		"CONTACT_SHIFT_CLASS_SAME":              6,
		"CONTACT_STUDENT_HAD_LEAVED":            7,
		"CONTACT_STUDENT_NOT_LEAVE":             8,
		"CONTACT_NO_CLASS_AUTH":                 9,
		"CONTACT_RELATION_EXIST":                10,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_student_v1_student_error_reason_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_student_v1_student_error_reason_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_student_v1_student_error_reason_proto_rawDescGZIP(), []int{0}
}

var File_student_v1_student_error_reason_proto protoreflect.FileDescriptor

var file_student_v1_student_error_reason_proto_rawDesc = []byte{
	0x0a, 0x25, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x13, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0xd0, 0x03, 0x0a,
	0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x1a,
	0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x55, 0x44, 0x45, 0x4e, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x00, 0x1a, 0x04, 0xa8, 0x45,
	0x90, 0x03, 0x12, 0x2d, 0x0a, 0x23, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x54,
	0x55, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41,
	0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x01, 0x1a, 0x04, 0xa8, 0x45, 0x90,
	0x03, 0x12, 0x2e, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x52,
	0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41,
	0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x02, 0x1a, 0x04, 0xa8, 0x45, 0x90,
	0x03, 0x12, 0x2f, 0x0a, 0x25, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x55,
	0x44, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4c, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x03, 0x1a, 0x04, 0xa8, 0x45,
	0x90, 0x03, 0x12, 0x23, 0x0a, 0x19, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x50, 0x41,
	0x52, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10,
	0x04, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x2e, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x54, 0x41,
	0x43, 0x54, 0x5f, 0x53, 0x54, 0x55, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53,
	0x5f, 0x52, 0x45, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10,
	0x05, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x22, 0x0a, 0x18, 0x43, 0x4f, 0x4e, 0x54, 0x41,
	0x43, 0x54, 0x5f, 0x53, 0x48, 0x49, 0x46, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x53,
	0x41, 0x4d, 0x45, 0x10, 0x06, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x24, 0x0a, 0x1a, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x55, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x48,
	0x41, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x56, 0x45, 0x44, 0x10, 0x07, 0x1a, 0x04, 0xa8, 0x45, 0x90,
	0x03, 0x12, 0x23, 0x0a, 0x19, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x55,
	0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x45, 0x41, 0x56, 0x45, 0x10, 0x08,
	0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x1f, 0x0a, 0x15, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43,
	0x54, 0x5f, 0x4e, 0x4f, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x10,
	0x09, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x20, 0x0a, 0x16, 0x43, 0x4f, 0x4e, 0x54, 0x41,
	0x43, 0x54, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x10, 0x0a, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x1a, 0x04, 0xa0, 0x45, 0xf4, 0x03, 0x42,
	0x13, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_student_v1_student_error_reason_proto_rawDescOnce sync.Once
	file_student_v1_student_error_reason_proto_rawDescData = file_student_v1_student_error_reason_proto_rawDesc
)

func file_student_v1_student_error_reason_proto_rawDescGZIP() []byte {
	file_student_v1_student_error_reason_proto_rawDescOnce.Do(func() {
		file_student_v1_student_error_reason_proto_rawDescData = protoimpl.X.CompressGZIP(file_student_v1_student_error_reason_proto_rawDescData)
	})
	return file_student_v1_student_error_reason_proto_rawDescData
}

var file_student_v1_student_error_reason_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_student_v1_student_error_reason_proto_goTypes = []interface{}{
	(ErrorReason)(0), // 0: api.student.v1.ErrorReason
}
var file_student_v1_student_error_reason_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_student_v1_student_error_reason_proto_init() }
func file_student_v1_student_error_reason_proto_init() {
	if File_student_v1_student_error_reason_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_student_v1_student_error_reason_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_student_v1_student_error_reason_proto_goTypes,
		DependencyIndexes: file_student_v1_student_error_reason_proto_depIdxs,
		EnumInfos:         file_student_v1_student_error_reason_proto_enumTypes,
	}.Build()
	File_student_v1_student_error_reason_proto = out.File
	file_student_v1_student_error_reason_proto_rawDesc = nil
	file_student_v1_student_error_reason_proto_goTypes = nil
	file_student_v1_student_error_reason_proto_depIdxs = nil
}
