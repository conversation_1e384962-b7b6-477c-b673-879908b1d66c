syntax = "proto3";

package api.student.v1;

import "errors/errors.proto";

option go_package = "api/student/v1;v1";

enum ErrorReason {
  // 设置缺省错误码
  option (errors.default_code) = 500;

  // 学生不存在或已删除
  CONTACT_STUDENT_NOT_EXISTS = 0 [(errors.code) = 400];

  // 同一班级下存在同名学生
  CONTACT_STUDENT_NAME_ALREADY_EXISTS = 1 [(errors.code) = 400];

  // 家长手机号重复
  CONTACT_PARENT_MOBILE_ALREADY_EXISTS = 2 [(errors.code) = 400];

  // 学生家长关联记录不存在
  CONTACT_STUDENT_PARENT_REL_NOT_EXISTS = 3 [(errors.code) = 400];

  // 家长不存在
  CONTACT_PARENT_NOT_EXISTS = 4 [(errors.code) = 400];

  // 学生与班级关联记录不存在
  CONTACT_STUDENT_CLASS_REL_NOT_EXISTS = 5 [(errors.code) = 400];

  // 调班前后班级相同
  CONTACT_SHIFT_CLASS_SAME = 6 [(errors.code) = 400];

  // 学生已离园
  CONTACT_STUDENT_HAD_LEAVED = 7 [(errors.code) = 400];

  // 学生未离园
  CONTACT_STUDENT_NOT_LEAVE = 8 [(errors.code) = 400];

  // 没有该班级的权限
  CONTACT_NO_CLASS_AUTH = 9 [(errors.code) = 400];

  // 关系已存在
  CONTACT_RELATION_EXIST = 10 [(errors.code) = 400];
}
