syntax = "proto3";

package api.student.v1;

option go_package = "api/student/v1;v1";

import "google/protobuf/empty.proto";
import "validate/validate.proto";

service Student {
  // 获取学生信息
  rpc GetStudent (GetStudentReq) returns (GetStudentRsp) {}

  // 添加学生信息
  rpc CreateStudent (CreateStudentReq) returns (google.protobuf.Empty) {}

  // 更新学生基本信息
  rpc UpdateStudentBasicInfo(UpdateStudentBasicInfoReq) returns (google.protobuf.Empty) {}

  // 批量删除学生
  rpc BatchDeleteStudent(BatchDeleteStudentReq) returns (BatchDeleteStudentRsp) {}

  // 批量调班
  rpc BatchShiftStudent(BatchShiftStudentReq) returns (BatchShiftStudentRsp) {}

  // 批量离园学生
  rpc BatchLeaveStudent(BatchLeaveStudentReq) returns (BatchLeaveStudentRsp) {}

  // 批量重新入园
  rpc BatchReEntryStudent(BatchReEntryStudentReq) returns (BatchReEntryStudentRsp) {}

  // 导入学生信息
  // rpc ImportStudent(ImportStudentReq) returns (ImportStudentRsp) {}

  // 获取在园学生列表
  rpc ListCurrentStudent(ListCurrentStudentReq) returns (ListCurrentStudentRsp) {}

  // 获取毕业学生列表
  rpc ListGraduateStudent(ListGraduateStudentReq) returns (ListGraduateStudentRsp) {}

  // 获取指定班级下学生列表
  rpc ListStudentOnClass(ListStudentOnClassReq) returns (ListStudentOnClassRsp) {}

  // 获取完整的学生树（包括年级班级和学生）
  rpc GetFullStudentTree(GetFullStudentTreeReq) returns (GetFullStudentTreeRsp) {}

  // 获取学生的激活数和未激活数
  rpc GetStudentActivateInfo(GetStudentActivateInfoReq) returns (GetStudentActivateInfoRsp) {}

  // 搜索学生
  rpc SearchStudent(SearchStudentReq) returns (SearchStudentRsp) {}

  // 获取学生下可邀请的亲友团成员列表
  rpc ListUnInvitedFamilyGroup(ListUnInvitedFamilyGroupReq) returns (ListUnInvitedFamilyGroupRsp) {}

  // 条件查询在职学生信息（无权限判断，供内部服务使用）
  rpc FindStudent(FindStudentReq) returns (FindStudentRsp) {}
}

message FindStudentReq {
  // 是否返回班级信息
  bool return_class_info = 1;
  // 是否返回家长信息
  bool return_parent_info = 2;
  // 学校id，必填。
  int64 inst_id = 3 [(validate.rules).int64.gt = 0];
  // 学生id
  repeated int64 student_ids = 4;
  // 学生姓名
  string student_name = 5;
  // 班级id
  repeated int64 class_ids = 6;
}

message FindStudentRsp {
  // 学生列表
  repeated StudentInfo list = 1;
}

message ListUnInvitedFamilyGroupReq {
  // 学校ID
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 学生ID
  int64 stu_id = 2 [(validate.rules).int64.gt = 0];
}
message ListUnInvitedFamilyGroupRsp {
  repeated uint32 list = 1;
}

message SearchStudentReq {
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 搜索关键词
  string keyword = 2 [(validate.rules).string.min_len = 1];
}

message SearchStudentRsp {
  // 搜索结果列表
  repeated SimpleStudentInfo list = 1;
}

message SimpleStudentInfo {
  // 学生id
  int64 id = 1;
  // 学生姓名
  string name = 2;
}

message GetStudentActivateInfoReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 当前登录用户账号ID
  int64 acc_id = 2;
}

message GetStudentActivateInfoRsp {
  // 激活数
  uint32 activated_count = 1;
  // 未激活数
  uint32 unactivate_count = 2;
}

message GetFullStudentTreeReq {
  // 学生是否离园
  uint32 is_leave = 1;
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
}

message GetFullStudentTreeRsp {
  message Dept {
    uint32 level = 1; // level可选值： 1-年级 2-班级 3-学生
    string id = 2;
    string name = 3;
    repeated Dept child = 4;
  }
  repeated Dept child = 1;
}

// 获取学生信息请求参数
message GetStudentReq {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
}

// 获取学生信息返回参数
message GetStudentRsp {
  // 学生id
  int64 id = 1;
  // 学生姓名，限30字符
  string name = 2;
  // 头像
  string avatar = 3;
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 4;
  // 多音字
  string polyphone = 5;
  // 出生日期，时间戳，单位：秒
  int64 birthday = 6;
  // 入园时间，时间戳，单位：秒
  int64 entry_date = 7;
  // 是否离园（0：否，1：是）
  uint32 is_leave = 8;
  // 离园原因
  string leave_reason = 9;
  // 离园时间
  int64 leave_time = 10;
  // 所在班级信息
  ClassInfo class_info = 11;
  // 联系人列表
  repeated ContactInfo parent_list = 12;
  // 考勤卡列表
  repeated string card_list = 13;
}

message ClassInfo {
  // 班级id
  string class_id = 1;
  // 班级名称
  string class_name = 2;
  // 年级id
  string grade_id = 3;
  // 年级名称
  string grade_name = 4;
}

// 创建学生请求参数
message CreateStudentReq {
  // 学生姓名，限30字符
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 2 [(validate.rules).uint32 = {in: [0,1,2]}];
  // 出生日期，时间戳，单位：秒
  int64 birthday = 3;
  // 入园时间，时间戳，单位：秒
  int64 entry_date = 4;
  // 所在班级id
  int64 class_id = 5 [(validate.rules).int64.gt = 0];
  // 多音字
  string polyphone = 6 [(validate.rules).string = { max_len: 50}];
  // 家长列表
  repeated ContactInfo parent_list = 7 [(validate.rules).repeated.min_items = 1];
  // 考勤卡，最多绑10张卡
  repeated string card_list = 8 [(validate.rules).repeated.max_items = 10];
  // 来源
  uint32 source = 9 [(validate.rules).uint32 = {in: [1,2,3,4]}];
  // 学校id
  int64 inst_id = 10 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 11 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 12 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 13 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 14 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message ContactInfo {
  // 家长id
  int64 id = 1;
  // 关系(1：爸爸，2：妈妈，3：爷爷，4：奶奶，5：外公，6：外婆，7：伯父8：伯母，9：叔叔，10：婶婶，11：姑父，12：姑母，13：舅舅 14：舅妈，15：姐姐，16：哥哥，17：嫂嫂，18：姨妈，19：姑丈，20：其他)
  uint32 relation = 2 [(validate.rules).uint32 = {gte:1, lte: 20}];
  // 家长姓名
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 手机区号，默认需填86
  string re_code = 4 [(validate.rules).string = {min_len: 1, max_len: 3}];
  // 手机号
  string mobile = 5 [(validate.rules).string.pattern = "^1\\d{10}$"];
  // 是否是第一联系人
  uint32 is_first = 6 [(validate.rules).uint32 = {in: [0,1]}];
  // 是否已激活(0-未激活 1-已激活)
  uint32 is_activated = 7 [(validate.rules).uint32 = {in: [0,1]}];
  // 头像
  string avatar = 8;
  // 最后一次登陆时间
  int64 last_login_time = 9;
}

// 更新学生基本请求参数
message UpdateStudentBasicInfoReq {
  // 学生id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学生姓名，限30字符
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 3 [(validate.rules).uint32 = {in: [0,1,2]}];
  // 出生日期，时间戳，单位：秒
  int64 birthday = 4;
  // 入园时间，时间戳，单位：秒
  int64 entry_date = 5;
  // 多音字
  string polyphone = 6 [(validate.rules).string = { max_len: 50}];
  // 头像
  string avatar = 7;
  // 学校id
  int64 inst_id = 8 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 9 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 10 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 11 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 12 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 学生批量删除请求参数
message BatchDeleteStudentReq {
  repeated int64 ids = 1 [(validate.rules).repeated.min_items = 1];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 3 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 5 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 学生批量删除返回参数
message BatchDeleteStudentRsp {
  // 失败列表，若为空则表示全部成功
  repeated StudentFailure failure_list = 1;
}

message StudentFailure {
  // 学生id
  int64 id = 1;
  // 学生姓名
  string name = 2;
  // 失败原因
  string reason = 3;
}

// 学生批量调班请求参数
message BatchShiftStudentReq {
  message ShiftStudent {
    // 学生id
    int64 student_id = 1 [(validate.rules).int64.gt = 0];
    // 新班级id
    int64 new_class_id = 2 [(validate.rules).int64.gt = 0];
  }
  // 转班学生信息
  repeated ShiftStudent students = 1 [(validate.rules).repeated.min_items = 1];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 3 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 5 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 学生批量调班返回参数
message BatchShiftStudentRsp {
  // 失败列表，若为空则表示全部成功
  repeated StudentFailure failure_list = 1;
}

// 批量离园学生请求参数
message BatchLeaveStudentReq {
  // 学生id列表
  repeated int64 ids = 1 [(validate.rules).repeated.min_items = 1];
  // 离职原因，限50字
  string reason = 2 [(validate.rules).string = {min_len: 1, max_len: 50}];
  // 学校id
  int64 inst_id = 3 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 4 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 5 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 6 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 7 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 批量离园学生返回参数
message BatchLeaveStudentRsp {
  // 失败列表，若为空则表示全部成功
  repeated StudentFailure failure_list = 1;
}

// 学生批量重新入园请求参数
message BatchReEntryStudentReq {
  // 学生id列表
  repeated int64 ids = 1 [(validate.rules).repeated.min_items = 1];
  // 班级id
  int64 class_id = 2 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 3 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 4 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 5 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 6 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 7 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 学生批量重新入园返回参数
message BatchReEntryStudentRsp {
  // 失败列表，若为空则表示全部成功
  repeated StudentFailure failure_list = 1;
}

// excel导入学生请求参数
//message ImportStudentReq {
//  // 文件url
//  string file_url = 1;
//  // 学校id
//  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
//  // 渠道 （1：园长后台，2：app端）
//  uint32 channel = 3 [(validate.rules).uint32 = {in: [1,2,3]}];
//  // 创建人id
//  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
//  // 创建人姓名
//  string operator_name = 5 [(validate.rules).string.min_len = 1];
//  // ip地址
//  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
//}

// excel导入学生返回参数
//message ImportStudentRsp {
//  // 导入失败的错误信息记录文件地址
//  string fail_info_file_url = 1;
//}

// 获取在园学生列表请求参数
message ListCurrentStudentReq {
  // 当前页
  uint32 page = 1;
  // 每页条数
  uint32 per_page = 2;
  // 学生姓名
  string student_name = 3;
  // 家长姓名
  string parent_name = 4;
  // 家长手机号码
  string mobile = 5;
  // 年级班级id
  int64 dept_id = 6;
  // 入园起始时间
  int64 entry_start_date = 7;
  // 入园结束时间
  int64 entry_end_date = 8;
  // 学校id
  int64 inst_id = 9 [(validate.rules).int64.gt = 0];
}

// 查询在园学生返回参数
message ListCurrentStudentRsp {
  // 总条数
  int64 total = 1;
  // 当前页
  uint32 page = 2;
  // 每页条数
  uint32 per_page = 3;
  // 学生信息列表
  repeated StudentInfo list = 4;
}

// 学生信息
message StudentInfo {
  // 学生id
  int64 id = 1;
  // 学生姓名
  string name = 2;
  // 学生头像
  string avatar = 3;
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 4;
  // 所在班级信息
  ClassInfo class_info = 5;
  // 入园时间，时间戳，单位：秒
  int64 entry_date = 6;
  // 家长联系人列表
  repeated ContactInfo parent_list = 7;
  // 学生来源 (1-后台添加、2-扫码入班、3-园丁端APP添加)
  int32 source = 8;
}

// 获取毕业学生列表请求参数
message ListGraduateStudentReq {
  // 当前页
  uint32 page = 1 [(validate.rules).uint32.gte = 1];
  // 每页条数
  uint32 per_page = 2 [(validate.rules).uint32.gte = 1];
  // 学生姓名
  string student_name = 3;
  // 离园起始时间
  int64 leave_start_date = 4;
  // 离园结束时间
  int64 leave_end_date = 5;
  // 学校id
  int64 inst_id = 6 [(validate.rules).int64.gt = 0];
}

// 查询离园学生返回参数
message ListGraduateStudentRsp {
  // 总条数
  int64 total = 1;
  // 当前页
  uint32 page = 2;
  // 每页条数
  uint32 per_page = 3;
  // 学生信息列表
  repeated GraduateStudentInfo list = 4;
}

// 离园学生信息
message GraduateStudentInfo {
  // 学生id
  int64 id = 1;
  // 学生姓名
  string name = 2;
  // 学生头像
  string avatar = 3;
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 4;
  // 所在班级信息
  ClassInfo class_info = 5;
  // 离园时间，时间戳，单位：秒
  int64 leave_date = 6;
  // 离园原因
  string leave_reason = 7;
}

message ListStudentOnClassReq {
  // 班级id
  int64 class_id = 1 [(validate.rules).int64.gt = 0];
  // 是否离园(0-在园 1-离园)
  uint32 is_leave = 2 [(validate.rules).uint32 = {in: [0,1]}];
  // 学校id
  int64 inst_id = 3 [(validate.rules).int64.gt = 0];
  // 当前登录用户账号ID
  int64 acc_id = 4;
}

message ListStudentOnClassRsp {
  message BasicInfo {
    // 学生id
    int64 id = 1;
    // 学生姓名
    string name = 2;
    // 学生头像
    string avatar = 3;
    // 性别，0表示未知，1表示男性，2表示女性
    uint32 gender = 4;
    // 学生关联的家长数
    uint32 parent_count = 5;
    // 激活状态(0-未激活 1-已激活)
    uint32 activate_status = 6;
  }
  // 学生列表
  repeated BasicInfo list = 1;
}
