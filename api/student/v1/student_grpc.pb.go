// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: student/v1/student.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Student_GetStudent_FullMethodName               = "/api.student.v1.Student/GetStudent"
	Student_CreateStudent_FullMethodName            = "/api.student.v1.Student/CreateStudent"
	Student_UpdateStudentBasicInfo_FullMethodName   = "/api.student.v1.Student/UpdateStudentBasicInfo"
	Student_BatchDeleteStudent_FullMethodName       = "/api.student.v1.Student/BatchDeleteStudent"
	Student_BatchShiftStudent_FullMethodName        = "/api.student.v1.Student/BatchShiftStudent"
	Student_BatchLeaveStudent_FullMethodName        = "/api.student.v1.Student/BatchLeaveStudent"
	Student_BatchReEntryStudent_FullMethodName      = "/api.student.v1.Student/BatchReEntryStudent"
	Student_ListCurrentStudent_FullMethodName       = "/api.student.v1.Student/ListCurrentStudent"
	Student_ListGraduateStudent_FullMethodName      = "/api.student.v1.Student/ListGraduateStudent"
	Student_ListStudentOnClass_FullMethodName       = "/api.student.v1.Student/ListStudentOnClass"
	Student_GetFullStudentTree_FullMethodName       = "/api.student.v1.Student/GetFullStudentTree"
	Student_GetStudentActivateInfo_FullMethodName   = "/api.student.v1.Student/GetStudentActivateInfo"
	Student_SearchStudent_FullMethodName            = "/api.student.v1.Student/SearchStudent"
	Student_ListUnInvitedFamilyGroup_FullMethodName = "/api.student.v1.Student/ListUnInvitedFamilyGroup"
	Student_FindStudent_FullMethodName              = "/api.student.v1.Student/FindStudent"
)

// StudentClient is the client API for Student service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StudentClient interface {
	// 获取学生信息
	GetStudent(ctx context.Context, in *GetStudentReq, opts ...grpc.CallOption) (*GetStudentRsp, error)
	// 添加学生信息
	CreateStudent(ctx context.Context, in *CreateStudentReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新学生基本信息
	UpdateStudentBasicInfo(ctx context.Context, in *UpdateStudentBasicInfoReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 批量删除学生
	BatchDeleteStudent(ctx context.Context, in *BatchDeleteStudentReq, opts ...grpc.CallOption) (*BatchDeleteStudentRsp, error)
	// 批量调班
	BatchShiftStudent(ctx context.Context, in *BatchShiftStudentReq, opts ...grpc.CallOption) (*BatchShiftStudentRsp, error)
	// 批量离园学生
	BatchLeaveStudent(ctx context.Context, in *BatchLeaveStudentReq, opts ...grpc.CallOption) (*BatchLeaveStudentRsp, error)
	// 批量重新入园
	BatchReEntryStudent(ctx context.Context, in *BatchReEntryStudentReq, opts ...grpc.CallOption) (*BatchReEntryStudentRsp, error)
	// 获取在园学生列表
	ListCurrentStudent(ctx context.Context, in *ListCurrentStudentReq, opts ...grpc.CallOption) (*ListCurrentStudentRsp, error)
	// 获取毕业学生列表
	ListGraduateStudent(ctx context.Context, in *ListGraduateStudentReq, opts ...grpc.CallOption) (*ListGraduateStudentRsp, error)
	// 获取指定班级下学生列表
	ListStudentOnClass(ctx context.Context, in *ListStudentOnClassReq, opts ...grpc.CallOption) (*ListStudentOnClassRsp, error)
	// 获取完整的学生树（包括年级班级和学生）
	GetFullStudentTree(ctx context.Context, in *GetFullStudentTreeReq, opts ...grpc.CallOption) (*GetFullStudentTreeRsp, error)
	// 获取学生的激活数和未激活数
	GetStudentActivateInfo(ctx context.Context, in *GetStudentActivateInfoReq, opts ...grpc.CallOption) (*GetStudentActivateInfoRsp, error)
	// 搜索学生
	SearchStudent(ctx context.Context, in *SearchStudentReq, opts ...grpc.CallOption) (*SearchStudentRsp, error)
	// 获取学生下可邀请的亲友团成员列表
	ListUnInvitedFamilyGroup(ctx context.Context, in *ListUnInvitedFamilyGroupReq, opts ...grpc.CallOption) (*ListUnInvitedFamilyGroupRsp, error)
	// 条件查询在职学生信息（无权限判断，供内部服务使用）
	FindStudent(ctx context.Context, in *FindStudentReq, opts ...grpc.CallOption) (*FindStudentRsp, error)
}

type studentClient struct {
	cc grpc.ClientConnInterface
}

func NewStudentClient(cc grpc.ClientConnInterface) StudentClient {
	return &studentClient{cc}
}

func (c *studentClient) GetStudent(ctx context.Context, in *GetStudentReq, opts ...grpc.CallOption) (*GetStudentRsp, error) {
	out := new(GetStudentRsp)
	err := c.cc.Invoke(ctx, Student_GetStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) CreateStudent(ctx context.Context, in *CreateStudentReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Student_CreateStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) UpdateStudentBasicInfo(ctx context.Context, in *UpdateStudentBasicInfoReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Student_UpdateStudentBasicInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) BatchDeleteStudent(ctx context.Context, in *BatchDeleteStudentReq, opts ...grpc.CallOption) (*BatchDeleteStudentRsp, error) {
	out := new(BatchDeleteStudentRsp)
	err := c.cc.Invoke(ctx, Student_BatchDeleteStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) BatchShiftStudent(ctx context.Context, in *BatchShiftStudentReq, opts ...grpc.CallOption) (*BatchShiftStudentRsp, error) {
	out := new(BatchShiftStudentRsp)
	err := c.cc.Invoke(ctx, Student_BatchShiftStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) BatchLeaveStudent(ctx context.Context, in *BatchLeaveStudentReq, opts ...grpc.CallOption) (*BatchLeaveStudentRsp, error) {
	out := new(BatchLeaveStudentRsp)
	err := c.cc.Invoke(ctx, Student_BatchLeaveStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) BatchReEntryStudent(ctx context.Context, in *BatchReEntryStudentReq, opts ...grpc.CallOption) (*BatchReEntryStudentRsp, error) {
	out := new(BatchReEntryStudentRsp)
	err := c.cc.Invoke(ctx, Student_BatchReEntryStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) ListCurrentStudent(ctx context.Context, in *ListCurrentStudentReq, opts ...grpc.CallOption) (*ListCurrentStudentRsp, error) {
	out := new(ListCurrentStudentRsp)
	err := c.cc.Invoke(ctx, Student_ListCurrentStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) ListGraduateStudent(ctx context.Context, in *ListGraduateStudentReq, opts ...grpc.CallOption) (*ListGraduateStudentRsp, error) {
	out := new(ListGraduateStudentRsp)
	err := c.cc.Invoke(ctx, Student_ListGraduateStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) ListStudentOnClass(ctx context.Context, in *ListStudentOnClassReq, opts ...grpc.CallOption) (*ListStudentOnClassRsp, error) {
	out := new(ListStudentOnClassRsp)
	err := c.cc.Invoke(ctx, Student_ListStudentOnClass_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) GetFullStudentTree(ctx context.Context, in *GetFullStudentTreeReq, opts ...grpc.CallOption) (*GetFullStudentTreeRsp, error) {
	out := new(GetFullStudentTreeRsp)
	err := c.cc.Invoke(ctx, Student_GetFullStudentTree_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) GetStudentActivateInfo(ctx context.Context, in *GetStudentActivateInfoReq, opts ...grpc.CallOption) (*GetStudentActivateInfoRsp, error) {
	out := new(GetStudentActivateInfoRsp)
	err := c.cc.Invoke(ctx, Student_GetStudentActivateInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) SearchStudent(ctx context.Context, in *SearchStudentReq, opts ...grpc.CallOption) (*SearchStudentRsp, error) {
	out := new(SearchStudentRsp)
	err := c.cc.Invoke(ctx, Student_SearchStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) ListUnInvitedFamilyGroup(ctx context.Context, in *ListUnInvitedFamilyGroupReq, opts ...grpc.CallOption) (*ListUnInvitedFamilyGroupRsp, error) {
	out := new(ListUnInvitedFamilyGroupRsp)
	err := c.cc.Invoke(ctx, Student_ListUnInvitedFamilyGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentClient) FindStudent(ctx context.Context, in *FindStudentReq, opts ...grpc.CallOption) (*FindStudentRsp, error) {
	out := new(FindStudentRsp)
	err := c.cc.Invoke(ctx, Student_FindStudent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StudentServer is the server API for Student service.
// All implementations must embed UnimplementedStudentServer
// for forward compatibility
type StudentServer interface {
	// 获取学生信息
	GetStudent(context.Context, *GetStudentReq) (*GetStudentRsp, error)
	// 添加学生信息
	CreateStudent(context.Context, *CreateStudentReq) (*emptypb.Empty, error)
	// 更新学生基本信息
	UpdateStudentBasicInfo(context.Context, *UpdateStudentBasicInfoReq) (*emptypb.Empty, error)
	// 批量删除学生
	BatchDeleteStudent(context.Context, *BatchDeleteStudentReq) (*BatchDeleteStudentRsp, error)
	// 批量调班
	BatchShiftStudent(context.Context, *BatchShiftStudentReq) (*BatchShiftStudentRsp, error)
	// 批量离园学生
	BatchLeaveStudent(context.Context, *BatchLeaveStudentReq) (*BatchLeaveStudentRsp, error)
	// 批量重新入园
	BatchReEntryStudent(context.Context, *BatchReEntryStudentReq) (*BatchReEntryStudentRsp, error)
	// 获取在园学生列表
	ListCurrentStudent(context.Context, *ListCurrentStudentReq) (*ListCurrentStudentRsp, error)
	// 获取毕业学生列表
	ListGraduateStudent(context.Context, *ListGraduateStudentReq) (*ListGraduateStudentRsp, error)
	// 获取指定班级下学生列表
	ListStudentOnClass(context.Context, *ListStudentOnClassReq) (*ListStudentOnClassRsp, error)
	// 获取完整的学生树（包括年级班级和学生）
	GetFullStudentTree(context.Context, *GetFullStudentTreeReq) (*GetFullStudentTreeRsp, error)
	// 获取学生的激活数和未激活数
	GetStudentActivateInfo(context.Context, *GetStudentActivateInfoReq) (*GetStudentActivateInfoRsp, error)
	// 搜索学生
	SearchStudent(context.Context, *SearchStudentReq) (*SearchStudentRsp, error)
	// 获取学生下可邀请的亲友团成员列表
	ListUnInvitedFamilyGroup(context.Context, *ListUnInvitedFamilyGroupReq) (*ListUnInvitedFamilyGroupRsp, error)
	// 条件查询在职学生信息（无权限判断，供内部服务使用）
	FindStudent(context.Context, *FindStudentReq) (*FindStudentRsp, error)
	mustEmbedUnimplementedStudentServer()
}

// UnimplementedStudentServer must be embedded to have forward compatible implementations.
type UnimplementedStudentServer struct {
}

func (UnimplementedStudentServer) GetStudent(context.Context, *GetStudentReq) (*GetStudentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStudent not implemented")
}
func (UnimplementedStudentServer) CreateStudent(context.Context, *CreateStudentReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStudent not implemented")
}
func (UnimplementedStudentServer) UpdateStudentBasicInfo(context.Context, *UpdateStudentBasicInfoReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStudentBasicInfo not implemented")
}
func (UnimplementedStudentServer) BatchDeleteStudent(context.Context, *BatchDeleteStudentReq) (*BatchDeleteStudentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteStudent not implemented")
}
func (UnimplementedStudentServer) BatchShiftStudent(context.Context, *BatchShiftStudentReq) (*BatchShiftStudentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchShiftStudent not implemented")
}
func (UnimplementedStudentServer) BatchLeaveStudent(context.Context, *BatchLeaveStudentReq) (*BatchLeaveStudentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchLeaveStudent not implemented")
}
func (UnimplementedStudentServer) BatchReEntryStudent(context.Context, *BatchReEntryStudentReq) (*BatchReEntryStudentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchReEntryStudent not implemented")
}
func (UnimplementedStudentServer) ListCurrentStudent(context.Context, *ListCurrentStudentReq) (*ListCurrentStudentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCurrentStudent not implemented")
}
func (UnimplementedStudentServer) ListGraduateStudent(context.Context, *ListGraduateStudentReq) (*ListGraduateStudentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGraduateStudent not implemented")
}
func (UnimplementedStudentServer) ListStudentOnClass(context.Context, *ListStudentOnClassReq) (*ListStudentOnClassRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStudentOnClass not implemented")
}
func (UnimplementedStudentServer) GetFullStudentTree(context.Context, *GetFullStudentTreeReq) (*GetFullStudentTreeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFullStudentTree not implemented")
}
func (UnimplementedStudentServer) GetStudentActivateInfo(context.Context, *GetStudentActivateInfoReq) (*GetStudentActivateInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStudentActivateInfo not implemented")
}
func (UnimplementedStudentServer) SearchStudent(context.Context, *SearchStudentReq) (*SearchStudentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchStudent not implemented")
}
func (UnimplementedStudentServer) ListUnInvitedFamilyGroup(context.Context, *ListUnInvitedFamilyGroupReq) (*ListUnInvitedFamilyGroupRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUnInvitedFamilyGroup not implemented")
}
func (UnimplementedStudentServer) FindStudent(context.Context, *FindStudentReq) (*FindStudentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindStudent not implemented")
}
func (UnimplementedStudentServer) mustEmbedUnimplementedStudentServer() {}

// UnsafeStudentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StudentServer will
// result in compilation errors.
type UnsafeStudentServer interface {
	mustEmbedUnimplementedStudentServer()
}

func RegisterStudentServer(s grpc.ServiceRegistrar, srv StudentServer) {
	s.RegisterService(&Student_ServiceDesc, srv)
}

func _Student_GetStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).GetStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_GetStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).GetStudent(ctx, req.(*GetStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_CreateStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).CreateStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_CreateStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).CreateStudent(ctx, req.(*CreateStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_UpdateStudentBasicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStudentBasicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).UpdateStudentBasicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_UpdateStudentBasicInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).UpdateStudentBasicInfo(ctx, req.(*UpdateStudentBasicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_BatchDeleteStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).BatchDeleteStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_BatchDeleteStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).BatchDeleteStudent(ctx, req.(*BatchDeleteStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_BatchShiftStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchShiftStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).BatchShiftStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_BatchShiftStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).BatchShiftStudent(ctx, req.(*BatchShiftStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_BatchLeaveStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchLeaveStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).BatchLeaveStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_BatchLeaveStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).BatchLeaveStudent(ctx, req.(*BatchLeaveStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_BatchReEntryStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchReEntryStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).BatchReEntryStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_BatchReEntryStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).BatchReEntryStudent(ctx, req.(*BatchReEntryStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_ListCurrentStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCurrentStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).ListCurrentStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_ListCurrentStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).ListCurrentStudent(ctx, req.(*ListCurrentStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_ListGraduateStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGraduateStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).ListGraduateStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_ListGraduateStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).ListGraduateStudent(ctx, req.(*ListGraduateStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_ListStudentOnClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStudentOnClassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).ListStudentOnClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_ListStudentOnClass_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).ListStudentOnClass(ctx, req.(*ListStudentOnClassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_GetFullStudentTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFullStudentTreeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).GetFullStudentTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_GetFullStudentTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).GetFullStudentTree(ctx, req.(*GetFullStudentTreeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_GetStudentActivateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStudentActivateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).GetStudentActivateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_GetStudentActivateInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).GetStudentActivateInfo(ctx, req.(*GetStudentActivateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_SearchStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).SearchStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_SearchStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).SearchStudent(ctx, req.(*SearchStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_ListUnInvitedFamilyGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUnInvitedFamilyGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).ListUnInvitedFamilyGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_ListUnInvitedFamilyGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).ListUnInvitedFamilyGroup(ctx, req.(*ListUnInvitedFamilyGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Student_FindStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindStudentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServer).FindStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Student_FindStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServer).FindStudent(ctx, req.(*FindStudentReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Student_ServiceDesc is the grpc.ServiceDesc for Student service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Student_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.student.v1.Student",
	HandlerType: (*StudentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStudent",
			Handler:    _Student_GetStudent_Handler,
		},
		{
			MethodName: "CreateStudent",
			Handler:    _Student_CreateStudent_Handler,
		},
		{
			MethodName: "UpdateStudentBasicInfo",
			Handler:    _Student_UpdateStudentBasicInfo_Handler,
		},
		{
			MethodName: "BatchDeleteStudent",
			Handler:    _Student_BatchDeleteStudent_Handler,
		},
		{
			MethodName: "BatchShiftStudent",
			Handler:    _Student_BatchShiftStudent_Handler,
		},
		{
			MethodName: "BatchLeaveStudent",
			Handler:    _Student_BatchLeaveStudent_Handler,
		},
		{
			MethodName: "BatchReEntryStudent",
			Handler:    _Student_BatchReEntryStudent_Handler,
		},
		{
			MethodName: "ListCurrentStudent",
			Handler:    _Student_ListCurrentStudent_Handler,
		},
		{
			MethodName: "ListGraduateStudent",
			Handler:    _Student_ListGraduateStudent_Handler,
		},
		{
			MethodName: "ListStudentOnClass",
			Handler:    _Student_ListStudentOnClass_Handler,
		},
		{
			MethodName: "GetFullStudentTree",
			Handler:    _Student_GetFullStudentTree_Handler,
		},
		{
			MethodName: "GetStudentActivateInfo",
			Handler:    _Student_GetStudentActivateInfo_Handler,
		},
		{
			MethodName: "SearchStudent",
			Handler:    _Student_SearchStudent_Handler,
		},
		{
			MethodName: "ListUnInvitedFamilyGroup",
			Handler:    _Student_ListUnInvitedFamilyGroup_Handler,
		},
		{
			MethodName: "FindStudent",
			Handler:    _Student_FindStudent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "student/v1/student.proto",
}
