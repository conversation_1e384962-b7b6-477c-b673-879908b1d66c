// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package v1

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 数据库内部错误
func IsDbInternalError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DB_INTERNAL_ERROR.String() && e.Code == 500
}

// 数据库内部错误
func ErrorDbInternalError(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_DB_INTERNAL_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsParamErr(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_PARAM_ERR.String() && e.Code == 400
}

func ErrorParamErr(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_PARAM_ERR.String(), fmt.Sprintf(format, args...))
}

// 操作失败
func IsOptFailure(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_OPT_FAILURE.String() && e.Code == 500
}

// 操作失败
func ErrorOptFailure(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_OPT_FAILURE.String(), fmt.Sprintf(format, args...))
}

// 操作成功
func IsOptSuccess(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_OPT_SUCCESS.String() && e.Code == 500
}

// 操作成功
func ErrorOptSuccess(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_OPT_SUCCESS.String(), fmt.Sprintf(format, args...))
}

// 远程调用错误
func IsRpcCallError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_RPC_CALL_ERROR.String() && e.Code == 500
}

// 远程调用错误
func ErrorRpcCallError(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_RPC_CALL_ERROR.String(), fmt.Sprintf(format, args...))
}

// 考勤卡不在卡库
func IsInvalidCard(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_INVALID_CARD.String() && e.Code == 400
}

// 考勤卡不在卡库
func ErrorInvalidCard(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_INVALID_CARD.String(), fmt.Sprintf(format, args...))
}

// 考勤卡已被占用
func IsCardIsOccupied(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CARD_IS_OCCUPIED.String() && e.Code == 400
}

// 考勤卡已被占用
func ErrorCardIsOccupied(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CARD_IS_OCCUPIED.String(), fmt.Sprintf(format, args...))
}

// 没有权限操作
func IsPermissionDenied(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_PERMISSION_DENIED.String() && e.Code == 400
}

// 没有权限操作
func ErrorPermissionDenied(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_PERMISSION_DENIED.String(), fmt.Sprintf(format, args...))
}
