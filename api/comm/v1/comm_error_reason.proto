syntax = "proto3";

package comm.v1;

import "errors/errors.proto";

option go_package = "api/comm/v1;v1";

//生成proto代码
//kratos proto client api/comm/v1/comm_error_reason.proto

enum ErrorReason {
  // 设置缺省错误码
  option (errors.default_code) = 500;

  // 数据库内部错误
  DB_INTERNAL_ERROR = 0 [(errors.code) = 500];

  PARAM_ERR = 2 [(errors.code) = 400];

  OPT_FAILURE = 3 [(errors.code) = 500]; /*操作失败*/
  OPT_SUCCESS = 4 [(errors.code) = 500]; /*操作成功*/

  // 远程调用错误
  RPC_CALL_ERROR = 5 [(errors.code) = 500];

  // 考勤卡不在卡库
  INVALID_CARD = 6 [(errors.code) = 400];

  // 考勤卡已被占用
  CARD_IS_OCCUPIED = 7 [(errors.code) = 400];

  // 没有权限操作
  PERMISSION_DENIED = 8 [(errors.code) = 400];
}
