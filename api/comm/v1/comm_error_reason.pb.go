// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.17.3
// source: comm/v1/comm_error_reason.proto

package v1

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	// 数据库内部错误
	ErrorReason_DB_INTERNAL_ERROR ErrorReason = 0
	ErrorReason_PARAM_ERR         ErrorReason = 2
	ErrorReason_OPT_FAILURE       ErrorReason = 3 //操作失败
	ErrorReason_OPT_SUCCESS       ErrorReason = 4 //操作成功
	// 远程调用错误
	ErrorReason_RPC_CALL_ERROR ErrorReason = 5
	// 考勤卡不在卡库
	ErrorReason_INVALID_CARD ErrorReason = 6
	// 考勤卡已被占用
	ErrorReason_CARD_IS_OCCUPIED ErrorReason = 7
	// 没有权限操作
	ErrorReason_PERMISSION_DENIED ErrorReason = 8
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0: "DB_INTERNAL_ERROR",
		2: "PARAM_ERR",
		3: "OPT_FAILURE",
		4: "OPT_SUCCESS",
		5: "RPC_CALL_ERROR",
		6: "INVALID_CARD",
		7: "CARD_IS_OCCUPIED",
		8: "PERMISSION_DENIED",
	}
	ErrorReason_value = map[string]int32{
		"DB_INTERNAL_ERROR": 0,
		"PARAM_ERR":         2,
		"OPT_FAILURE":       3,
		"OPT_SUCCESS":       4,
		"RPC_CALL_ERROR":    5,
		"INVALID_CARD":      6,
		"CARD_IS_OCCUPIED":  7,
		"PERMISSION_DENIED": 8,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_comm_v1_comm_error_reason_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_comm_v1_comm_error_reason_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_comm_v1_comm_error_reason_proto_rawDescGZIP(), []int{0}
}

var File_comm_v1_comm_error_reason_proto protoreflect.FileDescriptor

var file_comm_v1_comm_error_reason_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x13, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a,
	0xde, 0x01, 0x0a, 0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x1b, 0x0a, 0x11, 0x44, 0x42, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x00, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03, 0x12, 0x13, 0x0a, 0x09,
	0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x02, 0x1a, 0x04, 0xa8, 0x45, 0x90,
	0x03, 0x12, 0x15, 0x0a, 0x0b, 0x4f, 0x50, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x10, 0x03, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03, 0x12, 0x15, 0x0a, 0x0b, 0x4f, 0x50, 0x54, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x04, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03, 0x12,
	0x18, 0x0a, 0x0e, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x05, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03, 0x12, 0x16, 0x0a, 0x0c, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x06, 0x1a, 0x04, 0xa8, 0x45, 0x90,
	0x03, 0x12, 0x1a, 0x0a, 0x10, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x53, 0x5f, 0x4f, 0x43, 0x43,
	0x55, 0x50, 0x49, 0x45, 0x44, 0x10, 0x07, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x1b, 0x0a,
	0x11, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49,
	0x45, 0x44, 0x10, 0x08, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x1a, 0x04, 0xa0, 0x45, 0xf4, 0x03,
	0x42, 0x10, 0x5a, 0x0e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2f, 0x76, 0x31, 0x3b,
	0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_comm_v1_comm_error_reason_proto_rawDescOnce sync.Once
	file_comm_v1_comm_error_reason_proto_rawDescData = file_comm_v1_comm_error_reason_proto_rawDesc
)

func file_comm_v1_comm_error_reason_proto_rawDescGZIP() []byte {
	file_comm_v1_comm_error_reason_proto_rawDescOnce.Do(func() {
		file_comm_v1_comm_error_reason_proto_rawDescData = protoimpl.X.CompressGZIP(file_comm_v1_comm_error_reason_proto_rawDescData)
	})
	return file_comm_v1_comm_error_reason_proto_rawDescData
}

var file_comm_v1_comm_error_reason_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_comm_v1_comm_error_reason_proto_goTypes = []interface{}{
	(ErrorReason)(0), // 0: comm.v1.ErrorReason
}
var file_comm_v1_comm_error_reason_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_comm_v1_comm_error_reason_proto_init() }
func file_comm_v1_comm_error_reason_proto_init() {
	if File_comm_v1_comm_error_reason_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_comm_v1_comm_error_reason_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_comm_v1_comm_error_reason_proto_goTypes,
		DependencyIndexes: file_comm_v1_comm_error_reason_proto_depIdxs,
		EnumInfos:         file_comm_v1_comm_error_reason_proto_enumTypes,
	}.Build()
	File_comm_v1_comm_error_reason_proto = out.File
	file_comm_v1_comm_error_reason_proto_rawDesc = nil
	file_comm_v1_comm_error_reason_proto_goTypes = nil
	file_comm_v1_comm_error_reason_proto_depIdxs = nil
}
