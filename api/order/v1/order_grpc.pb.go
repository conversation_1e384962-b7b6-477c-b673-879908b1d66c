// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: order/v1/order.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Order_OpenSunBabyService_FullMethodName             = "/api.order.v1.Order/OpenSunBabyService"
	Order_RenewalSunBabyService_FullMethodName          = "/api.order.v1.Order/RenewalSunBabyService"
	Order_UpgradeSunBabyService_FullMethodName          = "/api.order.v1.Order/UpgradeSunBabyService"
	Order_ExamineInstOrder_FullMethodName               = "/api.order.v1.Order/ExamineInstOrder"
	Order_StaffOpenVideoForParent_FullMethodName        = "/api.order.v1.Order/StaffOpenVideoForParent"
	Order_InstIsOpenSunBaby_FullMethodName              = "/api.order.v1.Order/InstIsOpenSunBaby"
	Order_ParentIsOpenVideo_FullMethodName              = "/api.order.v1.Order/ParentIsOpenVideo"
	Order_ListOrder_FullMethodName                      = "/api.order.v1.Order/ListOrder"
	Order_GetOrder_FullMethodName                       = "/api.order.v1.Order/GetOrder"
	Order_ListInstServicePackage_FullMethodName         = "/api.order.v1.Order/ListInstServicePackage"
	Order_GetUpgradeSunBabyServiceAmount_FullMethodName = "/api.order.v1.Order/GetUpgradeSunBabyServiceAmount"
)

// OrderClient is the client API for Order service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderClient interface {
	// 学校开通阳光宝贝服务
	OpenSunBabyService(ctx context.Context, in *OpenSunBabyServiceReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 学校续费阳光宝贝服务
	RenewalSunBabyService(ctx context.Context, in *RenewalSunBabyServiceReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 学校升级阳光宝贝服务
	UpgradeSunBabyService(ctx context.Context, in *UpgradeSunBabyServiceReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 订单审核接口 (没有这个接口 没法测试审核通过的服务订单)
	ExamineInstOrder(ctx context.Context, in *ExamineInstOrderReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 老师帮家长代开通视频服务
	StaffOpenVideoForParent(ctx context.Context, in *StaffOpenVideoForParentReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取学校是否开通宝贝在线服务(如果有则返回详情 包括审核状态 合同是否签署)
	InstIsOpenSunBaby(ctx context.Context, in *InstIsOpenSunBabyReq, opts ...grpc.CallOption) (*InstIsOpenSunBabyRsp, error)
	// 获取家长学生是否开通了视频服务
	ParentIsOpenVideo(ctx context.Context, in *ParentIsOpenVideoReq, opts ...grpc.CallOption) (*ParentIsOpenVideoRsp, error)
	// 订单列表 (暂时不需要支持查询)
	ListOrder(ctx context.Context, in *ListOrderReq, opts ...grpc.CallOption) (*ListOrderRsp, error)
	// 订单详情
	GetOrder(ctx context.Context, in *GetOrderReq, opts ...grpc.CallOption) (*GetOrderRsp, error)
	// 学校服务套餐列表
	ListInstServicePackage(ctx context.Context, in *ListInstServicePackageReq, opts ...grpc.CallOption) (*ListInstServicePackageRsp, error)
	// 计算升级阳光宝贝服务订单金额
	GetUpgradeSunBabyServiceAmount(ctx context.Context, in *GetUpgradeSunBabyServiceAmountReq, opts ...grpc.CallOption) (*GetUpgradeSunBabyServiceAmountRsp, error)
}

type orderClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderClient(cc grpc.ClientConnInterface) OrderClient {
	return &orderClient{cc}
}

func (c *orderClient) OpenSunBabyService(ctx context.Context, in *OpenSunBabyServiceReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Order_OpenSunBabyService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) RenewalSunBabyService(ctx context.Context, in *RenewalSunBabyServiceReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Order_RenewalSunBabyService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) UpgradeSunBabyService(ctx context.Context, in *UpgradeSunBabyServiceReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Order_UpgradeSunBabyService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) ExamineInstOrder(ctx context.Context, in *ExamineInstOrderReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Order_ExamineInstOrder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) StaffOpenVideoForParent(ctx context.Context, in *StaffOpenVideoForParentReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Order_StaffOpenVideoForParent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) InstIsOpenSunBaby(ctx context.Context, in *InstIsOpenSunBabyReq, opts ...grpc.CallOption) (*InstIsOpenSunBabyRsp, error) {
	out := new(InstIsOpenSunBabyRsp)
	err := c.cc.Invoke(ctx, Order_InstIsOpenSunBaby_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) ParentIsOpenVideo(ctx context.Context, in *ParentIsOpenVideoReq, opts ...grpc.CallOption) (*ParentIsOpenVideoRsp, error) {
	out := new(ParentIsOpenVideoRsp)
	err := c.cc.Invoke(ctx, Order_ParentIsOpenVideo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) ListOrder(ctx context.Context, in *ListOrderReq, opts ...grpc.CallOption) (*ListOrderRsp, error) {
	out := new(ListOrderRsp)
	err := c.cc.Invoke(ctx, Order_ListOrder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) GetOrder(ctx context.Context, in *GetOrderReq, opts ...grpc.CallOption) (*GetOrderRsp, error) {
	out := new(GetOrderRsp)
	err := c.cc.Invoke(ctx, Order_GetOrder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) ListInstServicePackage(ctx context.Context, in *ListInstServicePackageReq, opts ...grpc.CallOption) (*ListInstServicePackageRsp, error) {
	out := new(ListInstServicePackageRsp)
	err := c.cc.Invoke(ctx, Order_ListInstServicePackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) GetUpgradeSunBabyServiceAmount(ctx context.Context, in *GetUpgradeSunBabyServiceAmountReq, opts ...grpc.CallOption) (*GetUpgradeSunBabyServiceAmountRsp, error) {
	out := new(GetUpgradeSunBabyServiceAmountRsp)
	err := c.cc.Invoke(ctx, Order_GetUpgradeSunBabyServiceAmount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderServer is the server API for Order service.
// All implementations must embed UnimplementedOrderServer
// for forward compatibility
type OrderServer interface {
	// 学校开通阳光宝贝服务
	OpenSunBabyService(context.Context, *OpenSunBabyServiceReq) (*emptypb.Empty, error)
	// 学校续费阳光宝贝服务
	RenewalSunBabyService(context.Context, *RenewalSunBabyServiceReq) (*emptypb.Empty, error)
	// 学校升级阳光宝贝服务
	UpgradeSunBabyService(context.Context, *UpgradeSunBabyServiceReq) (*emptypb.Empty, error)
	// 订单审核接口 (没有这个接口 没法测试审核通过的服务订单)
	ExamineInstOrder(context.Context, *ExamineInstOrderReq) (*emptypb.Empty, error)
	// 老师帮家长代开通视频服务
	StaffOpenVideoForParent(context.Context, *StaffOpenVideoForParentReq) (*emptypb.Empty, error)
	// 获取学校是否开通宝贝在线服务(如果有则返回详情 包括审核状态 合同是否签署)
	InstIsOpenSunBaby(context.Context, *InstIsOpenSunBabyReq) (*InstIsOpenSunBabyRsp, error)
	// 获取家长学生是否开通了视频服务
	ParentIsOpenVideo(context.Context, *ParentIsOpenVideoReq) (*ParentIsOpenVideoRsp, error)
	// 订单列表 (暂时不需要支持查询)
	ListOrder(context.Context, *ListOrderReq) (*ListOrderRsp, error)
	// 订单详情
	GetOrder(context.Context, *GetOrderReq) (*GetOrderRsp, error)
	// 学校服务套餐列表
	ListInstServicePackage(context.Context, *ListInstServicePackageReq) (*ListInstServicePackageRsp, error)
	// 计算升级阳光宝贝服务订单金额
	GetUpgradeSunBabyServiceAmount(context.Context, *GetUpgradeSunBabyServiceAmountReq) (*GetUpgradeSunBabyServiceAmountRsp, error)
	mustEmbedUnimplementedOrderServer()
}

// UnimplementedOrderServer must be embedded to have forward compatible implementations.
type UnimplementedOrderServer struct {
}

func (UnimplementedOrderServer) OpenSunBabyService(context.Context, *OpenSunBabyServiceReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OpenSunBabyService not implemented")
}
func (UnimplementedOrderServer) RenewalSunBabyService(context.Context, *RenewalSunBabyServiceReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewalSunBabyService not implemented")
}
func (UnimplementedOrderServer) UpgradeSunBabyService(context.Context, *UpgradeSunBabyServiceReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpgradeSunBabyService not implemented")
}
func (UnimplementedOrderServer) ExamineInstOrder(context.Context, *ExamineInstOrderReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineInstOrder not implemented")
}
func (UnimplementedOrderServer) StaffOpenVideoForParent(context.Context, *StaffOpenVideoForParentReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StaffOpenVideoForParent not implemented")
}
func (UnimplementedOrderServer) InstIsOpenSunBaby(context.Context, *InstIsOpenSunBabyReq) (*InstIsOpenSunBabyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstIsOpenSunBaby not implemented")
}
func (UnimplementedOrderServer) ParentIsOpenVideo(context.Context, *ParentIsOpenVideoReq) (*ParentIsOpenVideoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParentIsOpenVideo not implemented")
}
func (UnimplementedOrderServer) ListOrder(context.Context, *ListOrderReq) (*ListOrderRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOrder not implemented")
}
func (UnimplementedOrderServer) GetOrder(context.Context, *GetOrderReq) (*GetOrderRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrder not implemented")
}
func (UnimplementedOrderServer) ListInstServicePackage(context.Context, *ListInstServicePackageReq) (*ListInstServicePackageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstServicePackage not implemented")
}
func (UnimplementedOrderServer) GetUpgradeSunBabyServiceAmount(context.Context, *GetUpgradeSunBabyServiceAmountReq) (*GetUpgradeSunBabyServiceAmountRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpgradeSunBabyServiceAmount not implemented")
}
func (UnimplementedOrderServer) mustEmbedUnimplementedOrderServer() {}

// UnsafeOrderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderServer will
// result in compilation errors.
type UnsafeOrderServer interface {
	mustEmbedUnimplementedOrderServer()
}

func RegisterOrderServer(s grpc.ServiceRegistrar, srv OrderServer) {
	s.RegisterService(&Order_ServiceDesc, srv)
}

func _Order_OpenSunBabyService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenSunBabyServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).OpenSunBabyService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_OpenSunBabyService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).OpenSunBabyService(ctx, req.(*OpenSunBabyServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_RenewalSunBabyService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewalSunBabyServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).RenewalSunBabyService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_RenewalSunBabyService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).RenewalSunBabyService(ctx, req.(*RenewalSunBabyServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpgradeSunBabyService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpgradeSunBabyServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).UpgradeSunBabyService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_UpgradeSunBabyService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).UpgradeSunBabyService(ctx, req.(*UpgradeSunBabyServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ExamineInstOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineInstOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).ExamineInstOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_ExamineInstOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).ExamineInstOrder(ctx, req.(*ExamineInstOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_StaffOpenVideoForParent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StaffOpenVideoForParentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).StaffOpenVideoForParent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_StaffOpenVideoForParent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).StaffOpenVideoForParent(ctx, req.(*StaffOpenVideoForParentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_InstIsOpenSunBaby_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstIsOpenSunBabyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).InstIsOpenSunBaby(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_InstIsOpenSunBaby_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).InstIsOpenSunBaby(ctx, req.(*InstIsOpenSunBabyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ParentIsOpenVideo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParentIsOpenVideoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).ParentIsOpenVideo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_ParentIsOpenVideo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).ParentIsOpenVideo(ctx, req.(*ParentIsOpenVideoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ListOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).ListOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_ListOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).ListOrder(ctx, req.(*ListOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).GetOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_GetOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).GetOrder(ctx, req.(*GetOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ListInstServicePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstServicePackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).ListInstServicePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_ListInstServicePackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).ListInstServicePackage(ctx, req.(*ListInstServicePackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetUpgradeSunBabyServiceAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpgradeSunBabyServiceAmountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).GetUpgradeSunBabyServiceAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_GetUpgradeSunBabyServiceAmount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).GetUpgradeSunBabyServiceAmount(ctx, req.(*GetUpgradeSunBabyServiceAmountReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Order_ServiceDesc is the grpc.ServiceDesc for Order service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Order_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.order.v1.Order",
	HandlerType: (*OrderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OpenSunBabyService",
			Handler:    _Order_OpenSunBabyService_Handler,
		},
		{
			MethodName: "RenewalSunBabyService",
			Handler:    _Order_RenewalSunBabyService_Handler,
		},
		{
			MethodName: "UpgradeSunBabyService",
			Handler:    _Order_UpgradeSunBabyService_Handler,
		},
		{
			MethodName: "ExamineInstOrder",
			Handler:    _Order_ExamineInstOrder_Handler,
		},
		{
			MethodName: "StaffOpenVideoForParent",
			Handler:    _Order_StaffOpenVideoForParent_Handler,
		},
		{
			MethodName: "InstIsOpenSunBaby",
			Handler:    _Order_InstIsOpenSunBaby_Handler,
		},
		{
			MethodName: "ParentIsOpenVideo",
			Handler:    _Order_ParentIsOpenVideo_Handler,
		},
		{
			MethodName: "ListOrder",
			Handler:    _Order_ListOrder_Handler,
		},
		{
			MethodName: "GetOrder",
			Handler:    _Order_GetOrder_Handler,
		},
		{
			MethodName: "ListInstServicePackage",
			Handler:    _Order_ListInstServicePackage_Handler,
		},
		{
			MethodName: "GetUpgradeSunBabyServiceAmount",
			Handler:    _Order_GetUpgradeSunBabyServiceAmount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "order/v1/order.proto",
}
