// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package v1

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 订单不存在
func IsBaseOrderNotExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_BASE_ORDER_NOT_EXIST.String() && e.Code == 400
}

// 订单不存在
func ErrorBaseOrderNotExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_BASE_ORDER_NOT_EXIST.String(), fmt.Sprintf(format, args...))
}

// 订单金额有误
func IsBaseOrderAmountWrong(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_BASE_ORDER_AMOUNT_WRONG.String() && e.Code == 400
}

// 订单金额有误
func ErrorBaseOrderAmountWrong(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_BASE_ORDER_AMOUNT_WRONG.String(), fmt.Sprintf(format, args...))
}
