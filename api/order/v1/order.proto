syntax = "proto3";

package api.order.v1;

import "google/protobuf/empty.proto";
import "validate/validate.proto";

option go_package = "api/order/v1;v1";


// 学校订单相关服务
service Order {
    // 学校开通阳光宝贝服务
    rpc OpenSunBabyService (OpenSunBabyServiceReq) returns (google.protobuf.Empty) {}

    // 学校续费阳光宝贝服务
    rpc RenewalSunBabyService (RenewalSunBabyServiceReq) returns (google.protobuf.Empty) {}

    // 学校升级阳光宝贝服务
    rpc UpgradeSunBabyService (UpgradeSunBabyServiceReq) returns (google.protobuf.Empty) {}

    // 订单审核接口 (没有这个接口 没法测试审核通过的服务订单)
    rpc ExamineInstOrder (ExamineInstOrderReq) returns (google.protobuf.Empty) {}

    // 老师帮家长代开通视频服务
    rpc StaffOpenVideoForParent (StaffOpenVideoForParentReq) returns (google.protobuf.Empty) {}

    // 获取学校是否开通宝贝在线服务(如果有则返回详情 包括审核状态 合同是否签署)
    rpc InstIsOpenSunBaby (InstIsOpenSunBabyReq) returns (InstIsOpenSunBabyRsp) {}

    // 获取家长学生是否开通了视频服务
    rpc ParentIsOpenVideo (ParentIsOpenVideoReq) returns (ParentIsOpenVideoRsp) {}

    // 订单列表 (暂时不需要支持查询)
    rpc ListOrder (ListOrderReq) returns (ListOrderRsp) {}

    // 订单详情
    rpc GetOrder (GetOrderReq) returns (GetOrderRsp) {}

    // 学校服务套餐列表
    rpc ListInstServicePackage (ListInstServicePackageReq) returns (ListInstServicePackageRsp) {}

    // 计算升级阳光宝贝服务订单金额
    rpc GetUpgradeSunBabyServiceAmount (GetUpgradeSunBabyServiceAmountReq) returns (GetUpgradeSunBabyServiceAmountRsp) {}
}

message GetUpgradeSunBabyServiceAmountReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 原服务订单id
    int64 old_order_id = 2;
    // 现服务套餐id
    int64 new_package_id = 3;
}
message GetUpgradeSunBabyServiceAmountRsp {
    // 订单金额(单位:分)
    uint32 amount = 1;
    // 服务器计算订单金额时的时间戳
    int64 server_timestamp = 2;
    // 升级的年限
    uint32 years = 3;
}

message ListInstServicePackageReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 套餐类型（1-宝贝在线 2-成长档案）
    uint32 package_type = 2;
}
message ListInstServicePackageRsp {
    // 服务套餐列表
    repeated PackageInfo list = 1;
}
message PackageInfo {
    int64 id = 1;
    // 业务ID
    int64 package_id = 2;
    // 学校id
    int64 inst_id = 3;
    // 套餐类型（1-宝贝在线 2-成长档案）
    uint32 package_type = 4;
    // 套餐种类（0-系统套餐 1-自定义套餐）
    uint32 package_class = 5;
    // 套餐名
    string package_name = 6;
    // 通道数
    uint32 passage_num = 7;
    // 原价格(单位:分)
    uint32 price = 8;
    // 优惠价格(单位:分)
    uint32 discount_price = 9;
    // 起购月数
    uint32 least_months = 10;
    // 是否显示（1-显示 0-隐藏）
    uint32 display = 11;
}

message OpenSunBabyServiceReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 服务套餐id
    int64 package_id = 2;
    // 开通月数(1年=12个月)
    uint32 active_months = 3;
    // 总费用
    uint32 order_amount = 4;
    // 付款模式(0-线上 1-线下)
    uint32 pay_mode = 5;
    // 付款方式(0-未知 1-微信 2-支付宝 3-银行转账)
    uint32 pay_method = 6;
    // 支付状态(0-未支付 1-已支付 2-已退款 3-已过期)
    uint32 pay_status = 7;
    // 付款凭证图片地址
    string pay_proof_url = 8;
    // 来源(0-运营后台 1-园丁端App 2-家长端App 3-园长后台)
    uint32 source = 9;
    // 备注
    string remark = 10;
    // 支付时间(线下支付不需要传支付时间)
    uint32 pay_time = 11;
    // 渠道 （1：园长后台，2：app端）
    uint32 channel = 12 [(validate.rules).uint32 = {in: [1,2,3]}];
    // 创建人id
    int64 operator_id = 13 [(validate.rules).int64.gt = 0];
    // 创建人姓名
    string operator_name = 14 [(validate.rules).string.min_len = 1];
    // ip地址
    string ip = 15 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message RenewalSunBabyServiceReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 原服务套餐id
    int64 package_id = 2;
    // 开通月数(1年=12个月)
    uint32 active_months = 3;
    // 总费用
    uint32 order_amount = 4;
    // 付款模式(0-线上 1-线下)
    uint32 pay_mode = 5;
    // 付款方式(0-未知 1-微信 2-支付宝 3-银行转账)
    uint32 pay_method = 6;
    // 支付状态(0-未支付 1-已支付 2-已退款 3-已过期)
    uint32 pay_status = 7;
    // 付款凭证图片地址
    string pay_proof_url = 8;
    // 来源(0-运营后台 1-园丁端App 2-家长端App 3-园长后台)
    uint32 source = 9;
    // 备注
    string remark = 10;
    // 支付时间(线下支付不需要传支付时间)
    uint32 pay_time = 11;
    // 渠道 （1：园长后台，2：app端）
    uint32 channel = 12 [(validate.rules).uint32 = {in: [1,2,3]}];
    // 创建人id
    int64 operator_id = 13 [(validate.rules).int64.gt = 0];
    // 创建人姓名
    string operator_name = 14 [(validate.rules).string.min_len = 1];
    // ip地址
    string ip = 15 [(validate.rules).string = {min_len: 1, max_len: 20}];
}
message UpgradeSunBabyServiceReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 原服务订单id
    int64 old_order_id = 2;
    // 现服务套餐id
    int64 new_package_id = 3;
    // 总费用
    uint32 order_amount = 4;
    // 付款模式(0-线上 1-线下)
    uint32 pay_mode = 5;
    // 付款方式(0-未知 1-微信 2-支付宝 3-银行转账)
    uint32 pay_method = 6;
    // 支付状态(0-未支付 1-已支付 2-已退款 3-已过期)
    uint32 pay_status = 7;
    // 付款凭证图片地址
    string pay_proof_url = 8;
    // 来源(0-运营后台 1-园丁端App 2-家长端App 3-园长后台)
    uint32 source = 9;
    // 备注
    string remark = 10;
    // 支付时间(线下支付不需要传支付时间)
    uint32 pay_time = 11;
    // 渠道 （1：园长后台，2：app端）
    uint32 channel = 12 [(validate.rules).uint32 = {in: [1,2,3]}];
    // 创建人id
    int64 operator_id = 13 [(validate.rules).int64.gt = 0];
    // 创建人姓名
    string operator_name = 14 [(validate.rules).string.min_len = 1];
    // ip地址
    string ip = 15 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message ExamineInstOrderReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 订单id
    int64 order_id = 2;
    // 审核结果 (1-审核通过 2-取消订单)
    uint32 examine_result = 3;
    // 支付方式
    uint32 pay_method = 4;
    // 转账时间
    uint32 pay_time = 5;
    // 备注信息
    string remark = 6;
    // 以下字段到时候真正开发此接口时再完善
    // 套餐
    // 年限
    // 订单金额
    // 开通路数
    // 付费凭证
    // 渠道 （1：园长后台，2：app端）
    uint32 channel = 7 [(validate.rules).uint32 = {in: [1,2,3]}];
    // 创建人id
    int64 operator_id = 8 [(validate.rules).int64.gt = 0];
    // 创建人姓名
    string operator_name = 9 [(validate.rules).string.min_len = 1];
    // ip地址
    string ip = 10 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message StaffOpenVideoForParentReq {
    message ParentStudentPair {
        // 家长id
        int64 parent_id = 1;
        // 学生id
        int64 student_id = 2;
    }
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 家长学生列表
    repeated ParentStudentPair list = 2;
    // 套餐类型(0-按天数 1-按月数)
    uint32 package_type = 3;
    // 开通时长(天数或月数)
    uint32 package_duration = 4;
    // 渠道 （1：园长后台，2：app端）
    uint32 channel = 5 [(validate.rules).uint32 = {in: [1,2,3]}];
    // 创建人id
    int64 operator_id = 6 [(validate.rules).int64.gt = 0];
    // 创建人姓名
    string operator_name = 7 [(validate.rules).string.min_len = 1];
    // ip地址
    string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message InstIsOpenSunBabyReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 服务类型(1-阳光宝贝 2-成长档案 等等)
    uint32 service_type = 2;
}
message InstIsOpenSunBabyRsp {
    // 是否开通
    bool is_open = 1;
    // 审核状态(1-待审核 2-已审核)
    uint32 examine_status = 2;
    // 开通起始日期(时间戳)
    int64 open_start_date = 3;
    // 开通截止日期(时间戳)
    int64 open_end_date = 4;
}

message ParentIsOpenVideoReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 家长id
    int64 parent_id = 2;
    // 学生id
    int64 student_id = 3;
    // 服务类型(1-阳光宝贝 2-成长档案 等等)
    uint32 service_type = 4;
}
message ParentIsOpenVideoRsp {
    // 是否开通
    bool is_open = 1;
    // 开通截止日期(时间戳)
    int64 open_end_date = 2;
}

message ListOrderReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 服务类型(1-阳光宝贝 2-成长档案 等等)
    uint32 service_type = 2;
    // 当前页
    int32 page = 3 [(validate.rules).int32.gt = 0];
    // 每页条数
    uint32 per_page = 4 [(validate.rules).uint32.gt = 0];
}
message ListOrderRsp {
    message OrderSimpleInfo {
        int64 id = 1;
        // 订单id
        int64 order_id = 2;
        // 订单名
        string order_category_name = 3;
        // 订单金额(单位:分)
        uint32 amount = 4;
    }
    // 总条数
    int64 total = 1;
    // 当前页
    int32 page = 2;
    // 每页条数
    uint32 per_page = 3;
    // 订单列表
    repeated OrderSimpleInfo list = 4;
}

message GetOrderReq {
    // 学校id
    int64 inst_id = 1 [(validate.rules).int64.gt = 0];
    // 订单id
    int64 order_id = 2 [(validate.rules).int64.gt = 0];
}
message GetOrderRsp {
    // 订单信息
    OrderInfo order = 1;
    // 订单子信息
    OrderExtra order_extra = 2;
    // 套餐信息
    PackageInfo package = 3;
}

message OrderInfo {
    int64 id = 1;
    // 订单id
    int64 order_id = 2;
    // 订单编号
    string order_no = 3;
    // 订单名
    string order_category_name = 4;
    // 订单金额(单位:分)
    uint32 order_amount = 5;
    // 实际支付金额(单位: 分)
    uint32 pay_amount = 6;
    // 订单状态(0-待财务审核 1-待出单 2-待发货 3-已发货 4-部分发货 5-已作废 6-已取消)
    uint32 order_status = 7;
    // 付款凭证(付款截图文件地址)
    string pay_proof_url = 8;
    // 备注
    string remark = 9;
    // 支付时间
    int64 pay_time = 10;
    int64 create_time = 11;
    int64 update_time = 12;
}
message OrderExtra {
    int64 id = 1;
    // 套餐ID
    int64 package_id = 2;
    // 实际开通路数
    uint32 active_passage_num = 3;
    // 开通月数(1年=12个月)
    uint32 active_months = 4;
    // 生效日期
    int64 start_date = 5;
    // 到期日期
    int64 end_date = 6;
    // 审核时间
    int64 examine_time = 7;
}