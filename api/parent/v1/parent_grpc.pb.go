// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: parent/v1/parent.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Parent_GetRelParentStudentInfo_FullMethodName     = "/api.parent.v1.Parent/GetRelParentStudentInfo"
	Parent_CreateParent_FullMethodName                = "/api.parent.v1.Parent/CreateParent"
	Parent_CreateParentTwo_FullMethodName             = "/api.parent.v1.Parent/CreateParentTwo"
	Parent_UpdateParent_FullMethodName                = "/api.parent.v1.Parent/UpdateParent"
	Parent_UpdateBasicParent_FullMethodName           = "/api.parent.v1.Parent/UpdateBasicParent"
	Parent_ChangeParentMobile_FullMethodName          = "/api.parent.v1.Parent/ChangeParentMobile"
	Parent_UpdateParentStudentRelation_FullMethodName = "/api.parent.v1.Parent/UpdateParentStudentRelation"
	Parent_DeleteParent_FullMethodName                = "/api.parent.v1.Parent/DeleteParent"
	Parent_ResetPassword_FullMethodName               = "/api.parent.v1.Parent/ResetPassword"
	Parent_VerifyParentMobile_FullMethodName          = "/api.parent.v1.Parent/VerifyParentMobile"
	Parent_ListParentServiceInfo_FullMethodName       = "/api.parent.v1.Parent/ListParentServiceInfo"
	Parent_ListParentStudentInfo_FullMethodName       = "/api.parent.v1.Parent/ListParentStudentInfo"
)

// ParentClient is the client API for Parent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ParentClient interface {
	// 批量获取家长及关联学生信息（此接口不返回班级信息），后续请使用ListParentStudentInfo接口
	GetRelParentStudentInfo(ctx context.Context, in *GetRelParentStudentInfoReq, opts ...grpc.CallOption) (*GetRelParentStudentInfoRsp, error)
	// 添加家长信息
	CreateParent(ctx context.Context, in *CreateParentReq, opts ...grpc.CallOption) (*CreateParentRsp, error)
	// 批量创建家长并返回密码
	CreateParentTwo(ctx context.Context, in *CreateParentReq, opts ...grpc.CallOption) (*CreateParentTwoRsp, error)
	// 更新家长信息
	UpdateParent(ctx context.Context, in *UpdateParentReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新家长基础信息
	UpdateBasicParent(ctx context.Context, in *UpdateBasicParentReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更换家长手机号
	ChangeParentMobile(ctx context.Context, in *ChangeParentMobileReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新家长与学生的关系
	UpdateParentStudentRelation(ctx context.Context, in *UpdateParentStudentRelationReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除家长
	DeleteParent(ctx context.Context, in *DeleteParentReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 重置家长密码
	ResetPassword(ctx context.Context, in *ResetParentPasswordReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 校验家长手机号是否在系统中(同一类用户下)
	VerifyParentMobile(ctx context.Context, in *VerifyParentMobileReq, opts ...grpc.CallOption) (*VerifyParentMobileRsp, error)
	// 获取家长服务开通信息列表
	ListParentServiceInfo(ctx context.Context, in *ListParentServiceInfoReq, opts ...grpc.CallOption) (*ListParentServiceInfoRsp, error)
	// 根据家长和学生id组合列表获取在职家长学生明细信息（删除的家长关联关系也会查询）
	ListParentStudentInfo(ctx context.Context, in *ListParentStudentInfoReq, opts ...grpc.CallOption) (*ListParentStudentInfoRsp, error)
}

type parentClient struct {
	cc grpc.ClientConnInterface
}

func NewParentClient(cc grpc.ClientConnInterface) ParentClient {
	return &parentClient{cc}
}

func (c *parentClient) GetRelParentStudentInfo(ctx context.Context, in *GetRelParentStudentInfoReq, opts ...grpc.CallOption) (*GetRelParentStudentInfoRsp, error) {
	out := new(GetRelParentStudentInfoRsp)
	err := c.cc.Invoke(ctx, Parent_GetRelParentStudentInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) CreateParent(ctx context.Context, in *CreateParentReq, opts ...grpc.CallOption) (*CreateParentRsp, error) {
	out := new(CreateParentRsp)
	err := c.cc.Invoke(ctx, Parent_CreateParent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) CreateParentTwo(ctx context.Context, in *CreateParentReq, opts ...grpc.CallOption) (*CreateParentTwoRsp, error) {
	out := new(CreateParentTwoRsp)
	err := c.cc.Invoke(ctx, Parent_CreateParentTwo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) UpdateParent(ctx context.Context, in *UpdateParentReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Parent_UpdateParent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) UpdateBasicParent(ctx context.Context, in *UpdateBasicParentReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Parent_UpdateBasicParent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) ChangeParentMobile(ctx context.Context, in *ChangeParentMobileReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Parent_ChangeParentMobile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) UpdateParentStudentRelation(ctx context.Context, in *UpdateParentStudentRelationReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Parent_UpdateParentStudentRelation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) DeleteParent(ctx context.Context, in *DeleteParentReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Parent_DeleteParent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) ResetPassword(ctx context.Context, in *ResetParentPasswordReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Parent_ResetPassword_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) VerifyParentMobile(ctx context.Context, in *VerifyParentMobileReq, opts ...grpc.CallOption) (*VerifyParentMobileRsp, error) {
	out := new(VerifyParentMobileRsp)
	err := c.cc.Invoke(ctx, Parent_VerifyParentMobile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) ListParentServiceInfo(ctx context.Context, in *ListParentServiceInfoReq, opts ...grpc.CallOption) (*ListParentServiceInfoRsp, error) {
	out := new(ListParentServiceInfoRsp)
	err := c.cc.Invoke(ctx, Parent_ListParentServiceInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parentClient) ListParentStudentInfo(ctx context.Context, in *ListParentStudentInfoReq, opts ...grpc.CallOption) (*ListParentStudentInfoRsp, error) {
	out := new(ListParentStudentInfoRsp)
	err := c.cc.Invoke(ctx, Parent_ListParentStudentInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ParentServer is the server API for Parent service.
// All implementations must embed UnimplementedParentServer
// for forward compatibility
type ParentServer interface {
	// 批量获取家长及关联学生信息（此接口不返回班级信息），后续请使用ListParentStudentInfo接口
	GetRelParentStudentInfo(context.Context, *GetRelParentStudentInfoReq) (*GetRelParentStudentInfoRsp, error)
	// 添加家长信息
	CreateParent(context.Context, *CreateParentReq) (*CreateParentRsp, error)
	// 批量创建家长并返回密码
	CreateParentTwo(context.Context, *CreateParentReq) (*CreateParentTwoRsp, error)
	// 更新家长信息
	UpdateParent(context.Context, *UpdateParentReq) (*emptypb.Empty, error)
	// 更新家长基础信息
	UpdateBasicParent(context.Context, *UpdateBasicParentReq) (*emptypb.Empty, error)
	// 更换家长手机号
	ChangeParentMobile(context.Context, *ChangeParentMobileReq) (*emptypb.Empty, error)
	// 更新家长与学生的关系
	UpdateParentStudentRelation(context.Context, *UpdateParentStudentRelationReq) (*emptypb.Empty, error)
	// 删除家长
	DeleteParent(context.Context, *DeleteParentReq) (*emptypb.Empty, error)
	// 重置家长密码
	ResetPassword(context.Context, *ResetParentPasswordReq) (*emptypb.Empty, error)
	// 校验家长手机号是否在系统中(同一类用户下)
	VerifyParentMobile(context.Context, *VerifyParentMobileReq) (*VerifyParentMobileRsp, error)
	// 获取家长服务开通信息列表
	ListParentServiceInfo(context.Context, *ListParentServiceInfoReq) (*ListParentServiceInfoRsp, error)
	// 根据家长和学生id组合列表获取在职家长学生明细信息（删除的家长关联关系也会查询）
	ListParentStudentInfo(context.Context, *ListParentStudentInfoReq) (*ListParentStudentInfoRsp, error)
	mustEmbedUnimplementedParentServer()
}

// UnimplementedParentServer must be embedded to have forward compatible implementations.
type UnimplementedParentServer struct {
}

func (UnimplementedParentServer) GetRelParentStudentInfo(context.Context, *GetRelParentStudentInfoReq) (*GetRelParentStudentInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRelParentStudentInfo not implemented")
}
func (UnimplementedParentServer) CreateParent(context.Context, *CreateParentReq) (*CreateParentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateParent not implemented")
}
func (UnimplementedParentServer) CreateParentTwo(context.Context, *CreateParentReq) (*CreateParentTwoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateParentTwo not implemented")
}
func (UnimplementedParentServer) UpdateParent(context.Context, *UpdateParentReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateParent not implemented")
}
func (UnimplementedParentServer) UpdateBasicParent(context.Context, *UpdateBasicParentReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBasicParent not implemented")
}
func (UnimplementedParentServer) ChangeParentMobile(context.Context, *ChangeParentMobileReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeParentMobile not implemented")
}
func (UnimplementedParentServer) UpdateParentStudentRelation(context.Context, *UpdateParentStudentRelationReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateParentStudentRelation not implemented")
}
func (UnimplementedParentServer) DeleteParent(context.Context, *DeleteParentReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteParent not implemented")
}
func (UnimplementedParentServer) ResetPassword(context.Context, *ResetParentPasswordReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetPassword not implemented")
}
func (UnimplementedParentServer) VerifyParentMobile(context.Context, *VerifyParentMobileReq) (*VerifyParentMobileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyParentMobile not implemented")
}
func (UnimplementedParentServer) ListParentServiceInfo(context.Context, *ListParentServiceInfoReq) (*ListParentServiceInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListParentServiceInfo not implemented")
}
func (UnimplementedParentServer) ListParentStudentInfo(context.Context, *ListParentStudentInfoReq) (*ListParentStudentInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListParentStudentInfo not implemented")
}
func (UnimplementedParentServer) mustEmbedUnimplementedParentServer() {}

// UnsafeParentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ParentServer will
// result in compilation errors.
type UnsafeParentServer interface {
	mustEmbedUnimplementedParentServer()
}

func RegisterParentServer(s grpc.ServiceRegistrar, srv ParentServer) {
	s.RegisterService(&Parent_ServiceDesc, srv)
}

func _Parent_GetRelParentStudentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRelParentStudentInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).GetRelParentStudentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_GetRelParentStudentInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).GetRelParentStudentInfo(ctx, req.(*GetRelParentStudentInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_CreateParent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateParentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).CreateParent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_CreateParent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).CreateParent(ctx, req.(*CreateParentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_CreateParentTwo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateParentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).CreateParentTwo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_CreateParentTwo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).CreateParentTwo(ctx, req.(*CreateParentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_UpdateParent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateParentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).UpdateParent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_UpdateParent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).UpdateParent(ctx, req.(*UpdateParentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_UpdateBasicParent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBasicParentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).UpdateBasicParent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_UpdateBasicParent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).UpdateBasicParent(ctx, req.(*UpdateBasicParentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_ChangeParentMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeParentMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).ChangeParentMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_ChangeParentMobile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).ChangeParentMobile(ctx, req.(*ChangeParentMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_UpdateParentStudentRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateParentStudentRelationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).UpdateParentStudentRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_UpdateParentStudentRelation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).UpdateParentStudentRelation(ctx, req.(*UpdateParentStudentRelationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_DeleteParent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteParentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).DeleteParent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_DeleteParent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).DeleteParent(ctx, req.(*DeleteParentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_ResetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetParentPasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).ResetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_ResetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).ResetPassword(ctx, req.(*ResetParentPasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_VerifyParentMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyParentMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).VerifyParentMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_VerifyParentMobile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).VerifyParentMobile(ctx, req.(*VerifyParentMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_ListParentServiceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListParentServiceInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).ListParentServiceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_ListParentServiceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).ListParentServiceInfo(ctx, req.(*ListParentServiceInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Parent_ListParentStudentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListParentStudentInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParentServer).ListParentStudentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Parent_ListParentStudentInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParentServer).ListParentStudentInfo(ctx, req.(*ListParentStudentInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Parent_ServiceDesc is the grpc.ServiceDesc for Parent service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Parent_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.parent.v1.Parent",
	HandlerType: (*ParentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRelParentStudentInfo",
			Handler:    _Parent_GetRelParentStudentInfo_Handler,
		},
		{
			MethodName: "CreateParent",
			Handler:    _Parent_CreateParent_Handler,
		},
		{
			MethodName: "CreateParentTwo",
			Handler:    _Parent_CreateParentTwo_Handler,
		},
		{
			MethodName: "UpdateParent",
			Handler:    _Parent_UpdateParent_Handler,
		},
		{
			MethodName: "UpdateBasicParent",
			Handler:    _Parent_UpdateBasicParent_Handler,
		},
		{
			MethodName: "ChangeParentMobile",
			Handler:    _Parent_ChangeParentMobile_Handler,
		},
		{
			MethodName: "UpdateParentStudentRelation",
			Handler:    _Parent_UpdateParentStudentRelation_Handler,
		},
		{
			MethodName: "DeleteParent",
			Handler:    _Parent_DeleteParent_Handler,
		},
		{
			MethodName: "ResetPassword",
			Handler:    _Parent_ResetPassword_Handler,
		},
		{
			MethodName: "VerifyParentMobile",
			Handler:    _Parent_VerifyParentMobile_Handler,
		},
		{
			MethodName: "ListParentServiceInfo",
			Handler:    _Parent_ListParentServiceInfo_Handler,
		},
		{
			MethodName: "ListParentStudentInfo",
			Handler:    _Parent_ListParentStudentInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "parent/v1/parent.proto",
}
