// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.17.3
// source: parent/v1/parent.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 根据家长和学生id组合列表获取家长学生明细信息请求参数
type ListParentStudentInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否返回班级年级信息
	ReturnSchDept bool `protobuf:"varint,1,opt,name=return_sch_dept,json=returnSchDept,proto3" json:"return_sch_dept,omitempty"`
	// 关联信息
	Relations []*RelParentStudentId `protobuf:"bytes,2,rep,name=relations,proto3" json:"relations,omitempty"`
}

func (x *ListParentStudentInfoReq) Reset() {
	*x = ListParentStudentInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListParentStudentInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListParentStudentInfoReq) ProtoMessage() {}

func (x *ListParentStudentInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListParentStudentInfoReq.ProtoReflect.Descriptor instead.
func (*ListParentStudentInfoReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{0}
}

func (x *ListParentStudentInfoReq) GetReturnSchDept() bool {
	if x != nil {
		return x.ReturnSchDept
	}
	return false
}

func (x *ListParentStudentInfoReq) GetRelations() []*RelParentStudentId {
	if x != nil {
		return x.Relations
	}
	return nil
}

// 根据家长和学生id组合列表获取家长学生明细信息响应参数
type ListParentStudentInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 信息列表
	List []*RelParentStudentInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListParentStudentInfoRsp) Reset() {
	*x = ListParentStudentInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListParentStudentInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListParentStudentInfoRsp) ProtoMessage() {}

func (x *ListParentStudentInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListParentStudentInfoRsp.ProtoReflect.Descriptor instead.
func (*ListParentStudentInfoRsp) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{1}
}

func (x *ListParentStudentInfoRsp) GetList() []*RelParentStudentInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 家长服务开通记录请求结构
type ListParentServiceInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 服务类型
	ServiceType uint32 `protobuf:"varint,1,opt,name=service_type,json=serviceType,proto3" json:"service_type,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,2,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 班级id
	SchDeptIds []int64 `protobuf:"varint,3,rep,packed,name=sch_dept_ids,json=schDeptIds,proto3" json:"sch_dept_ids,omitempty"`
	// 开通状态（1：未开通，2：已开通）
	ActivationStatus int32 `protobuf:"varint,4,opt,name=activation_status,json=activationStatus,proto3" json:"activation_status,omitempty"`
	// 有效期（1：7天以内，2：30天以内，3：90天以内，4：超过90天，5：已过期）
	ValidType int32 `protobuf:"varint,5,opt,name=valid_type,json=validType,proto3" json:"valid_type,omitempty"`
	// 页数，不填默认不分页
	Page int32 `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数，不填默认不分页
	PerPage int32 `protobuf:"varint,7,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,8,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *ListParentServiceInfoReq) Reset() {
	*x = ListParentServiceInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListParentServiceInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListParentServiceInfoReq) ProtoMessage() {}

func (x *ListParentServiceInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListParentServiceInfoReq.ProtoReflect.Descriptor instead.
func (*ListParentServiceInfoReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{2}
}

func (x *ListParentServiceInfoReq) GetServiceType() uint32 {
	if x != nil {
		return x.ServiceType
	}
	return 0
}

func (x *ListParentServiceInfoReq) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *ListParentServiceInfoReq) GetSchDeptIds() []int64 {
	if x != nil {
		return x.SchDeptIds
	}
	return nil
}

func (x *ListParentServiceInfoReq) GetActivationStatus() int32 {
	if x != nil {
		return x.ActivationStatus
	}
	return 0
}

func (x *ListParentServiceInfoReq) GetValidType() int32 {
	if x != nil {
		return x.ValidType
	}
	return 0
}

func (x *ListParentServiceInfoReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListParentServiceInfoReq) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListParentServiceInfoReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 家长开通状态返回结构
type ListParentServiceInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 学生开通记录列表
	List []*StudentActivationInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListParentServiceInfoRsp) Reset() {
	*x = ListParentServiceInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListParentServiceInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListParentServiceInfoRsp) ProtoMessage() {}

func (x *ListParentServiceInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListParentServiceInfoRsp.ProtoReflect.Descriptor instead.
func (*ListParentServiceInfoRsp) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{3}
}

func (x *ListParentServiceInfoRsp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListParentServiceInfoRsp) GetList() []*StudentActivationInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 学生开通记录信息
type StudentActivationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	StudentId int64 `protobuf:"varint,1,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,2,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 性别
	Gender int32 `protobuf:"varint,3,opt,name=gender,proto3" json:"gender,omitempty"`
	// 学生头像
	Avatar string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 班级id
	ClassId int64 `protobuf:"varint,5,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 所在班级
	ClassName string `protobuf:"bytes,6,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级id
	GradeId int64 `protobuf:"varint,7,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	// 所在年级
	GradeName string `protobuf:"bytes,8,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
	// 学生家长列表
	ParentList []*ParentActivationInfo `protobuf:"bytes,9,rep,name=parent_list,json=parentList,proto3" json:"parent_list,omitempty"`
}

func (x *StudentActivationInfo) Reset() {
	*x = StudentActivationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StudentActivationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StudentActivationInfo) ProtoMessage() {}

func (x *StudentActivationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StudentActivationInfo.ProtoReflect.Descriptor instead.
func (*StudentActivationInfo) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{4}
}

func (x *StudentActivationInfo) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *StudentActivationInfo) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *StudentActivationInfo) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *StudentActivationInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *StudentActivationInfo) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *StudentActivationInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *StudentActivationInfo) GetGradeId() int64 {
	if x != nil {
		return x.GradeId
	}
	return 0
}

func (x *StudentActivationInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *StudentActivationInfo) GetParentList() []*ParentActivationInfo {
	if x != nil {
		return x.ParentList
	}
	return nil
}

// 家长开通信息
type ParentActivationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	ParentId int64 `protobuf:"varint,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 家长姓名
	ParentName string `protobuf:"bytes,2,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"`
	// 家长手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 家长与学生关系
	Relation string `protobuf:"bytes,4,opt,name=relation,proto3" json:"relation,omitempty"`
	// 家长开通状态（1：未开通，2：已开通）
	ActivationStatus uint32 `protobuf:"varint,5,opt,name=activation_status,json=activationStatus,proto3" json:"activation_status,omitempty"`
	// 家长开通截止时间
	ExpirationTime int64 `protobuf:"varint,6,opt,name=expiration_time,json=expirationTime,proto3" json:"expiration_time,omitempty"`
}

func (x *ParentActivationInfo) Reset() {
	*x = ParentActivationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParentActivationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParentActivationInfo) ProtoMessage() {}

func (x *ParentActivationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParentActivationInfo.ProtoReflect.Descriptor instead.
func (*ParentActivationInfo) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{5}
}

func (x *ParentActivationInfo) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *ParentActivationInfo) GetParentName() string {
	if x != nil {
		return x.ParentName
	}
	return ""
}

func (x *ParentActivationInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *ParentActivationInfo) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

func (x *ParentActivationInfo) GetActivationStatus() uint32 {
	if x != nil {
		return x.ActivationStatus
	}
	return 0
}

func (x *ParentActivationInfo) GetExpirationTime() int64 {
	if x != nil {
		return x.ExpirationTime
	}
	return 0
}

type VerifyParentMobileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 手机区号
	ReCode string `protobuf:"bytes,2,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile,omitempty"`
}

func (x *VerifyParentMobileReq) Reset() {
	*x = VerifyParentMobileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyParentMobileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyParentMobileReq) ProtoMessage() {}

func (x *VerifyParentMobileReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyParentMobileReq.ProtoReflect.Descriptor instead.
func (*VerifyParentMobileReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{6}
}

func (x *VerifyParentMobileReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *VerifyParentMobileReq) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *VerifyParentMobileReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

type VerifyParentMobileRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否在系统中
	Exist bool `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
}

func (x *VerifyParentMobileRsp) Reset() {
	*x = VerifyParentMobileRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyParentMobileRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyParentMobileRsp) ProtoMessage() {}

func (x *VerifyParentMobileRsp) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyParentMobileRsp.ProtoReflect.Descriptor instead.
func (*VerifyParentMobileRsp) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{7}
}

func (x *VerifyParentMobileRsp) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

type ChangeParentMobileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 原手机号
	OldMobile string `protobuf:"bytes,2,opt,name=old_mobile,json=oldMobile,proto3" json:"old_mobile,omitempty"`
	// 新手机区号
	ReCode string `protobuf:"bytes,3,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 新手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,5,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,6,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,7,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,8,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,9,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *ChangeParentMobileReq) Reset() {
	*x = ChangeParentMobileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeParentMobileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeParentMobileReq) ProtoMessage() {}

func (x *ChangeParentMobileReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeParentMobileReq.ProtoReflect.Descriptor instead.
func (*ChangeParentMobileReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{8}
}

func (x *ChangeParentMobileReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeParentMobileReq) GetOldMobile() string {
	if x != nil {
		return x.OldMobile
	}
	return ""
}

func (x *ChangeParentMobileReq) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *ChangeParentMobileReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *ChangeParentMobileReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ChangeParentMobileReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ChangeParentMobileReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *ChangeParentMobileReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ChangeParentMobileReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type UpdateParentStudentRelationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	ParentId int64 `protobuf:"varint,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,2,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 关系(1：爸爸，2：妈妈，3：爷爷，4：奶奶，5：外公，6：外婆，7：伯父8：伯母，9：叔叔，10：婶婶，11：姑父，12：姑母，13：舅舅 14：舅妈，15：姐姐，16：哥哥，17：嫂嫂，18：姨妈，19：姑丈，20：其他)
	Relation uint32 `protobuf:"varint,3,opt,name=relation,proto3" json:"relation,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateParentStudentRelationReq) Reset() {
	*x = UpdateParentStudentRelationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateParentStudentRelationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateParentStudentRelationReq) ProtoMessage() {}

func (x *UpdateParentStudentRelationReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateParentStudentRelationReq.ProtoReflect.Descriptor instead.
func (*UpdateParentStudentRelationReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateParentStudentRelationReq) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *UpdateParentStudentRelationReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *UpdateParentStudentRelationReq) GetRelation() uint32 {
	if x != nil {
		return x.Relation
	}
	return 0
}

func (x *UpdateParentStudentRelationReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateParentStudentRelationReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateParentStudentRelationReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateParentStudentRelationReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateParentStudentRelationReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 批量获取家长信息
type GetRelParentStudentInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 家长和学生
	ParentStudentId []*RelParentStudentId `protobuf:"bytes,2,rep,name=parent_student_id,json=parentStudentId,proto3" json:"parent_student_id,omitempty"`
}

func (x *GetRelParentStudentInfoReq) Reset() {
	*x = GetRelParentStudentInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelParentStudentInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelParentStudentInfoReq) ProtoMessage() {}

func (x *GetRelParentStudentInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelParentStudentInfoReq.ProtoReflect.Descriptor instead.
func (*GetRelParentStudentInfoReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{10}
}

func (x *GetRelParentStudentInfoReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetRelParentStudentInfoReq) GetParentStudentId() []*RelParentStudentId {
	if x != nil {
		return x.ParentStudentId
	}
	return nil
}

type RelParentStudentId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	ParentId int64 `protobuf:"varint,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 关联的学生id
	StudentId int64 `protobuf:"varint,2,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
}

func (x *RelParentStudentId) Reset() {
	*x = RelParentStudentId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelParentStudentId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelParentStudentId) ProtoMessage() {}

func (x *RelParentStudentId) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelParentStudentId.ProtoReflect.Descriptor instead.
func (*RelParentStudentId) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{11}
}

func (x *RelParentStudentId) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *RelParentStudentId) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

type GetRelParentStudentInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 信息列表
	Data []*RelParentStudentInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetRelParentStudentInfoRsp) Reset() {
	*x = GetRelParentStudentInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelParentStudentInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelParentStudentInfoRsp) ProtoMessage() {}

func (x *GetRelParentStudentInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelParentStudentInfoRsp.ProtoReflect.Descriptor instead.
func (*GetRelParentStudentInfoRsp) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{12}
}

func (x *GetRelParentStudentInfoRsp) GetData() []*RelParentStudentInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 家长及关联学生的基本信息
type RelParentStudentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长信息
	Parent *ParentInfo `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// 学生信息
	Student *StudentInfo `protobuf:"bytes,2,opt,name=student,proto3" json:"student,omitempty"`
	// 家长与学生的关系
	Relation string `protobuf:"bytes,3,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *RelParentStudentInfo) Reset() {
	*x = RelParentStudentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelParentStudentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelParentStudentInfo) ProtoMessage() {}

func (x *RelParentStudentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelParentStudentInfo.ProtoReflect.Descriptor instead.
func (*RelParentStudentInfo) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{13}
}

func (x *RelParentStudentInfo) GetParent() *ParentInfo {
	if x != nil {
		return x.Parent
	}
	return nil
}

func (x *RelParentStudentInfo) GetStudent() *StudentInfo {
	if x != nil {
		return x.Student
	}
	return nil
}

func (x *RelParentStudentInfo) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

type ParentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 家长姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 手机区号
	ReCode string `protobuf:"bytes,3,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
}

func (x *ParentInfo) Reset() {
	*x = ParentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParentInfo) ProtoMessage() {}

func (x *ParentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParentInfo.ProtoReflect.Descriptor instead.
func (*ParentInfo) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{14}
}

func (x *ParentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ParentInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ParentInfo) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *ParentInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *ParentInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type StudentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 性别
	Gender int32 `protobuf:"varint,3,opt,name=gender,proto3" json:"gender,omitempty"`
	// 班级信息
	ClassInfo *ClassInfo `protobuf:"bytes,4,opt,name=class_info,json=classInfo,proto3" json:"class_info,omitempty"`
}

func (x *StudentInfo) Reset() {
	*x = StudentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StudentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StudentInfo) ProtoMessage() {}

func (x *StudentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StudentInfo.ProtoReflect.Descriptor instead.
func (*StudentInfo) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{15}
}

func (x *StudentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StudentInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StudentInfo) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *StudentInfo) GetClassInfo() *ClassInfo {
	if x != nil {
		return x.ClassInfo
	}
	return nil
}

type ClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,2,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级id
	GradeId int64 `protobuf:"varint,3,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,4,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
}

func (x *ClassInfo) Reset() {
	*x = ClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassInfo) ProtoMessage() {}

func (x *ClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassInfo.ProtoReflect.Descriptor instead.
func (*ClassInfo) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{16}
}

func (x *ClassInfo) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *ClassInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *ClassInfo) GetGradeId() int64 {
	if x != nil {
		return x.GradeId
	}
	return 0
}

func (x *ClassInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

// 创建家长请求参数
type CreateParentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	StudentId int64 `protobuf:"varint,1,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 新增家长列表
	ParentList []*ParentDetail `protobuf:"bytes,2,rep,name=parent_list,json=parentList,proto3" json:"parent_list,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,6,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *CreateParentReq) Reset() {
	*x = CreateParentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateParentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateParentReq) ProtoMessage() {}

func (x *CreateParentReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateParentReq.ProtoReflect.Descriptor instead.
func (*CreateParentReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{17}
}

func (x *CreateParentReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *CreateParentReq) GetParentList() []*ParentDetail {
	if x != nil {
		return x.ParentList
	}
	return nil
}

func (x *CreateParentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *CreateParentReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *CreateParentReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateParentReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *CreateParentReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 创建家长返回参数
type CreateParentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 失败列表，若为空则表示全部成功
	FailureList []*ParentFailure `protobuf:"bytes,1,rep,name=failure_list,json=failureList,proto3" json:"failure_list,omitempty"`
}

func (x *CreateParentRsp) Reset() {
	*x = CreateParentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateParentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateParentRsp) ProtoMessage() {}

func (x *CreateParentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateParentRsp.ProtoReflect.Descriptor instead.
func (*CreateParentRsp) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{18}
}

func (x *CreateParentRsp) GetFailureList() []*ParentFailure {
	if x != nil {
		return x.FailureList
	}
	return nil
}

// 失败信息
type ParentFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长姓名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 失败原因
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *ParentFailure) Reset() {
	*x = ParentFailure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParentFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParentFailure) ProtoMessage() {}

func (x *ParentFailure) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParentFailure.ProtoReflect.Descriptor instead.
func (*ParentFailure) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{19}
}

func (x *ParentFailure) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ParentFailure) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type CreateParentTwoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 密码信息或失败信息列表
	List []*CreateParentTwoRsp_ParentResInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *CreateParentTwoRsp) Reset() {
	*x = CreateParentTwoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateParentTwoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateParentTwoRsp) ProtoMessage() {}

func (x *CreateParentTwoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateParentTwoRsp.ProtoReflect.Descriptor instead.
func (*CreateParentTwoRsp) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{20}
}

func (x *CreateParentTwoRsp) GetList() []*CreateParentTwoRsp_ParentResInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type ParentDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 关系(1：爸爸，2：妈妈，3：爷爷，4：奶奶，5：外公，6：外婆，7：伯父8：伯母，9：叔叔，10：婶婶，11：姑父，12：姑母，13：舅舅 14：舅妈，15：姐姐，16：哥哥，17：嫂嫂，18：姨妈，19：姑丈，20：其他)
	Relation uint32 `protobuf:"varint,1,opt,name=relation,proto3" json:"relation,omitempty"`
	// 家长姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 手机区号，默认需填86
	ReCode string `protobuf:"bytes,3,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 是否是第一联系人
	IsFirst uint32 `protobuf:"varint,5,opt,name=is_first,json=isFirst,proto3" json:"is_first,omitempty"`
	// 来源
	Source uint32 `protobuf:"varint,6,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *ParentDetail) Reset() {
	*x = ParentDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParentDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParentDetail) ProtoMessage() {}

func (x *ParentDetail) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParentDetail.ProtoReflect.Descriptor instead.
func (*ParentDetail) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{21}
}

func (x *ParentDetail) GetRelation() uint32 {
	if x != nil {
		return x.Relation
	}
	return 0
}

func (x *ParentDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ParentDetail) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *ParentDetail) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *ParentDetail) GetIsFirst() uint32 {
	if x != nil {
		return x.IsFirst
	}
	return 0
}

func (x *ParentDetail) GetSource() uint32 {
	if x != nil {
		return x.Source
	}
	return 0
}

// 更新家长请求参数
type UpdateParentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生id
	StuId int64 `protobuf:"varint,2,opt,name=stu_id,json=stuId,proto3" json:"stu_id,omitempty"`
	// 家长姓名，限30字符
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 手机区号
	ReCode string `protobuf:"bytes,4,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 关系(1：爸爸，2：妈妈，3：爷爷，4：奶奶，5：外公，6：外婆，7：伯父8：伯母，9：叔叔，10：婶婶，11：姑父，12：姑母，13：舅舅 14：舅妈，15：姐姐，16：哥哥，17：嫂嫂，18：姨妈，19：姑丈，20：其他)
	Relation uint32 `protobuf:"varint,6,opt,name=relation,proto3" json:"relation,omitempty"`
	// 是否是第一联系人
	IsFirst uint32 `protobuf:"varint,7,opt,name=is_first,json=isFirst,proto3" json:"is_first,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,8,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,9,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,10,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,11,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,12,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateParentReq) Reset() {
	*x = UpdateParentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateParentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateParentReq) ProtoMessage() {}

func (x *UpdateParentReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateParentReq.ProtoReflect.Descriptor instead.
func (*UpdateParentReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateParentReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateParentReq) GetStuId() int64 {
	if x != nil {
		return x.StuId
	}
	return 0
}

func (x *UpdateParentReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateParentReq) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *UpdateParentReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UpdateParentReq) GetRelation() uint32 {
	if x != nil {
		return x.Relation
	}
	return 0
}

func (x *UpdateParentReq) GetIsFirst() uint32 {
	if x != nil {
		return x.IsFirst
	}
	return 0
}

func (x *UpdateParentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateParentReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateParentReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateParentReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateParentReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type UpdateBasicParentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 家长姓名，限30字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateBasicParentReq) Reset() {
	*x = UpdateBasicParentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBasicParentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBasicParentReq) ProtoMessage() {}

func (x *UpdateBasicParentReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBasicParentReq.ProtoReflect.Descriptor instead.
func (*UpdateBasicParentReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateBasicParentReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateBasicParentReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateBasicParentReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateBasicParentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateBasicParentReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateBasicParentReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateBasicParentReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateBasicParentReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 删除家长请求参数
type DeleteParentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学生ID
	StuId int64 `protobuf:"varint,2,opt,name=stu_id,json=stuId,proto3" json:"stu_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,6,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
	// 是否是亲友团场景删除家长
	IsFamilyGroup bool `protobuf:"varint,8,opt,name=is_family_group,json=isFamilyGroup,proto3" json:"is_family_group,omitempty"`
}

func (x *DeleteParentReq) Reset() {
	*x = DeleteParentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteParentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteParentReq) ProtoMessage() {}

func (x *DeleteParentReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteParentReq.ProtoReflect.Descriptor instead.
func (*DeleteParentReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteParentReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteParentReq) GetStuId() int64 {
	if x != nil {
		return x.StuId
	}
	return 0
}

func (x *DeleteParentReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *DeleteParentReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *DeleteParentReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *DeleteParentReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *DeleteParentReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DeleteParentReq) GetIsFamilyGroup() bool {
	if x != nil {
		return x.IsFamilyGroup
	}
	return false
}

// 重置家长密码请求参数
type ResetParentPasswordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *ResetParentPasswordReq) Reset() {
	*x = ResetParentPasswordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetParentPasswordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetParentPasswordReq) ProtoMessage() {}

func (x *ResetParentPasswordReq) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetParentPasswordReq.ProtoReflect.Descriptor instead.
func (*ResetParentPasswordReq) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{25}
}

func (x *ResetParentPasswordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResetParentPasswordReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ResetParentPasswordReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ResetParentPasswordReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *ResetParentPasswordReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ResetParentPasswordReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type CreateParentTwoRsp_ParentResInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长姓名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 家长手机号
	Mobile string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 家长账号的密码
	Pwd string `protobuf:"bytes,3,opt,name=pwd,proto3" json:"pwd,omitempty"`
	// 失败原因
	Reason string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *CreateParentTwoRsp_ParentResInfo) Reset() {
	*x = CreateParentTwoRsp_ParentResInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_parent_v1_parent_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateParentTwoRsp_ParentResInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateParentTwoRsp_ParentResInfo) ProtoMessage() {}

func (x *CreateParentTwoRsp_ParentResInfo) ProtoReflect() protoreflect.Message {
	mi := &file_parent_v1_parent_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateParentTwoRsp_ParentResInfo.ProtoReflect.Descriptor instead.
func (*CreateParentTwoRsp_ParentResInfo) Descriptor() ([]byte, []int) {
	return file_parent_v1_parent_proto_rawDescGZIP(), []int{20, 0}
}

func (x *CreateParentTwoRsp_ParentResInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateParentTwoRsp_ParentResInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *CreateParentTwoRsp_ParentResInfo) GetPwd() string {
	if x != nil {
		return x.Pwd
	}
	return ""
}

func (x *CreateParentTwoRsp_ParentResInfo) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

var File_parent_v1_parent_proto protoreflect.FileDescriptor

var file_parent_v1_parent_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x01,
	0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x5f, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x53, 0x63, 0x68, 0x44, 0x65,
	0x70, 0x74, 0x12, 0x49, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x53, 0x0a,
	0x18, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0xc8, 0x02, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12,
	0x2a, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20, 0x00, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0c, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x63, 0x68, 0x44, 0x65, 0x70, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x38, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x1a, 0x06, 0x30, 0x00, 0x30, 0x01, 0x30, 0x02, 0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11,
	0xfa, 0x42, 0x0e, 0x1a, 0x0c, 0x30, 0x00, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x30, 0x04, 0x30,
	0x05, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69,
	0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0x6a, 0x0a,
	0x18, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x38, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xc3, 0x02, 0x0a, 0x15, 0x53, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0xde, 0x01, 0x0a, 0x14, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x87, 0x01, 0x0a, 0x15, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x07,
	0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x04, 0x52, 0x06, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x28, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72, 0x0b, 0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31, 0x30,
	0x7d, 0x24, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x22, 0x2d, 0x0a, 0x15, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74, 0x22, 0xe9, 0x02, 0x0a, 0x15, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x0a,
	0x6f, 0x6c, 0x64, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72, 0x0b, 0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31, 0x30,
	0x7d, 0x24, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x22, 0x0a,
	0x07, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x04, 0x52, 0x06, 0x72, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x28, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72, 0x0b, 0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31,
	0x30, 0x7d, 0x24, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69,
	0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xd1, 0x02, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x18,
	0x14, 0x28, 0x01, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x97, 0x01, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x11, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x12, 0x52, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x55, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x9b, 0x01, 0x0a,
	0x14, 0x52, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x79, 0x0a, 0x0a, 0x50, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0x82, 0x01, 0x0a, 0x0b, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x12, 0x37, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x7f, 0x0a, 0x09, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xbd, 0x02, 0x0a, 0x0f,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x26, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x52, 0x0a, 0x0f, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3f,
	0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x3b, 0x0a, 0x0d, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xc0, 0x01, 0x0a,
	0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x77, 0x6f,
	0x52, 0x73, 0x70, 0x12, 0x43, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x77,
	0x6f, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x65, 0x0a, 0x0d, 0x50, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x77, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x70, 0x77, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22,
	0xef, 0x01, 0x0a, 0x0c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x25, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x18, 0x14, 0x28, 0x01, 0x52, 0x08, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x07, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x03, 0x52, 0x06, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72,
	0x0b, 0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31, 0x30, 0x7d, 0x24, 0x52, 0x06, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x00, 0x30,
	0x01, 0x52, 0x07, 0x69, 0x73, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x2a,
	0x08, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x30, 0x04, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x22, 0xc0, 0x03, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e,
	0x0a, 0x06, 0x73, 0x74, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x73, 0x74, 0x75, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a,
	0x07, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x03, 0x52, 0x06, 0x72, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x28, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72, 0x0b, 0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31,
	0x30, 0x7d, 0x24, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x08, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x2a, 0x04, 0x18, 0x14, 0x28, 0x01, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x00, 0x30, 0x01, 0x52,
	0x07, 0x69, 0x73, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14,
	0x52, 0x02, 0x69, 0x70, 0x22, 0xa2, 0x02, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x61, 0x73, 0x69, 0x63, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x20, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xae, 0x02, 0x0a, 0x0f, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x73, 0x74, 0x75, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x05, 0x73, 0x74, 0x75, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06,
	0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12,
	0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x46,
	0x61, 0x6d, 0x69, 0x6c, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xed, 0x01, 0x0a, 0x16, 0x52,
	0x65, 0x73, 0x65, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20,
	0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x32, 0xdb, 0x08, 0x0a, 0x06, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x71, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x0f, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x77, 0x6f, 0x12, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x77, 0x6f, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x48, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x11,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x63, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x63, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x54, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x48,
	0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x50,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x12, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6b,
	0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x12, 0x5a, 0x10, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_parent_v1_parent_proto_rawDescOnce sync.Once
	file_parent_v1_parent_proto_rawDescData = file_parent_v1_parent_proto_rawDesc
)

func file_parent_v1_parent_proto_rawDescGZIP() []byte {
	file_parent_v1_parent_proto_rawDescOnce.Do(func() {
		file_parent_v1_parent_proto_rawDescData = protoimpl.X.CompressGZIP(file_parent_v1_parent_proto_rawDescData)
	})
	return file_parent_v1_parent_proto_rawDescData
}

var file_parent_v1_parent_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_parent_v1_parent_proto_goTypes = []interface{}{
	(*ListParentStudentInfoReq)(nil),         // 0: api.parent.v1.ListParentStudentInfoReq
	(*ListParentStudentInfoRsp)(nil),         // 1: api.parent.v1.ListParentStudentInfoRsp
	(*ListParentServiceInfoReq)(nil),         // 2: api.parent.v1.ListParentServiceInfoReq
	(*ListParentServiceInfoRsp)(nil),         // 3: api.parent.v1.ListParentServiceInfoRsp
	(*StudentActivationInfo)(nil),            // 4: api.parent.v1.StudentActivationInfo
	(*ParentActivationInfo)(nil),             // 5: api.parent.v1.ParentActivationInfo
	(*VerifyParentMobileReq)(nil),            // 6: api.parent.v1.VerifyParentMobileReq
	(*VerifyParentMobileRsp)(nil),            // 7: api.parent.v1.VerifyParentMobileRsp
	(*ChangeParentMobileReq)(nil),            // 8: api.parent.v1.ChangeParentMobileReq
	(*UpdateParentStudentRelationReq)(nil),   // 9: api.parent.v1.UpdateParentStudentRelationReq
	(*GetRelParentStudentInfoReq)(nil),       // 10: api.parent.v1.GetRelParentStudentInfoReq
	(*RelParentStudentId)(nil),               // 11: api.parent.v1.RelParentStudentId
	(*GetRelParentStudentInfoRsp)(nil),       // 12: api.parent.v1.GetRelParentStudentInfoRsp
	(*RelParentStudentInfo)(nil),             // 13: api.parent.v1.RelParentStudentInfo
	(*ParentInfo)(nil),                       // 14: api.parent.v1.ParentInfo
	(*StudentInfo)(nil),                      // 15: api.parent.v1.StudentInfo
	(*ClassInfo)(nil),                        // 16: api.parent.v1.ClassInfo
	(*CreateParentReq)(nil),                  // 17: api.parent.v1.CreateParentReq
	(*CreateParentRsp)(nil),                  // 18: api.parent.v1.CreateParentRsp
	(*ParentFailure)(nil),                    // 19: api.parent.v1.ParentFailure
	(*CreateParentTwoRsp)(nil),               // 20: api.parent.v1.CreateParentTwoRsp
	(*ParentDetail)(nil),                     // 21: api.parent.v1.ParentDetail
	(*UpdateParentReq)(nil),                  // 22: api.parent.v1.UpdateParentReq
	(*UpdateBasicParentReq)(nil),             // 23: api.parent.v1.UpdateBasicParentReq
	(*DeleteParentReq)(nil),                  // 24: api.parent.v1.DeleteParentReq
	(*ResetParentPasswordReq)(nil),           // 25: api.parent.v1.ResetParentPasswordReq
	(*CreateParentTwoRsp_ParentResInfo)(nil), // 26: api.parent.v1.CreateParentTwoRsp.ParentResInfo
	(*emptypb.Empty)(nil),                    // 27: google.protobuf.Empty
}
var file_parent_v1_parent_proto_depIdxs = []int32{
	11, // 0: api.parent.v1.ListParentStudentInfoReq.relations:type_name -> api.parent.v1.RelParentStudentId
	13, // 1: api.parent.v1.ListParentStudentInfoRsp.list:type_name -> api.parent.v1.RelParentStudentInfo
	4,  // 2: api.parent.v1.ListParentServiceInfoRsp.list:type_name -> api.parent.v1.StudentActivationInfo
	5,  // 3: api.parent.v1.StudentActivationInfo.parent_list:type_name -> api.parent.v1.ParentActivationInfo
	11, // 4: api.parent.v1.GetRelParentStudentInfoReq.parent_student_id:type_name -> api.parent.v1.RelParentStudentId
	13, // 5: api.parent.v1.GetRelParentStudentInfoRsp.data:type_name -> api.parent.v1.RelParentStudentInfo
	14, // 6: api.parent.v1.RelParentStudentInfo.parent:type_name -> api.parent.v1.ParentInfo
	15, // 7: api.parent.v1.RelParentStudentInfo.student:type_name -> api.parent.v1.StudentInfo
	16, // 8: api.parent.v1.StudentInfo.class_info:type_name -> api.parent.v1.ClassInfo
	21, // 9: api.parent.v1.CreateParentReq.parent_list:type_name -> api.parent.v1.ParentDetail
	19, // 10: api.parent.v1.CreateParentRsp.failure_list:type_name -> api.parent.v1.ParentFailure
	26, // 11: api.parent.v1.CreateParentTwoRsp.list:type_name -> api.parent.v1.CreateParentTwoRsp.ParentResInfo
	10, // 12: api.parent.v1.Parent.GetRelParentStudentInfo:input_type -> api.parent.v1.GetRelParentStudentInfoReq
	17, // 13: api.parent.v1.Parent.CreateParent:input_type -> api.parent.v1.CreateParentReq
	17, // 14: api.parent.v1.Parent.CreateParentTwo:input_type -> api.parent.v1.CreateParentReq
	22, // 15: api.parent.v1.Parent.UpdateParent:input_type -> api.parent.v1.UpdateParentReq
	23, // 16: api.parent.v1.Parent.UpdateBasicParent:input_type -> api.parent.v1.UpdateBasicParentReq
	8,  // 17: api.parent.v1.Parent.ChangeParentMobile:input_type -> api.parent.v1.ChangeParentMobileReq
	9,  // 18: api.parent.v1.Parent.UpdateParentStudentRelation:input_type -> api.parent.v1.UpdateParentStudentRelationReq
	24, // 19: api.parent.v1.Parent.DeleteParent:input_type -> api.parent.v1.DeleteParentReq
	25, // 20: api.parent.v1.Parent.ResetPassword:input_type -> api.parent.v1.ResetParentPasswordReq
	6,  // 21: api.parent.v1.Parent.VerifyParentMobile:input_type -> api.parent.v1.VerifyParentMobileReq
	2,  // 22: api.parent.v1.Parent.ListParentServiceInfo:input_type -> api.parent.v1.ListParentServiceInfoReq
	0,  // 23: api.parent.v1.Parent.ListParentStudentInfo:input_type -> api.parent.v1.ListParentStudentInfoReq
	12, // 24: api.parent.v1.Parent.GetRelParentStudentInfo:output_type -> api.parent.v1.GetRelParentStudentInfoRsp
	18, // 25: api.parent.v1.Parent.CreateParent:output_type -> api.parent.v1.CreateParentRsp
	20, // 26: api.parent.v1.Parent.CreateParentTwo:output_type -> api.parent.v1.CreateParentTwoRsp
	27, // 27: api.parent.v1.Parent.UpdateParent:output_type -> google.protobuf.Empty
	27, // 28: api.parent.v1.Parent.UpdateBasicParent:output_type -> google.protobuf.Empty
	27, // 29: api.parent.v1.Parent.ChangeParentMobile:output_type -> google.protobuf.Empty
	27, // 30: api.parent.v1.Parent.UpdateParentStudentRelation:output_type -> google.protobuf.Empty
	27, // 31: api.parent.v1.Parent.DeleteParent:output_type -> google.protobuf.Empty
	27, // 32: api.parent.v1.Parent.ResetPassword:output_type -> google.protobuf.Empty
	7,  // 33: api.parent.v1.Parent.VerifyParentMobile:output_type -> api.parent.v1.VerifyParentMobileRsp
	3,  // 34: api.parent.v1.Parent.ListParentServiceInfo:output_type -> api.parent.v1.ListParentServiceInfoRsp
	1,  // 35: api.parent.v1.Parent.ListParentStudentInfo:output_type -> api.parent.v1.ListParentStudentInfoRsp
	24, // [24:36] is the sub-list for method output_type
	12, // [12:24] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_parent_v1_parent_proto_init() }
func file_parent_v1_parent_proto_init() {
	if File_parent_v1_parent_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_parent_v1_parent_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListParentStudentInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListParentStudentInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListParentServiceInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListParentServiceInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StudentActivationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParentActivationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyParentMobileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyParentMobileRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeParentMobileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateParentStudentRelationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelParentStudentInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelParentStudentId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelParentStudentInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelParentStudentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StudentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateParentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateParentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParentFailure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateParentTwoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParentDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateParentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBasicParentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteParentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetParentPasswordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_parent_v1_parent_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateParentTwoRsp_ParentResInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_parent_v1_parent_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_parent_v1_parent_proto_goTypes,
		DependencyIndexes: file_parent_v1_parent_proto_depIdxs,
		MessageInfos:      file_parent_v1_parent_proto_msgTypes,
	}.Build()
	File_parent_v1_parent_proto = out.File
	file_parent_v1_parent_proto_rawDesc = nil
	file_parent_v1_parent_proto_goTypes = nil
	file_parent_v1_parent_proto_depIdxs = nil
}
