syntax = "proto3";

package api.parent.v1;

import "google/protobuf/empty.proto";
import "validate/validate.proto";

option go_package = "api/parent/v1;v1";

//生成proto代码
//kratos proto client api/parent/v1/parent.proto

//生成service代码
//kratos proto server api/parent/v1/parent.proto

// 家长信息
service Parent {
  // 批量获取家长及关联学生信息（此接口不返回班级信息），后续请使用ListParentStudentInfo接口
  rpc GetRelParentStudentInfo(GetRelParentStudentInfoReq) returns (GetRelParentStudentInfoRsp) {}

  // 添加家长信息
  rpc CreateParent (CreateParentReq) returns (CreateParentRsp) {}

  // 批量创建家长并返回密码
  rpc CreateParentTwo (CreateParentReq) returns (CreateParentTwoRsp) {}

  // 更新家长信息
  rpc UpdateParent(UpdateParentReq) returns (google.protobuf.Empty) {}

  // 更新家长基础信息
  rpc UpdateBasicParent(UpdateBasicParentReq) returns (google.protobuf.Empty) {}

  // 更换家长手机号
  rpc ChangeParentMobile(ChangeParentMobileReq) returns (google.protobuf.Empty) {}

  // 更新家长与学生的关系
  rpc UpdateParentStudentRelation(UpdateParentStudentRelationReq) returns (google.protobuf.Empty) {}

  // 删除家长
  rpc DeleteParent(DeleteParentReq) returns (google.protobuf.Empty) {}

  // 重置家长密码
  rpc ResetPassword(ResetParentPasswordReq) returns (google.protobuf.Empty) {}

  // 校验家长手机号是否在系统中(同一类用户下)
  rpc VerifyParentMobile(VerifyParentMobileReq) returns (VerifyParentMobileRsp) {}

  // 获取家长服务开通信息列表
  rpc ListParentServiceInfo(ListParentServiceInfoReq) returns (ListParentServiceInfoRsp) {
  }

  // 根据家长和学生id组合列表获取在职家长学生明细信息（删除的家长关联关系也会查询）
  rpc ListParentStudentInfo(ListParentStudentInfoReq) returns (ListParentStudentInfoRsp) {}
}

// 根据家长和学生id组合列表获取家长学生明细信息请求参数
message ListParentStudentInfoReq {
  // 是否返回班级年级信息
  bool return_sch_dept = 1;
  // 关联信息
  repeated RelParentStudentId relations = 2 [(validate.rules).repeated.min_items = 1];
}

// 根据家长和学生id组合列表获取家长学生明细信息响应参数
message ListParentStudentInfoRsp {
  // 信息列表
  repeated RelParentStudentInfo list = 1;
}

// 家长服务开通记录请求结构
message ListParentServiceInfoReq {
  // 服务类型
  uint32 service_type = 1 [(validate.rules).uint32.gt = 0];
  // 学生姓名
  string student_name = 2;
  // 班级id
  repeated int64 sch_dept_ids= 3;
  // 开通状态（1：未开通，2：已开通）
  int32  activation_status = 4 [(validate.rules).int32 = {in: [0, 1, 2]}];
  // 有效期（1：7天以内，2：30天以内，3：90天以内，4：超过90天，5：已过期）
  int32 valid_type = 5 [(validate.rules).int32 = {in: [0, 1, 2, 3, 4, 5]}];
  // 页数，不填默认不分页
  int32 page = 6;
  // 每页条数，不填默认不分页
  int32 per_page = 7;
  // 学校id
  int64 inst_id = 8 [(validate.rules).int64.gt = 0];
}


// 家长开通状态返回结构
message ListParentServiceInfoRsp {
  // 总条数
  int64 total = 1;
  // 学生开通记录列表
  repeated StudentActivationInfo list = 2;
}

// 学生开通记录信息
message StudentActivationInfo {
  // 学生id
  int64 student_id = 1;
  // 学生姓名
  string student_name = 2;
  // 性别
  int32 gender = 3;
  // 学生头像
  string avatar = 4;
  // 班级id
  int64 class_id = 5;
  // 所在班级
  string class_name = 6;
  // 年级id
  int64 grade_id = 7;
  // 所在年级
  string grade_name = 8;
  // 学生家长列表
  repeated ParentActivationInfo parent_list = 9;
}

// 家长开通信息
message ParentActivationInfo {
  // 家长id
  int64 parent_id = 1;
  // 家长姓名
  string parent_name = 2;
  // 家长手机号
  string mobile = 3;
  // 家长与学生关系
  string relation = 4;
  // 家长开通状态（1：未开通，2：已开通）
  uint32 activation_status = 5;
  // 家长开通截止时间
  int64 expiration_time = 6;
}

message VerifyParentMobileReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 手机区号
  string re_code = 2 [(validate.rules).string = {min_len: 1, max_len: 4}];
  // 手机号
  string mobile = 3 [(validate.rules).string.pattern = "^1\\d{10}$"];
}
message VerifyParentMobileRsp {
  // 是否在系统中
  bool exist = 1;
}

message ChangeParentMobileReq {
  // 家长id
  int64 id = 1 [(validate.rules).int64.gte = 0];
  // 原手机号
  string old_mobile = 2 [(validate.rules).string.pattern = "^1\\d{10}$"];
  // 新手机区号
  string re_code = 3 [(validate.rules).string = {min_len: 1, max_len: 4}];
  // 新手机号
  string mobile = 4 [(validate.rules).string.pattern = "^1\\d{10}$"];
  // 学校id
  int64 inst_id = 5 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 6 [(validate.rules).uint32 = {in: [1, 2]}];
  // 创建人id
  int64 operator_id = 7 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 8 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 9 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message UpdateParentStudentRelationReq {
  // 家长id
  int64 parent_id = 1 [(validate.rules).int64.gt = 0];
  // 学生id
  int64 student_id = 2 [(validate.rules).int64.gt = 0];
  // 关系(1：爸爸，2：妈妈，3：爷爷，4：奶奶，5：外公，6：外婆，7：伯父8：伯母，9：叔叔，10：婶婶，11：姑父，12：姑母，13：舅舅 14：舅妈，15：姐姐，16：哥哥，17：嫂嫂，18：姨妈，19：姑丈，20：其他)
  uint32 relation = 3 [(validate.rules).uint32 = {gte:1, lte: 20}];
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 5 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 6 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 7 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 批量获取家长信息
message GetRelParentStudentInfoReq{
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gte = 0];
  // 家长和学生
  repeated RelParentStudentId parent_student_id = 2 [(validate.rules).repeated.min_items = 1];
}

message RelParentStudentId{
  //家长id
  int64 parent_id = 1;
  //关联的学生id
  int64 student_id = 2;
}

message GetRelParentStudentInfoRsp{
  // 信息列表
  repeated RelParentStudentInfo data = 1;
}

//家长及关联学生的基本信息
message RelParentStudentInfo {
  //家长信息
  ParentInfo parent = 1;
  //学生信息
  StudentInfo student = 2;
  //家长与学生的关系
  string relation = 3;
}

message ParentInfo{
  // 家长id
  int64 id = 1;
  // 家长姓名
  string name = 2;
  // 手机区号
  string re_code = 3;
  // 手机号
  string mobile = 4;
  // 头像
  string avatar = 5;
}

message StudentInfo{
  // 学生id
  int64 id = 1;
  // 学生姓名
  string name = 2;
  // 性别
  int32 gender = 3;
  // 班级信息
  ClassInfo class_info = 4;
}

message ClassInfo {
  // 班级id
  int64 class_id = 1;
  // 班级名称
  string class_name = 2;
  // 年级id
  int64 grade_id = 3;
  // 年级名称
  string grade_name = 4;
}

// 创建家长请求参数
message CreateParentReq {
  // 学生id
  int64 student_id = 1 [(validate.rules).int64.gt = 0];
  // 新增家长列表
  repeated ParentDetail parent_list = 2 [(validate.rules).repeated.min_items = 1];
  // 学校id
  int64 inst_id = 3 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 4 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 5 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 6 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 7 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 创建家长返回参数
message CreateParentRsp {
  // 失败列表，若为空则表示全部成功
  repeated ParentFailure failure_list = 1;
}

// 失败信息
message ParentFailure {
  // 家长姓名
  string name = 1;
  // 失败原因
  string reason = 2;
}

message CreateParentTwoRsp {
  message ParentResInfo {
    // 家长姓名
    string name = 1;
    // 家长手机号
    string mobile = 2;
    // 家长账号的密码
    string pwd = 3;
    // 失败原因
    string reason = 4;
  }
  // 密码信息或失败信息列表
  repeated ParentResInfo list = 1;
}

message ParentDetail {
  // 关系(1：爸爸，2：妈妈，3：爷爷，4：奶奶，5：外公，6：外婆，7：伯父8：伯母，9：叔叔，10：婶婶，11：姑父，12：姑母，13：舅舅 14：舅妈，15：姐姐，16：哥哥，17：嫂嫂，18：姨妈，19：姑丈，20：其他)
  uint32 relation = 1 [(validate.rules).uint32 = {gte:1, lte: 20}];
  // 家长姓名
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 手机区号，默认需填86
  string re_code = 3 [(validate.rules).string = {min_len: 1, max_len: 3}];
  // 手机号
  string mobile = 4 [(validate.rules).string.pattern = "^1\\d{10}$"];
  // 是否是第一联系人
  uint32 is_first = 5 [(validate.rules).uint32 = {in: [0,1]}];
  // 来源
  uint32 source = 6 [(validate.rules).uint32 = {in: [1,2,3,4]}];
}

// 更新家长请求参数
message UpdateParentReq {
  // 家长id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学生id
  int64 stu_id = 2 [(validate.rules).int64.gt = 0];
  // 家长姓名，限30字符
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 手机区号
  string re_code = 4 [(validate.rules).string = {min_len: 1, max_len: 3}];
  // 手机号
  string mobile = 5 [(validate.rules).string.pattern = "^1\\d{10}$"];
  // 关系(1：爸爸，2：妈妈，3：爷爷，4：奶奶，5：外公，6：外婆，7：伯父8：伯母，9：叔叔，10：婶婶，11：姑父，12：姑母，13：舅舅 14：舅妈，15：姐姐，16：哥哥，17：嫂嫂，18：姨妈，19：姑丈，20：其他)
  uint32 relation = 6 [(validate.rules).uint32 = {gte:1, lte: 20}];
  // 是否是第一联系人
  uint32 is_first = 7 [(validate.rules).uint32 = {in: [0,1]}];
  // 学校id
  int64 inst_id = 8 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 9 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 10 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 11 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 12 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message UpdateBasicParentReq {
  // 家长id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 家长姓名，限30字符
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 头像
  string avatar = 3;
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 5 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 6 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 7 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 删除家长请求参数
message DeleteParentReq {
  // 家长id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学生ID
  int64 stu_id = 2 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 3 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 4 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 5 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 6 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 7 [(validate.rules).string = {min_len: 1, max_len: 20}];
  // 是否是亲友团场景删除家长
  bool is_family_group = 8;
}

// 重置家长密码请求参数
message ResetParentPasswordReq {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 3 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 5 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
}
