syntax = "proto3";

package api.sch_dept.v1;

option go_package = "api/sch_dept/v1;v1";

import "google/protobuf/empty.proto";
import "validate/validate.proto";

// 家校部门服务
service SchDept {
  // 创建年级
  rpc CreateGrade (CreateGradeReq) returns (CreateGradeRsp) {}

  // 更新年级
  rpc UpdateGrade(UpdateGradeReq) returns (google.protobuf.Empty) {}

  // 删除年级
  rpc DeleteGrade(DeleteGradeReq) returns (google.protobuf.Empty) {}

  // 获取年级班级树
  rpc ListSchDept(ListSchDeptReq) returns (SchDeptTree) {}

  // 获取指定用户下的班级列表
  rpc ListClassOnUser(ListClassOnUserReq) returns (ListClassOnUserRsp) {}

  // 创建班级
  rpc CreateClass (CreateClassReq) returns (google.protobuf.Empty) {}

  // 更新班级
  rpc UpdateClass (UpdateClassReq) returns (google.protobuf.Empty) {}

  // 升班
  rpc PromoteClass (UpgradeClassReq) returns (google.protobuf.Empty) {}

  // 毕业班级
  rpc GraduateClass (GraduateClassReq) returns (google.protobuf.Empty) {}

  // 删除班级
  rpc DeleteClass(DeleteClassReq) returns (google.protobuf.Empty) {}

  // 批量更新班级年级顺序
  rpc BatchUpdateSchDeptSeqOrder(BatchUpdateSchDeptSeqOrderReq) returns (google.protobuf.Empty) {}

  // 判断班级列表是否都在指定的学校中
  rpc VerifyClassListIsExist(VerifyClassListIsExistReq) returns (VerifyClassListIsExistRsp) {}

  // 通过班级ID列表获取班级基础信息列表
  rpc ListClassOfIds(ListClassOfIdsReq) returns (ListClassOfIdsRsp) {}

  // 分页获取班级列表
  rpc ListClass(ListClassReq) returns (ListClassRsp) {}

  // 获取班级信息
  rpc GetClass(GetClassReq) returns (GetClassRsp) {}
}

message GetClassReq {
  // 班级id
  int64 class_id = 1 [(validate.rules).int64.gt = 0];
}

message GetClassRsp {
  // 学校id
  int64 inst_id = 1;
  // 班级id
  int64 class_id = 2;
  // 班级名称
  string class_name = 3;
  // 年级id
  int64 grade_id = 4;
  // 年级名称
  string grade_name = 5;
}

message ListClassReq {
  // 当前页
  uint32 page = 1;
  // 每页条数, 为0时不分页
  uint32 per_page = 2;
  // 学校id
  int64 inst_id = 3 [(validate.rules).int64.gt = 0];
  // 班级ID列表
  repeated int64 class_list = 4;
}

message ListClassRsp {
  message ClassInfo {
    // 班级id
    int64 class_id = 1;
    // 班级名称
    string class_name = 2;
    // 年级id
    int64 grade_id = 3;
    // 年级名称
    string grade_name = 4;
  }
  // 总条数
  int64 total = 1;
  // 班级列表信息
  repeated ClassInfo list = 2;
}

message ListClassOfIdsReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 班级ID列表
  repeated int64 class_list = 2 [(validate.rules).repeated.min_items = 1];
}
message ListClassOfIdsRsp {
  message ClassInfo {
    int64 id = 1;
    string name = 2;
  }
  repeated ClassInfo list = 1;
}

message VerifyClassListIsExistReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 班级ID列表
  repeated int64 class_list = 2 [(validate.rules).repeated.min_items = 1];
}
message VerifyClassListIsExistRsp {
  bool is_exist = 1;
}

message ListClassOnUserReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 用户ID
  int64 user_id = 2 [(validate.rules).int64.gt = 0];
  // 用户角色 (1-老师，2-学生)
  uint32 user_role = 3 [(validate.rules).uint32 = {in: [1,2]}];
}

message ListClassOnUserRsp {
  message ClassInfo {
    int64 id = 1;
    // 班级名
    string name = 2;
    // 班级别名
    string nick_name = 3;
    // 排序
    int32 sort = 4;
  }
  // 班级列表
  repeated ClassInfo list = 1;
}

// 创建年级请求参数
message CreateGradeReq {
  // 父级学段id，需传字符串类型。目前可不填
  string parent_id = 1;
  // 年级名称，最多20个字符
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 20}];
  // 年级类型（1：小小班，2：小班，3：中班，4：大班，5：学前班）
  uint32 standard_grade = 3 [(validate.rules).uint32 = {in: [1,2,3,4,5]}];
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 5 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 6 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 7 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message CreateGradeRsp {
  // 年级id
  int64 id = 1;
}

// 更新年级请求参数
message UpdateGradeReq {
  // 年级id，需传字符串类型。
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 年级名称，限20个字符
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 20}];
  // 年级类型（1：小小班，2：小班，3：中班，4：大班，5：学前班）
  uint32 standard_grade = 3 [(validate.rules).uint32 = {in: [1,2,3,4,5]}];
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 5 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 6 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 7 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 删除年级请求参数
message DeleteGradeReq {
  // 年级id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 3 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 5 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 获取家校部门树请求参数
message ListSchDeptReq {
  // 父级年级id，不填默认获取所有年级
  int64 grade_id = 1 [(validate.rules).int64.gte = 0];
  // 是否递归获取子部门（0：否，1：是）
  uint32 fetch_child = 2 [(validate.rules).uint32 = {in: [0,1]}];
  // 是否展示任教人员（0：否，1：是）
  uint32 show_teacher = 3 [(validate.rules).uint32 = {in: [0,1]}];
  // 是否获取各班级下的学生激活数和未激活数（0：否，1：是）
  uint32 fetch_stu_activate = 4 [(validate.rules).uint32 = {in: [0,1]}];
  // 学校id
  int64 inst_id = 5 [(validate.rules).int64.gt = 0];
  // 当前登录用户账号ID
  int64 acc_id = 6 [(validate.rules).int64.gt = 0];
}

// 家校部门树形结构
message SchDeptTree {
  message StuActivateInfo {
    // 激活数
    uint32 activated_count = 1;
    // 未激活数
    uint32 unactivate_count = 2;
  }
  // id
  int64 id = 1;
  // 学校id
  int64 inst_id = 2;
  // 父级id
  int64 parent_id = 3;
  // 部门类型（1：班级，2：年级）
  int32 type = 4;
  // 部门名称
  string name = 5;
  // 年级类型（1：小小班，2：小班，3：中班，4：大班，5：学前班）
  uint32 standard_grade = 6;
  // 入学年份
  uint32 register_year = 7;
  // 排序
  int32 sort = 8;
  // 班级任教信息，仅班级类型该字段有数据，若设置不展示任教信息，则该字段为空
  repeated TeachInfo teach_info = 9;
  // 班级下的男学生数
  int32 boy_count = 10;
  // 班级下的女学生数
  int32 girl_count = 11;
  // 创建时间
  int64 create_time = 12;
  // 当前班级下的学生激活数信息
  StuActivateInfo student_activate_info = 13;
  // 子部门
  repeated SchDeptTree children = 14;
}

// 创建班级请求参数
message CreateClassReq {
  // 所属年级id
  int64 grade_id = 1 [(validate.rules).int64.gt = 0];
  // 班级名称，限20个字符
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 20}];
  // 任教老师信息
  repeated TeachInfo teachers = 3;
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 5 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 6 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 7 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 更新班级请求参数
message UpdateClassReq {
  // 班级id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 班级名称，限20个字符
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 20}];
  // 任教信息
  repeated TeachInfo teach_info = 3;
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 5 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 6 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 7 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 升班请求参数
message UpgradeClassReq {
  // 班级id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 年级id
  int64 grade_id = 2 [(validate.rules).int64.gt = 0];
  // 新的班级名称
  string class_name = 3 [(validate.rules).string = {min_len: 1, max_len: 20}];
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 5 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 6 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 7 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 毕业班级请求参数
message GraduateClassReq {
  // 班级id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 3 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 5 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 删除班级请求参数
message DeleteClassReq {
  // 班级id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 3 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 5 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message TeachInfo {
  message Teacher {
    // 老师id
    int64 id = 1 [(validate.rules).int64.gt = 0];
    // 老师姓名
    string name = 2;
  }
  // 任教类型（1表示校区负责人，2表示年级负责人，3表示班主任，4表示任课（普通）老师，5表示学段负责人，6：配班老师，7：保育员）
  uint32 type = 1 [(validate.rules).uint32 = {in: [3,4,6,7]}];
  // 主班老师
  repeated Teacher teachers = 2;
}

// 批量更新年级或班级顺序请求参数
message BatchUpdateSchDeptSeqOrderReq {
  message SeqInfo {
    // 年级或班级id
    int64 id = 1 [(validate.rules).int64.gt = 0];
    // 顺序
    int32 sort = 2 [(validate.rules).int32.gt = 0];
  }
  repeated SeqInfo list = 1 [(validate.rules).repeated.min_items = 1];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 3 [(validate.rules).uint32 = {in: [1,2,3]}];
  // 创建人id
  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 5 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
}
