// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.17.3
// source: sch_dept/v1/sch_dept.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetClassReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
}

func (x *GetClassReq) Reset() {
	*x = GetClassReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClassReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClassReq) ProtoMessage() {}

func (x *GetClassReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClassReq.ProtoReflect.Descriptor instead.
func (*GetClassReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{0}
}

func (x *GetClassReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

type GetClassRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 班级id
	ClassId int64 `protobuf:"varint,2,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,3,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级id
	GradeId int64 `protobuf:"varint,4,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,5,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
}

func (x *GetClassRsp) Reset() {
	*x = GetClassRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClassRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClassRsp) ProtoMessage() {}

func (x *GetClassRsp) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClassRsp.ProtoReflect.Descriptor instead.
func (*GetClassRsp) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{1}
}

func (x *GetClassRsp) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetClassRsp) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *GetClassRsp) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *GetClassRsp) GetGradeId() int64 {
	if x != nil {
		return x.GradeId
	}
	return 0
}

func (x *GetClassRsp) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

type ListClassReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页
	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数, 为0时不分页
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 班级ID列表
	ClassList []int64 `protobuf:"varint,4,rep,packed,name=class_list,json=classList,proto3" json:"class_list,omitempty"`
}

func (x *ListClassReq) Reset() {
	*x = ListClassReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassReq) ProtoMessage() {}

func (x *ListClassReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassReq.ProtoReflect.Descriptor instead.
func (*ListClassReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{2}
}

func (x *ListClassReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListClassReq) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListClassReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListClassReq) GetClassList() []int64 {
	if x != nil {
		return x.ClassList
	}
	return nil
}

type ListClassRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 班级列表信息
	List []*ListClassRsp_ClassInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListClassRsp) Reset() {
	*x = ListClassRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassRsp) ProtoMessage() {}

func (x *ListClassRsp) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassRsp.ProtoReflect.Descriptor instead.
func (*ListClassRsp) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{3}
}

func (x *ListClassRsp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListClassRsp) GetList() []*ListClassRsp_ClassInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type ListClassOfIdsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 班级ID列表
	ClassList []int64 `protobuf:"varint,2,rep,packed,name=class_list,json=classList,proto3" json:"class_list,omitempty"`
}

func (x *ListClassOfIdsReq) Reset() {
	*x = ListClassOfIdsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassOfIdsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassOfIdsReq) ProtoMessage() {}

func (x *ListClassOfIdsReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassOfIdsReq.ProtoReflect.Descriptor instead.
func (*ListClassOfIdsReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{4}
}

func (x *ListClassOfIdsReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListClassOfIdsReq) GetClassList() []int64 {
	if x != nil {
		return x.ClassList
	}
	return nil
}

type ListClassOfIdsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ListClassOfIdsRsp_ClassInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListClassOfIdsRsp) Reset() {
	*x = ListClassOfIdsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassOfIdsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassOfIdsRsp) ProtoMessage() {}

func (x *ListClassOfIdsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassOfIdsRsp.ProtoReflect.Descriptor instead.
func (*ListClassOfIdsRsp) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{5}
}

func (x *ListClassOfIdsRsp) GetList() []*ListClassOfIdsRsp_ClassInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type VerifyClassListIsExistReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 班级ID列表
	ClassList []int64 `protobuf:"varint,2,rep,packed,name=class_list,json=classList,proto3" json:"class_list,omitempty"`
}

func (x *VerifyClassListIsExistReq) Reset() {
	*x = VerifyClassListIsExistReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyClassListIsExistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyClassListIsExistReq) ProtoMessage() {}

func (x *VerifyClassListIsExistReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyClassListIsExistReq.ProtoReflect.Descriptor instead.
func (*VerifyClassListIsExistReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{6}
}

func (x *VerifyClassListIsExistReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *VerifyClassListIsExistReq) GetClassList() []int64 {
	if x != nil {
		return x.ClassList
	}
	return nil
}

type VerifyClassListIsExistRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsExist bool `protobuf:"varint,1,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`
}

func (x *VerifyClassListIsExistRsp) Reset() {
	*x = VerifyClassListIsExistRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyClassListIsExistRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyClassListIsExistRsp) ProtoMessage() {}

func (x *VerifyClassListIsExistRsp) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyClassListIsExistRsp.ProtoReflect.Descriptor instead.
func (*VerifyClassListIsExistRsp) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{7}
}

func (x *VerifyClassListIsExistRsp) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type ListClassOnUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 用户ID
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户角色 (1-老师，2-学生)
	UserRole uint32 `protobuf:"varint,3,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
}

func (x *ListClassOnUserReq) Reset() {
	*x = ListClassOnUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassOnUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassOnUserReq) ProtoMessage() {}

func (x *ListClassOnUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassOnUserReq.ProtoReflect.Descriptor instead.
func (*ListClassOnUserReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{8}
}

func (x *ListClassOnUserReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListClassOnUserReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ListClassOnUserReq) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

type ListClassOnUserRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级列表
	List []*ListClassOnUserRsp_ClassInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListClassOnUserRsp) Reset() {
	*x = ListClassOnUserRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassOnUserRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassOnUserRsp) ProtoMessage() {}

func (x *ListClassOnUserRsp) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassOnUserRsp.ProtoReflect.Descriptor instead.
func (*ListClassOnUserRsp) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{9}
}

func (x *ListClassOnUserRsp) GetList() []*ListClassOnUserRsp_ClassInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 创建年级请求参数
type CreateGradeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 父级学段id，需传字符串类型。目前可不填
	ParentId string `protobuf:"bytes,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 年级名称，最多20个字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 年级类型（1：小小班，2：小班，3：中班，4：大班，5：学前班）
	StandardGrade uint32 `protobuf:"varint,3,opt,name=standard_grade,json=standardGrade,proto3" json:"standard_grade,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *CreateGradeReq) Reset() {
	*x = CreateGradeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGradeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGradeReq) ProtoMessage() {}

func (x *CreateGradeReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGradeReq.ProtoReflect.Descriptor instead.
func (*CreateGradeReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{10}
}

func (x *CreateGradeReq) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *CreateGradeReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateGradeReq) GetStandardGrade() uint32 {
	if x != nil {
		return x.StandardGrade
	}
	return 0
}

func (x *CreateGradeReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *CreateGradeReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *CreateGradeReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateGradeReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *CreateGradeReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type CreateGradeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 年级id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateGradeRsp) Reset() {
	*x = CreateGradeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGradeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGradeRsp) ProtoMessage() {}

func (x *CreateGradeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGradeRsp.ProtoReflect.Descriptor instead.
func (*CreateGradeRsp) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{11}
}

func (x *CreateGradeRsp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 更新年级请求参数
type UpdateGradeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 年级id，需传字符串类型。
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 年级名称，限20个字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 年级类型（1：小小班，2：小班，3：中班，4：大班，5：学前班）
	StandardGrade uint32 `protobuf:"varint,3,opt,name=standard_grade,json=standardGrade,proto3" json:"standard_grade,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateGradeReq) Reset() {
	*x = UpdateGradeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGradeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGradeReq) ProtoMessage() {}

func (x *UpdateGradeReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGradeReq.ProtoReflect.Descriptor instead.
func (*UpdateGradeReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateGradeReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateGradeReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateGradeReq) GetStandardGrade() uint32 {
	if x != nil {
		return x.StandardGrade
	}
	return 0
}

func (x *UpdateGradeReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateGradeReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateGradeReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateGradeReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateGradeReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 删除年级请求参数
type DeleteGradeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 年级id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *DeleteGradeReq) Reset() {
	*x = DeleteGradeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGradeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGradeReq) ProtoMessage() {}

func (x *DeleteGradeReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGradeReq.ProtoReflect.Descriptor instead.
func (*DeleteGradeReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteGradeReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteGradeReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *DeleteGradeReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *DeleteGradeReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *DeleteGradeReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *DeleteGradeReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 获取家校部门树请求参数
type ListSchDeptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 父级年级id，不填默认获取所有年级
	GradeId int64 `protobuf:"varint,1,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	// 是否递归获取子部门（0：否，1：是）
	FetchChild uint32 `protobuf:"varint,2,opt,name=fetch_child,json=fetchChild,proto3" json:"fetch_child,omitempty"`
	// 是否展示任教人员（0：否，1：是）
	ShowTeacher uint32 `protobuf:"varint,3,opt,name=show_teacher,json=showTeacher,proto3" json:"show_teacher,omitempty"`
	// 是否获取各班级下的学生激活数和未激活数（0：否，1：是）
	FetchStuActivate uint32 `protobuf:"varint,4,opt,name=fetch_stu_activate,json=fetchStuActivate,proto3" json:"fetch_stu_activate,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,5,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 当前登录用户账号ID
	AccId int64 `protobuf:"varint,6,opt,name=acc_id,json=accId,proto3" json:"acc_id,omitempty"`
}

func (x *ListSchDeptReq) Reset() {
	*x = ListSchDeptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSchDeptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSchDeptReq) ProtoMessage() {}

func (x *ListSchDeptReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSchDeptReq.ProtoReflect.Descriptor instead.
func (*ListSchDeptReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{14}
}

func (x *ListSchDeptReq) GetGradeId() int64 {
	if x != nil {
		return x.GradeId
	}
	return 0
}

func (x *ListSchDeptReq) GetFetchChild() uint32 {
	if x != nil {
		return x.FetchChild
	}
	return 0
}

func (x *ListSchDeptReq) GetShowTeacher() uint32 {
	if x != nil {
		return x.ShowTeacher
	}
	return 0
}

func (x *ListSchDeptReq) GetFetchStuActivate() uint32 {
	if x != nil {
		return x.FetchStuActivate
	}
	return 0
}

func (x *ListSchDeptReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListSchDeptReq) GetAccId() int64 {
	if x != nil {
		return x.AccId
	}
	return 0
}

// 家校部门树形结构
type SchDeptTree struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 父级id
	ParentId int64 `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 部门类型（1：班级，2：年级）
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	// 部门名称
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// 年级类型（1：小小班，2：小班，3：中班，4：大班，5：学前班）
	StandardGrade uint32 `protobuf:"varint,6,opt,name=standard_grade,json=standardGrade,proto3" json:"standard_grade,omitempty"`
	// 入学年份
	RegisterYear uint32 `protobuf:"varint,7,opt,name=register_year,json=registerYear,proto3" json:"register_year,omitempty"`
	// 排序
	Sort int32 `protobuf:"varint,8,opt,name=sort,proto3" json:"sort,omitempty"`
	// 班级任教信息，仅班级类型该字段有数据，若设置不展示任教信息，则该字段为空
	TeachInfo []*TeachInfo `protobuf:"bytes,9,rep,name=teach_info,json=teachInfo,proto3" json:"teach_info,omitempty"`
	// 班级下的男学生数
	BoyCount int32 `protobuf:"varint,10,opt,name=boy_count,json=boyCount,proto3" json:"boy_count,omitempty"`
	// 班级下的女学生数
	GirlCount int32 `protobuf:"varint,11,opt,name=girl_count,json=girlCount,proto3" json:"girl_count,omitempty"`
	// 创建时间
	CreateTime int64 `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 当前班级下的学生激活数信息
	StudentActivateInfo *SchDeptTree_StuActivateInfo `protobuf:"bytes,13,opt,name=student_activate_info,json=studentActivateInfo,proto3" json:"student_activate_info,omitempty"`
	// 子部门
	Children []*SchDeptTree `protobuf:"bytes,14,rep,name=children,proto3" json:"children,omitempty"`
}

func (x *SchDeptTree) Reset() {
	*x = SchDeptTree{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SchDeptTree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchDeptTree) ProtoMessage() {}

func (x *SchDeptTree) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchDeptTree.ProtoReflect.Descriptor instead.
func (*SchDeptTree) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{15}
}

func (x *SchDeptTree) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SchDeptTree) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *SchDeptTree) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *SchDeptTree) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SchDeptTree) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SchDeptTree) GetStandardGrade() uint32 {
	if x != nil {
		return x.StandardGrade
	}
	return 0
}

func (x *SchDeptTree) GetRegisterYear() uint32 {
	if x != nil {
		return x.RegisterYear
	}
	return 0
}

func (x *SchDeptTree) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *SchDeptTree) GetTeachInfo() []*TeachInfo {
	if x != nil {
		return x.TeachInfo
	}
	return nil
}

func (x *SchDeptTree) GetBoyCount() int32 {
	if x != nil {
		return x.BoyCount
	}
	return 0
}

func (x *SchDeptTree) GetGirlCount() int32 {
	if x != nil {
		return x.GirlCount
	}
	return 0
}

func (x *SchDeptTree) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SchDeptTree) GetStudentActivateInfo() *SchDeptTree_StuActivateInfo {
	if x != nil {
		return x.StudentActivateInfo
	}
	return nil
}

func (x *SchDeptTree) GetChildren() []*SchDeptTree {
	if x != nil {
		return x.Children
	}
	return nil
}

// 创建班级请求参数
type CreateClassReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 所属年级id
	GradeId int64 `protobuf:"varint,1,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	// 班级名称，限20个字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 任教老师信息
	Teachers []*TeachInfo `protobuf:"bytes,3,rep,name=teachers,proto3" json:"teachers,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *CreateClassReq) Reset() {
	*x = CreateClassReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateClassReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateClassReq) ProtoMessage() {}

func (x *CreateClassReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateClassReq.ProtoReflect.Descriptor instead.
func (*CreateClassReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{16}
}

func (x *CreateClassReq) GetGradeId() int64 {
	if x != nil {
		return x.GradeId
	}
	return 0
}

func (x *CreateClassReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateClassReq) GetTeachers() []*TeachInfo {
	if x != nil {
		return x.Teachers
	}
	return nil
}

func (x *CreateClassReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *CreateClassReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *CreateClassReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateClassReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *CreateClassReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 更新班级请求参数
type UpdateClassReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 班级名称，限20个字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 任教信息
	TeachInfo []*TeachInfo `protobuf:"bytes,3,rep,name=teach_info,json=teachInfo,proto3" json:"teach_info,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateClassReq) Reset() {
	*x = UpdateClassReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClassReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClassReq) ProtoMessage() {}

func (x *UpdateClassReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClassReq.ProtoReflect.Descriptor instead.
func (*UpdateClassReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateClassReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateClassReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateClassReq) GetTeachInfo() []*TeachInfo {
	if x != nil {
		return x.TeachInfo
	}
	return nil
}

func (x *UpdateClassReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateClassReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateClassReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateClassReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateClassReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 升班请求参数
type UpgradeClassReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 年级id
	GradeId int64 `protobuf:"varint,2,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	// 新的班级名称
	ClassName string `protobuf:"bytes,3,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpgradeClassReq) Reset() {
	*x = UpgradeClassReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeClassReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeClassReq) ProtoMessage() {}

func (x *UpgradeClassReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeClassReq.ProtoReflect.Descriptor instead.
func (*UpgradeClassReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{18}
}

func (x *UpgradeClassReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpgradeClassReq) GetGradeId() int64 {
	if x != nil {
		return x.GradeId
	}
	return 0
}

func (x *UpgradeClassReq) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *UpgradeClassReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpgradeClassReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpgradeClassReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpgradeClassReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpgradeClassReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 毕业班级请求参数
type GraduateClassReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *GraduateClassReq) Reset() {
	*x = GraduateClassReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GraduateClassReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GraduateClassReq) ProtoMessage() {}

func (x *GraduateClassReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GraduateClassReq.ProtoReflect.Descriptor instead.
func (*GraduateClassReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{19}
}

func (x *GraduateClassReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GraduateClassReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GraduateClassReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *GraduateClassReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GraduateClassReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *GraduateClassReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 删除班级请求参数
type DeleteClassReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *DeleteClassReq) Reset() {
	*x = DeleteClassReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteClassReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteClassReq) ProtoMessage() {}

func (x *DeleteClassReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteClassReq.ProtoReflect.Descriptor instead.
func (*DeleteClassReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{20}
}

func (x *DeleteClassReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteClassReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *DeleteClassReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *DeleteClassReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *DeleteClassReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *DeleteClassReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type TeachInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任教类型（1表示校区负责人，2表示年级负责人，3表示班主任，4表示任课（普通）老师，5表示学段负责人，6：配班老师，7：保育员）
	Type uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// 主班老师
	Teachers []*TeachInfo_Teacher `protobuf:"bytes,2,rep,name=teachers,proto3" json:"teachers,omitempty"`
}

func (x *TeachInfo) Reset() {
	*x = TeachInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeachInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeachInfo) ProtoMessage() {}

func (x *TeachInfo) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeachInfo.ProtoReflect.Descriptor instead.
func (*TeachInfo) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{21}
}

func (x *TeachInfo) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TeachInfo) GetTeachers() []*TeachInfo_Teacher {
	if x != nil {
		return x.Teachers
	}
	return nil
}

// 批量更新年级或班级顺序请求参数
type BatchUpdateSchDeptSeqOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*BatchUpdateSchDeptSeqOrderReq_SeqInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *BatchUpdateSchDeptSeqOrderReq) Reset() {
	*x = BatchUpdateSchDeptSeqOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateSchDeptSeqOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateSchDeptSeqOrderReq) ProtoMessage() {}

func (x *BatchUpdateSchDeptSeqOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateSchDeptSeqOrderReq.ProtoReflect.Descriptor instead.
func (*BatchUpdateSchDeptSeqOrderReq) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{22}
}

func (x *BatchUpdateSchDeptSeqOrderReq) GetList() []*BatchUpdateSchDeptSeqOrderReq_SeqInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *BatchUpdateSchDeptSeqOrderReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *BatchUpdateSchDeptSeqOrderReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *BatchUpdateSchDeptSeqOrderReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *BatchUpdateSchDeptSeqOrderReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *BatchUpdateSchDeptSeqOrderReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type ListClassRsp_ClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,2,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级id
	GradeId int64 `protobuf:"varint,3,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,4,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
}

func (x *ListClassRsp_ClassInfo) Reset() {
	*x = ListClassRsp_ClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassRsp_ClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassRsp_ClassInfo) ProtoMessage() {}

func (x *ListClassRsp_ClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassRsp_ClassInfo.ProtoReflect.Descriptor instead.
func (*ListClassRsp_ClassInfo) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ListClassRsp_ClassInfo) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *ListClassRsp_ClassInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *ListClassRsp_ClassInfo) GetGradeId() int64 {
	if x != nil {
		return x.GradeId
	}
	return 0
}

func (x *ListClassRsp_ClassInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

type ListClassOfIdsRsp_ClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ListClassOfIdsRsp_ClassInfo) Reset() {
	*x = ListClassOfIdsRsp_ClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassOfIdsRsp_ClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassOfIdsRsp_ClassInfo) ProtoMessage() {}

func (x *ListClassOfIdsRsp_ClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassOfIdsRsp_ClassInfo.ProtoReflect.Descriptor instead.
func (*ListClassOfIdsRsp_ClassInfo) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ListClassOfIdsRsp_ClassInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListClassOfIdsRsp_ClassInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListClassOnUserRsp_ClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 班级名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 班级别名
	NickName string `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	// 排序
	Sort int32 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *ListClassOnUserRsp_ClassInfo) Reset() {
	*x = ListClassOnUserRsp_ClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassOnUserRsp_ClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassOnUserRsp_ClassInfo) ProtoMessage() {}

func (x *ListClassOnUserRsp_ClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassOnUserRsp_ClassInfo.ProtoReflect.Descriptor instead.
func (*ListClassOnUserRsp_ClassInfo) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListClassOnUserRsp_ClassInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListClassOnUserRsp_ClassInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListClassOnUserRsp_ClassInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *ListClassOnUserRsp_ClassInfo) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

type SchDeptTree_StuActivateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 激活数
	ActivatedCount uint32 `protobuf:"varint,1,opt,name=activated_count,json=activatedCount,proto3" json:"activated_count,omitempty"`
	// 未激活数
	UnactivateCount uint32 `protobuf:"varint,2,opt,name=unactivate_count,json=unactivateCount,proto3" json:"unactivate_count,omitempty"`
}

func (x *SchDeptTree_StuActivateInfo) Reset() {
	*x = SchDeptTree_StuActivateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SchDeptTree_StuActivateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchDeptTree_StuActivateInfo) ProtoMessage() {}

func (x *SchDeptTree_StuActivateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchDeptTree_StuActivateInfo.ProtoReflect.Descriptor instead.
func (*SchDeptTree_StuActivateInfo) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{15, 0}
}

func (x *SchDeptTree_StuActivateInfo) GetActivatedCount() uint32 {
	if x != nil {
		return x.ActivatedCount
	}
	return 0
}

func (x *SchDeptTree_StuActivateInfo) GetUnactivateCount() uint32 {
	if x != nil {
		return x.UnactivateCount
	}
	return 0
}

type TeachInfo_Teacher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 老师id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 老师姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *TeachInfo_Teacher) Reset() {
	*x = TeachInfo_Teacher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeachInfo_Teacher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeachInfo_Teacher) ProtoMessage() {}

func (x *TeachInfo_Teacher) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeachInfo_Teacher.ProtoReflect.Descriptor instead.
func (*TeachInfo_Teacher) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{21, 0}
}

func (x *TeachInfo_Teacher) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TeachInfo_Teacher) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BatchUpdateSchDeptSeqOrderReq_SeqInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 年级或班级id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 顺序
	Sort int32 `protobuf:"varint,2,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *BatchUpdateSchDeptSeqOrderReq_SeqInfo) Reset() {
	*x = BatchUpdateSchDeptSeqOrderReq_SeqInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateSchDeptSeqOrderReq_SeqInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateSchDeptSeqOrderReq_SeqInfo) ProtoMessage() {}

func (x *BatchUpdateSchDeptSeqOrderReq_SeqInfo) ProtoReflect() protoreflect.Message {
	mi := &file_sch_dept_v1_sch_dept_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateSchDeptSeqOrderReq_SeqInfo.ProtoReflect.Descriptor instead.
func (*BatchUpdateSchDeptSeqOrderReq_SeqInfo) Descriptor() ([]byte, []int) {
	return file_sch_dept_v1_sch_dept_proto_rawDescGZIP(), []int{22, 0}
}

func (x *BatchUpdateSchDeptSeqOrderReq_SeqInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BatchUpdateSchDeptSeqOrderReq_SeqInfo) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_sch_dept_v1_sch_dept_proto protoreflect.FileDescriptor

var file_sch_dept_v1_sch_dept_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63,
	0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x31, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x7e, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61,
	0x67, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0xe2, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x7f, 0x0a, 0x09, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5e, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f, 0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x86, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f, 0x66, 0x49, 0x64, 0x73, 0x52, 0x73, 0x70, 0x12, 0x40,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f, 0x66, 0x49, 0x64, 0x73, 0x52, 0x73, 0x70,
	0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x1a, 0x2f, 0x0a, 0x09, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x66, 0x0a, 0x19, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x20,
	0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x27, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x36, 0x0a, 0x19, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x73, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73,
	0x74, 0x22, 0x80, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f,
	0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x52, 0x6f, 0x6c, 0x65, 0x22, 0xb9, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x4f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x73, 0x70, 0x2e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x60,
	0x0a, 0x09, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6e, 0x69, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x22, 0xc0, 0x02, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x36, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x5f, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x2a, 0x0a, 0x30, 0x01,
	0x30, 0x02, 0x30, 0x03, 0x30, 0x04, 0x30, 0x05, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x72, 0x64, 0x47, 0x72, 0x61, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a,
	0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52,
	0x02, 0x69, 0x70, 0x22, 0x20, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x61,
	0x64, 0x65, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xbc, 0x02, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x36, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x5f, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x2a, 0x0a, 0x30,
	0x01, 0x30, 0x02, 0x30, 0x03, 0x30, 0x04, 0x30, 0x05, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x6e, 0x64,
	0x61, 0x72, 0x64, 0x47, 0x72, 0x61, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14,
	0x52, 0x02, 0x69, 0x70, 0x22, 0xe5, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47,
	0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03,
	0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x89, 0x02, 0x0a,
	0x0e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68, 0x44, 0x65, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x22, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x68, 0x69,
	0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30,
	0x00, 0x30, 0x01, 0x52, 0x0a, 0x66, 0x65, 0x74, 0x63, 0x68, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x12,
	0x2c, 0x0a, 0x0c, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x74, 0x65, 0x61, 0x63, 0x68, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x00, 0x30, 0x01,
	0x52, 0x0b, 0x73, 0x68, 0x6f, 0x77, 0x54, 0x65, 0x61, 0x63, 0x68, 0x65, 0x72, 0x12, 0x37, 0x0a,
	0x12, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x75, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04,
	0x30, 0x00, 0x30, 0x01, 0x52, 0x10, 0x66, 0x65, 0x74, 0x63, 0x68, 0x53, 0x74, 0x75, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x61, 0x63, 0x63, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x05, 0x61, 0x63, 0x63, 0x49, 0x64, 0x22, 0xf6, 0x04, 0x0a, 0x0b, 0x53, 0x63, 0x68,
	0x44, 0x65, 0x70, 0x74, 0x54, 0x72, 0x65, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x72, 0x64, 0x5f, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x47, 0x72, 0x61, 0x64, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x59, 0x65,
	0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x74, 0x65, 0x61, 0x63, 0x68, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61,
	0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x6f, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x69, 0x72, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x67, 0x69, 0x72, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x60,
	0x0a, 0x15, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x63, 0x68, 0x44, 0x65, 0x70, 0x74, 0x54, 0x72, 0x65, 0x65, 0x2e, 0x53, 0x74, 0x75, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x13, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x38, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x0e, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x44, 0x65, 0x70, 0x74, 0x54, 0x72, 0x65, 0x65,
	0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x1a, 0x65, 0x0a, 0x0f, 0x53, 0x74,
	0x75, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a,
	0x0f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x75, 0x6e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0f, 0x75, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xc7, 0x02, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x14, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x74, 0x65, 0x61, 0x63, 0x68,
	0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x63,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x65, 0x61, 0x63, 0x68, 0x65, 0x72, 0x73, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xbf, 0x02, 0x0a, 0x0e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x74, 0x65, 0x61, 0x63, 0x68, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61,
	0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30,
	0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xb4, 0x02,
	0x0a, 0x0f, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x09, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14,
	0x52, 0x02, 0x69, 0x70, 0x22, 0xe7, 0x01, 0x0a, 0x10, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61, 0x74,
	0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02,
	0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xe5,
	0x01, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xa6, 0x01, 0x0a, 0x09, 0x54, 0x65, 0x61, 0x63, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x2a, 0x08, 0x30, 0x03, 0x30, 0x04, 0x30, 0x06, 0x30,
	0x07, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x74, 0x65, 0x61, 0x63, 0x68,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x61, 0x63,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x54, 0x65, 0x61, 0x63, 0x68, 0x65, 0x72, 0x52, 0x08, 0x74,
	0x65, 0x61, 0x63, 0x68, 0x65, 0x72, 0x73, 0x1a, 0x36, 0x0a, 0x07, 0x54, 0x65, 0x61, 0x63, 0x68,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0xf2, 0x02, 0x0a, 0x1d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x63, 0x68, 0x44, 0x65, 0x70, 0x74, 0x53, 0x65, 0x71, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x12, 0x54, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68,
	0x44, 0x65, 0x70, 0x74, 0x53, 0x65, 0x71, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e,
	0x53, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08,
	0x01, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a,
	0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52,
	0x02, 0x69, 0x70, 0x1a, 0x3f, 0x0a, 0x07, 0x53, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x32, 0xe6, 0x09, 0x0a, 0x07, 0x53, 0x63, 0x68, 0x44, 0x65, 0x70, 0x74,
	0x12, 0x51, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x12,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x61,
	0x64, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x48, 0x0a,
	0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x12, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x63, 0x68, 0x44, 0x65, 0x70, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68,
	0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x68,
	0x44, 0x65, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63,
	0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x44, 0x65, 0x70,
	0x74, 0x54, 0x72, 0x65, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x4f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a,
	0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f,
	0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x48, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0c, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x0d, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61,
	0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63,
	0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61,
	0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65,
	0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x66,
	0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68,
	0x44, 0x65, 0x70, 0x74, 0x53, 0x65, 0x71, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x44, 0x65, 0x70,
	0x74, 0x53, 0x65, 0x71, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x72, 0x0a, 0x16, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x73,
	0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0e, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f, 0x66, 0x49, 0x64, 0x73, 0x12, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f, 0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4f, 0x66, 0x49, 0x64,
	0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65,
	0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x14, 0x5a,
	0x12, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_sch_dept_v1_sch_dept_proto_rawDescOnce sync.Once
	file_sch_dept_v1_sch_dept_proto_rawDescData = file_sch_dept_v1_sch_dept_proto_rawDesc
)

func file_sch_dept_v1_sch_dept_proto_rawDescGZIP() []byte {
	file_sch_dept_v1_sch_dept_proto_rawDescOnce.Do(func() {
		file_sch_dept_v1_sch_dept_proto_rawDescData = protoimpl.X.CompressGZIP(file_sch_dept_v1_sch_dept_proto_rawDescData)
	})
	return file_sch_dept_v1_sch_dept_proto_rawDescData
}

var file_sch_dept_v1_sch_dept_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_sch_dept_v1_sch_dept_proto_goTypes = []interface{}{
	(*GetClassReq)(nil),                           // 0: api.sch_dept.v1.GetClassReq
	(*GetClassRsp)(nil),                           // 1: api.sch_dept.v1.GetClassRsp
	(*ListClassReq)(nil),                          // 2: api.sch_dept.v1.ListClassReq
	(*ListClassRsp)(nil),                          // 3: api.sch_dept.v1.ListClassRsp
	(*ListClassOfIdsReq)(nil),                     // 4: api.sch_dept.v1.ListClassOfIdsReq
	(*ListClassOfIdsRsp)(nil),                     // 5: api.sch_dept.v1.ListClassOfIdsRsp
	(*VerifyClassListIsExistReq)(nil),             // 6: api.sch_dept.v1.VerifyClassListIsExistReq
	(*VerifyClassListIsExistRsp)(nil),             // 7: api.sch_dept.v1.VerifyClassListIsExistRsp
	(*ListClassOnUserReq)(nil),                    // 8: api.sch_dept.v1.ListClassOnUserReq
	(*ListClassOnUserRsp)(nil),                    // 9: api.sch_dept.v1.ListClassOnUserRsp
	(*CreateGradeReq)(nil),                        // 10: api.sch_dept.v1.CreateGradeReq
	(*CreateGradeRsp)(nil),                        // 11: api.sch_dept.v1.CreateGradeRsp
	(*UpdateGradeReq)(nil),                        // 12: api.sch_dept.v1.UpdateGradeReq
	(*DeleteGradeReq)(nil),                        // 13: api.sch_dept.v1.DeleteGradeReq
	(*ListSchDeptReq)(nil),                        // 14: api.sch_dept.v1.ListSchDeptReq
	(*SchDeptTree)(nil),                           // 15: api.sch_dept.v1.SchDeptTree
	(*CreateClassReq)(nil),                        // 16: api.sch_dept.v1.CreateClassReq
	(*UpdateClassReq)(nil),                        // 17: api.sch_dept.v1.UpdateClassReq
	(*UpgradeClassReq)(nil),                       // 18: api.sch_dept.v1.UpgradeClassReq
	(*GraduateClassReq)(nil),                      // 19: api.sch_dept.v1.GraduateClassReq
	(*DeleteClassReq)(nil),                        // 20: api.sch_dept.v1.DeleteClassReq
	(*TeachInfo)(nil),                             // 21: api.sch_dept.v1.TeachInfo
	(*BatchUpdateSchDeptSeqOrderReq)(nil),         // 22: api.sch_dept.v1.BatchUpdateSchDeptSeqOrderReq
	(*ListClassRsp_ClassInfo)(nil),                // 23: api.sch_dept.v1.ListClassRsp.ClassInfo
	(*ListClassOfIdsRsp_ClassInfo)(nil),           // 24: api.sch_dept.v1.ListClassOfIdsRsp.ClassInfo
	(*ListClassOnUserRsp_ClassInfo)(nil),          // 25: api.sch_dept.v1.ListClassOnUserRsp.ClassInfo
	(*SchDeptTree_StuActivateInfo)(nil),           // 26: api.sch_dept.v1.SchDeptTree.StuActivateInfo
	(*TeachInfo_Teacher)(nil),                     // 27: api.sch_dept.v1.TeachInfo.Teacher
	(*BatchUpdateSchDeptSeqOrderReq_SeqInfo)(nil), // 28: api.sch_dept.v1.BatchUpdateSchDeptSeqOrderReq.SeqInfo
	(*emptypb.Empty)(nil),                         // 29: google.protobuf.Empty
}
var file_sch_dept_v1_sch_dept_proto_depIdxs = []int32{
	23, // 0: api.sch_dept.v1.ListClassRsp.list:type_name -> api.sch_dept.v1.ListClassRsp.ClassInfo
	24, // 1: api.sch_dept.v1.ListClassOfIdsRsp.list:type_name -> api.sch_dept.v1.ListClassOfIdsRsp.ClassInfo
	25, // 2: api.sch_dept.v1.ListClassOnUserRsp.list:type_name -> api.sch_dept.v1.ListClassOnUserRsp.ClassInfo
	21, // 3: api.sch_dept.v1.SchDeptTree.teach_info:type_name -> api.sch_dept.v1.TeachInfo
	26, // 4: api.sch_dept.v1.SchDeptTree.student_activate_info:type_name -> api.sch_dept.v1.SchDeptTree.StuActivateInfo
	15, // 5: api.sch_dept.v1.SchDeptTree.children:type_name -> api.sch_dept.v1.SchDeptTree
	21, // 6: api.sch_dept.v1.CreateClassReq.teachers:type_name -> api.sch_dept.v1.TeachInfo
	21, // 7: api.sch_dept.v1.UpdateClassReq.teach_info:type_name -> api.sch_dept.v1.TeachInfo
	27, // 8: api.sch_dept.v1.TeachInfo.teachers:type_name -> api.sch_dept.v1.TeachInfo.Teacher
	28, // 9: api.sch_dept.v1.BatchUpdateSchDeptSeqOrderReq.list:type_name -> api.sch_dept.v1.BatchUpdateSchDeptSeqOrderReq.SeqInfo
	10, // 10: api.sch_dept.v1.SchDept.CreateGrade:input_type -> api.sch_dept.v1.CreateGradeReq
	12, // 11: api.sch_dept.v1.SchDept.UpdateGrade:input_type -> api.sch_dept.v1.UpdateGradeReq
	13, // 12: api.sch_dept.v1.SchDept.DeleteGrade:input_type -> api.sch_dept.v1.DeleteGradeReq
	14, // 13: api.sch_dept.v1.SchDept.ListSchDept:input_type -> api.sch_dept.v1.ListSchDeptReq
	8,  // 14: api.sch_dept.v1.SchDept.ListClassOnUser:input_type -> api.sch_dept.v1.ListClassOnUserReq
	16, // 15: api.sch_dept.v1.SchDept.CreateClass:input_type -> api.sch_dept.v1.CreateClassReq
	17, // 16: api.sch_dept.v1.SchDept.UpdateClass:input_type -> api.sch_dept.v1.UpdateClassReq
	18, // 17: api.sch_dept.v1.SchDept.PromoteClass:input_type -> api.sch_dept.v1.UpgradeClassReq
	19, // 18: api.sch_dept.v1.SchDept.GraduateClass:input_type -> api.sch_dept.v1.GraduateClassReq
	20, // 19: api.sch_dept.v1.SchDept.DeleteClass:input_type -> api.sch_dept.v1.DeleteClassReq
	22, // 20: api.sch_dept.v1.SchDept.BatchUpdateSchDeptSeqOrder:input_type -> api.sch_dept.v1.BatchUpdateSchDeptSeqOrderReq
	6,  // 21: api.sch_dept.v1.SchDept.VerifyClassListIsExist:input_type -> api.sch_dept.v1.VerifyClassListIsExistReq
	4,  // 22: api.sch_dept.v1.SchDept.ListClassOfIds:input_type -> api.sch_dept.v1.ListClassOfIdsReq
	2,  // 23: api.sch_dept.v1.SchDept.ListClass:input_type -> api.sch_dept.v1.ListClassReq
	0,  // 24: api.sch_dept.v1.SchDept.GetClass:input_type -> api.sch_dept.v1.GetClassReq
	11, // 25: api.sch_dept.v1.SchDept.CreateGrade:output_type -> api.sch_dept.v1.CreateGradeRsp
	29, // 26: api.sch_dept.v1.SchDept.UpdateGrade:output_type -> google.protobuf.Empty
	29, // 27: api.sch_dept.v1.SchDept.DeleteGrade:output_type -> google.protobuf.Empty
	15, // 28: api.sch_dept.v1.SchDept.ListSchDept:output_type -> api.sch_dept.v1.SchDeptTree
	9,  // 29: api.sch_dept.v1.SchDept.ListClassOnUser:output_type -> api.sch_dept.v1.ListClassOnUserRsp
	29, // 30: api.sch_dept.v1.SchDept.CreateClass:output_type -> google.protobuf.Empty
	29, // 31: api.sch_dept.v1.SchDept.UpdateClass:output_type -> google.protobuf.Empty
	29, // 32: api.sch_dept.v1.SchDept.PromoteClass:output_type -> google.protobuf.Empty
	29, // 33: api.sch_dept.v1.SchDept.GraduateClass:output_type -> google.protobuf.Empty
	29, // 34: api.sch_dept.v1.SchDept.DeleteClass:output_type -> google.protobuf.Empty
	29, // 35: api.sch_dept.v1.SchDept.BatchUpdateSchDeptSeqOrder:output_type -> google.protobuf.Empty
	7,  // 36: api.sch_dept.v1.SchDept.VerifyClassListIsExist:output_type -> api.sch_dept.v1.VerifyClassListIsExistRsp
	5,  // 37: api.sch_dept.v1.SchDept.ListClassOfIds:output_type -> api.sch_dept.v1.ListClassOfIdsRsp
	3,  // 38: api.sch_dept.v1.SchDept.ListClass:output_type -> api.sch_dept.v1.ListClassRsp
	1,  // 39: api.sch_dept.v1.SchDept.GetClass:output_type -> api.sch_dept.v1.GetClassRsp
	25, // [25:40] is the sub-list for method output_type
	10, // [10:25] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_sch_dept_v1_sch_dept_proto_init() }
func file_sch_dept_v1_sch_dept_proto_init() {
	if File_sch_dept_v1_sch_dept_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_sch_dept_v1_sch_dept_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClassReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClassRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClassReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClassRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClassOfIdsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClassOfIdsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyClassListIsExistReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyClassListIsExistRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClassOnUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClassOnUserRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGradeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGradeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGradeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGradeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSchDeptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SchDeptTree); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateClassReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClassReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeClassReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GraduateClassReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteClassReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeachInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateSchDeptSeqOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClassRsp_ClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClassOfIdsRsp_ClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClassOnUserRsp_ClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SchDeptTree_StuActivateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeachInfo_Teacher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sch_dept_v1_sch_dept_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateSchDeptSeqOrderReq_SeqInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sch_dept_v1_sch_dept_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sch_dept_v1_sch_dept_proto_goTypes,
		DependencyIndexes: file_sch_dept_v1_sch_dept_proto_depIdxs,
		MessageInfos:      file_sch_dept_v1_sch_dept_proto_msgTypes,
	}.Build()
	File_sch_dept_v1_sch_dept_proto = out.File
	file_sch_dept_v1_sch_dept_proto_rawDesc = nil
	file_sch_dept_v1_sch_dept_proto_goTypes = nil
	file_sch_dept_v1_sch_dept_proto_depIdxs = nil
}
