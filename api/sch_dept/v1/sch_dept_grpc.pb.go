// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: sch_dept/v1/sch_dept.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SchDept_CreateGrade_FullMethodName                = "/api.sch_dept.v1.SchDept/CreateGrade"
	SchDept_UpdateGrade_FullMethodName                = "/api.sch_dept.v1.SchDept/UpdateGrade"
	SchDept_DeleteGrade_FullMethodName                = "/api.sch_dept.v1.SchDept/DeleteGrade"
	SchDept_ListSchDept_FullMethodName                = "/api.sch_dept.v1.SchDept/ListSchDept"
	SchDept_ListClassOnUser_FullMethodName            = "/api.sch_dept.v1.SchDept/ListClassOnUser"
	SchDept_CreateClass_FullMethodName                = "/api.sch_dept.v1.SchDept/CreateClass"
	SchDept_UpdateClass_FullMethodName                = "/api.sch_dept.v1.SchDept/UpdateClass"
	SchDept_PromoteClass_FullMethodName               = "/api.sch_dept.v1.SchDept/PromoteClass"
	SchDept_GraduateClass_FullMethodName              = "/api.sch_dept.v1.SchDept/GraduateClass"
	SchDept_DeleteClass_FullMethodName                = "/api.sch_dept.v1.SchDept/DeleteClass"
	SchDept_BatchUpdateSchDeptSeqOrder_FullMethodName = "/api.sch_dept.v1.SchDept/BatchUpdateSchDeptSeqOrder"
	SchDept_VerifyClassListIsExist_FullMethodName     = "/api.sch_dept.v1.SchDept/VerifyClassListIsExist"
	SchDept_ListClassOfIds_FullMethodName             = "/api.sch_dept.v1.SchDept/ListClassOfIds"
	SchDept_ListClass_FullMethodName                  = "/api.sch_dept.v1.SchDept/ListClass"
	SchDept_GetClass_FullMethodName                   = "/api.sch_dept.v1.SchDept/GetClass"
)

// SchDeptClient is the client API for SchDept service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SchDeptClient interface {
	// 创建年级
	CreateGrade(ctx context.Context, in *CreateGradeReq, opts ...grpc.CallOption) (*CreateGradeRsp, error)
	// 更新年级
	UpdateGrade(ctx context.Context, in *UpdateGradeReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除年级
	DeleteGrade(ctx context.Context, in *DeleteGradeReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取年级班级树
	ListSchDept(ctx context.Context, in *ListSchDeptReq, opts ...grpc.CallOption) (*SchDeptTree, error)
	// 获取指定用户下的班级列表
	ListClassOnUser(ctx context.Context, in *ListClassOnUserReq, opts ...grpc.CallOption) (*ListClassOnUserRsp, error)
	// 创建班级
	CreateClass(ctx context.Context, in *CreateClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新班级
	UpdateClass(ctx context.Context, in *UpdateClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 升班
	PromoteClass(ctx context.Context, in *UpgradeClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 毕业班级
	GraduateClass(ctx context.Context, in *GraduateClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除班级
	DeleteClass(ctx context.Context, in *DeleteClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 批量更新班级年级顺序
	BatchUpdateSchDeptSeqOrder(ctx context.Context, in *BatchUpdateSchDeptSeqOrderReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 判断班级列表是否都在指定的学校中
	VerifyClassListIsExist(ctx context.Context, in *VerifyClassListIsExistReq, opts ...grpc.CallOption) (*VerifyClassListIsExistRsp, error)
	// 通过班级ID列表获取班级基础信息列表
	ListClassOfIds(ctx context.Context, in *ListClassOfIdsReq, opts ...grpc.CallOption) (*ListClassOfIdsRsp, error)
	// 分页获取班级列表
	ListClass(ctx context.Context, in *ListClassReq, opts ...grpc.CallOption) (*ListClassRsp, error)
	// 获取班级信息
	GetClass(ctx context.Context, in *GetClassReq, opts ...grpc.CallOption) (*GetClassRsp, error)
}

type schDeptClient struct {
	cc grpc.ClientConnInterface
}

func NewSchDeptClient(cc grpc.ClientConnInterface) SchDeptClient {
	return &schDeptClient{cc}
}

func (c *schDeptClient) CreateGrade(ctx context.Context, in *CreateGradeReq, opts ...grpc.CallOption) (*CreateGradeRsp, error) {
	out := new(CreateGradeRsp)
	err := c.cc.Invoke(ctx, SchDept_CreateGrade_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) UpdateGrade(ctx context.Context, in *UpdateGradeReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchDept_UpdateGrade_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) DeleteGrade(ctx context.Context, in *DeleteGradeReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchDept_DeleteGrade_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) ListSchDept(ctx context.Context, in *ListSchDeptReq, opts ...grpc.CallOption) (*SchDeptTree, error) {
	out := new(SchDeptTree)
	err := c.cc.Invoke(ctx, SchDept_ListSchDept_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) ListClassOnUser(ctx context.Context, in *ListClassOnUserReq, opts ...grpc.CallOption) (*ListClassOnUserRsp, error) {
	out := new(ListClassOnUserRsp)
	err := c.cc.Invoke(ctx, SchDept_ListClassOnUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) CreateClass(ctx context.Context, in *CreateClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchDept_CreateClass_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) UpdateClass(ctx context.Context, in *UpdateClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchDept_UpdateClass_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) PromoteClass(ctx context.Context, in *UpgradeClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchDept_PromoteClass_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) GraduateClass(ctx context.Context, in *GraduateClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchDept_GraduateClass_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) DeleteClass(ctx context.Context, in *DeleteClassReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchDept_DeleteClass_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) BatchUpdateSchDeptSeqOrder(ctx context.Context, in *BatchUpdateSchDeptSeqOrderReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SchDept_BatchUpdateSchDeptSeqOrder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) VerifyClassListIsExist(ctx context.Context, in *VerifyClassListIsExistReq, opts ...grpc.CallOption) (*VerifyClassListIsExistRsp, error) {
	out := new(VerifyClassListIsExistRsp)
	err := c.cc.Invoke(ctx, SchDept_VerifyClassListIsExist_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) ListClassOfIds(ctx context.Context, in *ListClassOfIdsReq, opts ...grpc.CallOption) (*ListClassOfIdsRsp, error) {
	out := new(ListClassOfIdsRsp)
	err := c.cc.Invoke(ctx, SchDept_ListClassOfIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) ListClass(ctx context.Context, in *ListClassReq, opts ...grpc.CallOption) (*ListClassRsp, error) {
	out := new(ListClassRsp)
	err := c.cc.Invoke(ctx, SchDept_ListClass_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schDeptClient) GetClass(ctx context.Context, in *GetClassReq, opts ...grpc.CallOption) (*GetClassRsp, error) {
	out := new(GetClassRsp)
	err := c.cc.Invoke(ctx, SchDept_GetClass_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SchDeptServer is the server API for SchDept service.
// All implementations must embed UnimplementedSchDeptServer
// for forward compatibility
type SchDeptServer interface {
	// 创建年级
	CreateGrade(context.Context, *CreateGradeReq) (*CreateGradeRsp, error)
	// 更新年级
	UpdateGrade(context.Context, *UpdateGradeReq) (*emptypb.Empty, error)
	// 删除年级
	DeleteGrade(context.Context, *DeleteGradeReq) (*emptypb.Empty, error)
	// 获取年级班级树
	ListSchDept(context.Context, *ListSchDeptReq) (*SchDeptTree, error)
	// 获取指定用户下的班级列表
	ListClassOnUser(context.Context, *ListClassOnUserReq) (*ListClassOnUserRsp, error)
	// 创建班级
	CreateClass(context.Context, *CreateClassReq) (*emptypb.Empty, error)
	// 更新班级
	UpdateClass(context.Context, *UpdateClassReq) (*emptypb.Empty, error)
	// 升班
	PromoteClass(context.Context, *UpgradeClassReq) (*emptypb.Empty, error)
	// 毕业班级
	GraduateClass(context.Context, *GraduateClassReq) (*emptypb.Empty, error)
	// 删除班级
	DeleteClass(context.Context, *DeleteClassReq) (*emptypb.Empty, error)
	// 批量更新班级年级顺序
	BatchUpdateSchDeptSeqOrder(context.Context, *BatchUpdateSchDeptSeqOrderReq) (*emptypb.Empty, error)
	// 判断班级列表是否都在指定的学校中
	VerifyClassListIsExist(context.Context, *VerifyClassListIsExistReq) (*VerifyClassListIsExistRsp, error)
	// 通过班级ID列表获取班级基础信息列表
	ListClassOfIds(context.Context, *ListClassOfIdsReq) (*ListClassOfIdsRsp, error)
	// 分页获取班级列表
	ListClass(context.Context, *ListClassReq) (*ListClassRsp, error)
	// 获取班级信息
	GetClass(context.Context, *GetClassReq) (*GetClassRsp, error)
	mustEmbedUnimplementedSchDeptServer()
}

// UnimplementedSchDeptServer must be embedded to have forward compatible implementations.
type UnimplementedSchDeptServer struct {
}

func (UnimplementedSchDeptServer) CreateGrade(context.Context, *CreateGradeReq) (*CreateGradeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGrade not implemented")
}
func (UnimplementedSchDeptServer) UpdateGrade(context.Context, *UpdateGradeReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGrade not implemented")
}
func (UnimplementedSchDeptServer) DeleteGrade(context.Context, *DeleteGradeReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGrade not implemented")
}
func (UnimplementedSchDeptServer) ListSchDept(context.Context, *ListSchDeptReq) (*SchDeptTree, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSchDept not implemented")
}
func (UnimplementedSchDeptServer) ListClassOnUser(context.Context, *ListClassOnUserReq) (*ListClassOnUserRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClassOnUser not implemented")
}
func (UnimplementedSchDeptServer) CreateClass(context.Context, *CreateClassReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateClass not implemented")
}
func (UnimplementedSchDeptServer) UpdateClass(context.Context, *UpdateClassReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateClass not implemented")
}
func (UnimplementedSchDeptServer) PromoteClass(context.Context, *UpgradeClassReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PromoteClass not implemented")
}
func (UnimplementedSchDeptServer) GraduateClass(context.Context, *GraduateClassReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GraduateClass not implemented")
}
func (UnimplementedSchDeptServer) DeleteClass(context.Context, *DeleteClassReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteClass not implemented")
}
func (UnimplementedSchDeptServer) BatchUpdateSchDeptSeqOrder(context.Context, *BatchUpdateSchDeptSeqOrderReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateSchDeptSeqOrder not implemented")
}
func (UnimplementedSchDeptServer) VerifyClassListIsExist(context.Context, *VerifyClassListIsExistReq) (*VerifyClassListIsExistRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyClassListIsExist not implemented")
}
func (UnimplementedSchDeptServer) ListClassOfIds(context.Context, *ListClassOfIdsReq) (*ListClassOfIdsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClassOfIds not implemented")
}
func (UnimplementedSchDeptServer) ListClass(context.Context, *ListClassReq) (*ListClassRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClass not implemented")
}
func (UnimplementedSchDeptServer) GetClass(context.Context, *GetClassReq) (*GetClassRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClass not implemented")
}
func (UnimplementedSchDeptServer) mustEmbedUnimplementedSchDeptServer() {}

// UnsafeSchDeptServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SchDeptServer will
// result in compilation errors.
type UnsafeSchDeptServer interface {
	mustEmbedUnimplementedSchDeptServer()
}

func RegisterSchDeptServer(s grpc.ServiceRegistrar, srv SchDeptServer) {
	s.RegisterService(&SchDept_ServiceDesc, srv)
}

func _SchDept_CreateGrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGradeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).CreateGrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_CreateGrade_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).CreateGrade(ctx, req.(*CreateGradeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_UpdateGrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGradeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).UpdateGrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_UpdateGrade_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).UpdateGrade(ctx, req.(*UpdateGradeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_DeleteGrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGradeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).DeleteGrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_DeleteGrade_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).DeleteGrade(ctx, req.(*DeleteGradeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_ListSchDept_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSchDeptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).ListSchDept(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_ListSchDept_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).ListSchDept(ctx, req.(*ListSchDeptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_ListClassOnUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClassOnUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).ListClassOnUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_ListClassOnUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).ListClassOnUser(ctx, req.(*ListClassOnUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_CreateClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateClassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).CreateClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_CreateClass_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).CreateClass(ctx, req.(*CreateClassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_UpdateClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).UpdateClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_UpdateClass_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).UpdateClass(ctx, req.(*UpdateClassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_PromoteClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpgradeClassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).PromoteClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_PromoteClass_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).PromoteClass(ctx, req.(*UpgradeClassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_GraduateClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GraduateClassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).GraduateClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_GraduateClass_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).GraduateClass(ctx, req.(*GraduateClassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_DeleteClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteClassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).DeleteClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_DeleteClass_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).DeleteClass(ctx, req.(*DeleteClassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_BatchUpdateSchDeptSeqOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateSchDeptSeqOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).BatchUpdateSchDeptSeqOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_BatchUpdateSchDeptSeqOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).BatchUpdateSchDeptSeqOrder(ctx, req.(*BatchUpdateSchDeptSeqOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_VerifyClassListIsExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyClassListIsExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).VerifyClassListIsExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_VerifyClassListIsExist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).VerifyClassListIsExist(ctx, req.(*VerifyClassListIsExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_ListClassOfIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClassOfIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).ListClassOfIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_ListClassOfIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).ListClassOfIds(ctx, req.(*ListClassOfIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_ListClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).ListClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_ListClass_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).ListClass(ctx, req.(*ListClassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SchDept_GetClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchDeptServer).GetClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SchDept_GetClass_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchDeptServer).GetClass(ctx, req.(*GetClassReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SchDept_ServiceDesc is the grpc.ServiceDesc for SchDept service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SchDept_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.sch_dept.v1.SchDept",
	HandlerType: (*SchDeptServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGrade",
			Handler:    _SchDept_CreateGrade_Handler,
		},
		{
			MethodName: "UpdateGrade",
			Handler:    _SchDept_UpdateGrade_Handler,
		},
		{
			MethodName: "DeleteGrade",
			Handler:    _SchDept_DeleteGrade_Handler,
		},
		{
			MethodName: "ListSchDept",
			Handler:    _SchDept_ListSchDept_Handler,
		},
		{
			MethodName: "ListClassOnUser",
			Handler:    _SchDept_ListClassOnUser_Handler,
		},
		{
			MethodName: "CreateClass",
			Handler:    _SchDept_CreateClass_Handler,
		},
		{
			MethodName: "UpdateClass",
			Handler:    _SchDept_UpdateClass_Handler,
		},
		{
			MethodName: "PromoteClass",
			Handler:    _SchDept_PromoteClass_Handler,
		},
		{
			MethodName: "GraduateClass",
			Handler:    _SchDept_GraduateClass_Handler,
		},
		{
			MethodName: "DeleteClass",
			Handler:    _SchDept_DeleteClass_Handler,
		},
		{
			MethodName: "BatchUpdateSchDeptSeqOrder",
			Handler:    _SchDept_BatchUpdateSchDeptSeqOrder_Handler,
		},
		{
			MethodName: "VerifyClassListIsExist",
			Handler:    _SchDept_VerifyClassListIsExist_Handler,
		},
		{
			MethodName: "ListClassOfIds",
			Handler:    _SchDept_ListClassOfIds_Handler,
		},
		{
			MethodName: "ListClass",
			Handler:    _SchDept_ListClass_Handler,
		},
		{
			MethodName: "GetClass",
			Handler:    _SchDept_GetClass_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sch_dept/v1/sch_dept.proto",
}
