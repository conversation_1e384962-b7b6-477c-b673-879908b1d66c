// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.0
// source: api/monitor/v1/monitor.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	VideoMonitor_ListZxMonitor_FullMethodName                 = "/api.monitor.v1.VideoMonitor/ListZxMonitor"
	VideoMonitor_MergeZxMonitorNode_FullMethodName            = "/api.monitor.v1.VideoMonitor/MergeZxMonitorNode"
	VideoMonitor_UpdateZxMonitor_FullMethodName               = "/api.monitor.v1.VideoMonitor/UpdateZxMonitor"
	VideoMonitor_DeleteZxMonitor_FullMethodName               = "/api.monitor.v1.VideoMonitor/DeleteZxMonitor"
	VideoMonitor_SwitchZxMonitorQuality_FullMethodName        = "/api.monitor.v1.VideoMonitor/SwitchZxMonitorQuality"
	VideoMonitor_ListMonitorDevice_FullMethodName             = "/api.monitor.v1.VideoMonitor/ListMonitorDevice"
	VideoMonitor_CreateMonitorDevice_FullMethodName           = "/api.monitor.v1.VideoMonitor/CreateMonitorDevice"
	VideoMonitor_UpdateMonitorDevice_FullMethodName           = "/api.monitor.v1.VideoMonitor/UpdateMonitorDevice"
	VideoMonitor_DeleteMonitorDevice_FullMethodName           = "/api.monitor.v1.VideoMonitor/DeleteMonitorDevice"
	VideoMonitor_GetMonitorDeviceStatus_FullMethodName        = "/api.monitor.v1.VideoMonitor/GetMonitorDeviceStatus"
	VideoMonitor_GetMonitorDevice_FullMethodName              = "/api.monitor.v1.VideoMonitor/GetMonitorDevice"
	VideoMonitor_CreateMonitorCamera_FullMethodName           = "/api.monitor.v1.VideoMonitor/CreateMonitorCamera"
	VideoMonitor_UpdateMonitorCamera_FullMethodName           = "/api.monitor.v1.VideoMonitor/UpdateMonitorCamera"
	VideoMonitor_DeleteMonitorCamera_FullMethodName           = "/api.monitor.v1.VideoMonitor/DeleteMonitorCamera"
	VideoMonitor_GetClassCamera_FullMethodName                = "/api.monitor.v1.VideoMonitor/GetClassCamera"
	VideoMonitor_UpdateClassCamera_FullMethodName             = "/api.monitor.v1.VideoMonitor/UpdateClassCamera"
	VideoMonitor_ClearClassCamera_FullMethodName              = "/api.monitor.v1.VideoMonitor/ClearClassCamera"
	VideoMonitor_GetStaffCamera_FullMethodName                = "/api.monitor.v1.VideoMonitor/GetStaffCamera"
	VideoMonitor_UpdateStaffCamera_FullMethodName             = "/api.monitor.v1.VideoMonitor/UpdateStaffCamera"
	VideoMonitor_GetClassAccessTime_FullMethodName            = "/api.monitor.v1.VideoMonitor/GetClassAccessTime"
	VideoMonitor_UpdateClassAccessTime_FullMethodName         = "/api.monitor.v1.VideoMonitor/UpdateClassAccessTime"
	VideoMonitor_GetClassIdsByCameraName_FullMethodName       = "/api.monitor.v1.VideoMonitor/GetClassIdsByCameraName"
	VideoMonitor_FindClassCamera_FullMethodName               = "/api.monitor.v1.VideoMonitor/FindClassCamera"
	VideoMonitor_FindStaffCamera_FullMethodName               = "/api.monitor.v1.VideoMonitor/FindStaffCamera"
	VideoMonitor_GetMonitorCameraConfig_FullMethodName        = "/api.monitor.v1.VideoMonitor/GetMonitorCameraConfig"
	VideoMonitor_ListMonitorCamera_FullMethodName             = "/api.monitor.v1.VideoMonitor/ListMonitorCamera"
	VideoMonitor_ListMonitorCameraOnlineStatus_FullMethodName = "/api.monitor.v1.VideoMonitor/ListMonitorCameraOnlineStatus"
	VideoMonitor_ListClassCameraAccessTime_FullMethodName     = "/api.monitor.v1.VideoMonitor/ListClassCameraAccessTime"
	VideoMonitor_GetWatchUser_FullMethodName                  = "/api.monitor.v1.VideoMonitor/GetWatchUser"
	VideoMonitor_ReportWatchRecord_FullMethodName             = "/api.monitor.v1.VideoMonitor/ReportWatchRecord"
	VideoMonitor_StatsTodayWatch_FullMethodName               = "/api.monitor.v1.VideoMonitor/StatsTodayWatch"
	VideoMonitor_ListWatchRecord_FullMethodName               = "/api.monitor.v1.VideoMonitor/ListWatchRecord"
	VideoMonitor_StatsStudentWatch_FullMethodName             = "/api.monitor.v1.VideoMonitor/StatsStudentWatch"
	VideoMonitor_StatsParentWatch_FullMethodName              = "/api.monitor.v1.VideoMonitor/StatsParentWatch"
	VideoMonitor_GetChannelHistoryPlayback_FullMethodName     = "/api.monitor.v1.VideoMonitor/GetChannelHistoryPlayback"
	VideoMonitor_GetParentPaybackUrl_FullMethodName           = "/api.monitor.v1.VideoMonitor/GetParentPaybackUrl"
	VideoMonitor_QiniuToYsy_FullMethodName                    = "/api.monitor.v1.VideoMonitor/QiniuToYsy"
	VideoMonitor_YsyDeviceInfoExport_FullMethodName           = "/api.monitor.v1.VideoMonitor/YsyDeviceInfoExport"
	VideoMonitor_GetAuthorizedInstClassCamera_FullMethodName  = "/api.monitor.v1.VideoMonitor/GetAuthorizedInstClassCamera"
	VideoMonitor_SetLiveModel_FullMethodName                  = "/api.monitor.v1.VideoMonitor/SetLiveModel"
	VideoMonitor_QiniuBack_FullMethodName                     = "/api.monitor.v1.VideoMonitor/QiniuBack"
	VideoMonitor_CronCloseChannel_FullMethodName              = "/api.monitor.v1.VideoMonitor/CronCloseChannel"
)

// VideoMonitorClient is the client API for VideoMonitor service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VideoMonitorClient interface {
	// 获取掌心宝贝摄像头列表
	ListZxMonitor(ctx context.Context, in *ListZxMonitorReq, opts ...grpc.CallOption) (*ListZxMonitorRsp, error)
	// 合并掌心宝贝摄像头所属节点
	MergeZxMonitorNode(ctx context.Context, in *MergeZxMonitorNodeReq, opts ...grpc.CallOption) (*MergeZxMonitorNodeRsp, error)
	// 更新掌心宝贝摄像头信息
	UpdateZxMonitor(ctx context.Context, in *UpdateZxMonitorReq, opts ...grpc.CallOption) (*UpdateZxMonitorRsp, error)
	// 删除掌心宝贝摄像头
	DeleteZxMonitor(ctx context.Context, in *DeleteZxMonitorReq, opts ...grpc.CallOption) (*DeleteZxMonitorRsp, error)
	// 切换掌心宝贝摄像头清晰度
	SwitchZxMonitorQuality(ctx context.Context, in *SwitchZxMonitorQualityReq, opts ...grpc.CallOption) (*SwitchZxMonitorQualityRsp, error)
	// 监控设备列表
	ListMonitorDevice(ctx context.Context, in *ListMonitorDeviceReq, opts ...grpc.CallOption) (*ListMonitorDeviceRsp, error)
	// 添加监控设备
	CreateMonitorDevice(ctx context.Context, in *CreateMonitorDeviceReq, opts ...grpc.CallOption) (*CreateMonitorDeviceRsp, error)
	// 编辑监控设备
	UpdateMonitorDevice(ctx context.Context, in *UpdateMonitorDeviceReq, opts ...grpc.CallOption) (*UpdateMonitorDeviceRsp, error)
	// 删除监控设备
	DeleteMonitorDevice(ctx context.Context, in *DeleteMonitorDeviceReq, opts ...grpc.CallOption) (*DeleteMonitorDeviceRsp, error)
	// 获取监控设备状态
	GetMonitorDeviceStatus(ctx context.Context, in *GetMonitorDeviceStatusReq, opts ...grpc.CallOption) (*GetMonitorDeviceStatusRsp, error)
	// 获取监控设备信息
	GetMonitorDevice(ctx context.Context, in *GetMonitorDeviceReq, opts ...grpc.CallOption) (*MonitorDevice, error)
	// 添加监控摄像头
	CreateMonitorCamera(ctx context.Context, in *CreateMonitorCameraReq, opts ...grpc.CallOption) (*CreateMonitorCameraRsp, error)
	// 编辑监控摄像头
	UpdateMonitorCamera(ctx context.Context, in *UpdateMonitorCameraReq, opts ...grpc.CallOption) (*UpdateMonitorCameraRsp, error)
	// 删除监控摄像头
	DeleteMonitorCamera(ctx context.Context, in *DeleteMonitorCameraReq, opts ...grpc.CallOption) (*DeleteMonitorCameraRsp, error)
	// 获取班级授权摄像头
	GetClassCamera(ctx context.Context, in *GetClassCameraReq, opts ...grpc.CallOption) (*ClassCameraAuthInfo, error)
	// 更新班级授权摄像头
	UpdateClassCamera(ctx context.Context, in *UpdateClassCameraReq, opts ...grpc.CallOption) (*UpdateClassCameraRsp, error)
	// 清除学校班级授权摄像头
	ClearClassCamera(ctx context.Context, in *ClearClassCameraReq, opts ...grpc.CallOption) (*ClearClassCameraRsp, error)
	// 教职工绑定信息
	GetStaffCamera(ctx context.Context, in *GetStaffCameraReq, opts ...grpc.CallOption) (*StaffCameraAuthInfo, error)
	// 更新教职工授权
	UpdateStaffCamera(ctx context.Context, in *UpdateStaffCameraReq, opts ...grpc.CallOption) (*UpdateStaffCameraRsp, error)
	// 班级访问时间段信息
	GetClassAccessTime(ctx context.Context, in *GetClassAccessTimeReq, opts ...grpc.CallOption) (*GetClassAccessTimeRsp, error)
	// 设置班级访问时间
	UpdateClassAccessTime(ctx context.Context, in *UpdateClassAccessTimeReq, opts ...grpc.CallOption) (*UpdateClassAccessTimeRsp, error)
	// 根据摄像头名称查询关联班级id列表
	GetClassIdsByCameraName(ctx context.Context, in *GetClassIdsByCameraNameReq, opts ...grpc.CallOption) (*GetClassIdsByCameraNameRsp, error)
	// 获取班级授权摄像头信息
	FindClassCamera(ctx context.Context, in *FindClassCameraReq, opts ...grpc.CallOption) (*FindClassCameraRsp, error)
	// 获取教职工授权摄像头信息
	FindStaffCamera(ctx context.Context, in *FindStaffCameraReq, opts ...grpc.CallOption) (*FindStaffCameraRsp, error)
	// 获取配置信息
	GetMonitorCameraConfig(ctx context.Context, in *GetMonitorCameraConfigReq, opts ...grpc.CallOption) (*GetMonitorCameraConfigRsp, error)
	// 获取视频监控摄像头列表
	ListMonitorCamera(ctx context.Context, in *ListMonitorCameraReq, opts ...grpc.CallOption) (*ListMonitorCameraRsp, error)
	// 获取所有监控摄像头在线状态信息
	ListMonitorCameraOnlineStatus(ctx context.Context, in *ListMonitorCameraOnlineStatusReq, opts ...grpc.CallOption) (*ListMonitorCameraOnlineStatusRsp, error)
	// 获取班级摄像头开放时间信息
	ListClassCameraAccessTime(ctx context.Context, in *ListClassCameraAccessTimeReq, opts ...grpc.CallOption) (*ListClassCameraAccessTimeRsp, error)
	// 获取视频监控摄像头观看用户列表
	GetWatchUser(ctx context.Context, in *GetWatchUserReq, opts ...grpc.CallOption) (*GetWatchUserRsp, error)
	// 上报观看记录
	ReportWatchRecord(ctx context.Context, in *ReportWatchRecordReq, opts ...grpc.CallOption) (*ReportWatchRecordRsp, error)
	// 统计今日观看信息
	StatsTodayWatch(ctx context.Context, in *StatsTodayWatchReq, opts ...grpc.CallOption) (*StatsTodayWatchRsp, error)
	// 获取观看明细记录列表
	ListWatchRecord(ctx context.Context, in *ListWatchRecordReq, opts ...grpc.CallOption) (*ListWatchRecordRsp, error)
	// 获取学生观看统计信息
	StatsStudentWatch(ctx context.Context, in *StatsStudentWatchReq, opts ...grpc.CallOption) (*StatsStudentWatchRsp, error)
	// 获取家长观看统计信息
	StatsParentWatch(ctx context.Context, in *StatsParentWatchReq, opts ...grpc.CallOption) (*StatsParentWatchRsp, error)
	// 获取通道历史回放列表
	GetChannelHistoryPlayback(ctx context.Context, in *GetChannelHistoryPlaybackReq, opts ...grpc.CallOption) (*GetChannelHistoryPlaybackRsp, error)
	// 获取家长观看回放url
	GetParentPaybackUrl(ctx context.Context, in *GetParentPaybackUrlReq, opts ...grpc.CallOption) (*GetParentPaybackUrlRsp, error)
	// 七牛转萤石云
	QiniuToYsy(ctx context.Context, in *QiniuToYsyReq, opts ...grpc.CallOption) (*QiniuToYsyRsp, error)
	// 导出萤石云国标
	YsyDeviceInfoExport(ctx context.Context, in *YsyDeviceInfoExportReq, opts ...grpc.CallOption) (*YsyDeviceInfoExportRsp, error)
	// 查询学校班级授权摄像头信息
	GetAuthorizedInstClassCamera(ctx context.Context, in *GetAuthorizedInstClassCameraReq, opts ...grpc.CallOption) (*GetAuthorizedInstClassCameraRsp, error)
	// 配置清晰度
	SetLiveModel(ctx context.Context, in *SetLiveModelReq, opts ...grpc.CallOption) (*SetLiveModelRsp, error)
	// 七牛回归
	QiniuBack(ctx context.Context, in *QiniuBackReq, opts ...grpc.CallOption) (*QiniuBackRsp, error)
	// 定时关闭通道
	CronCloseChannel(ctx context.Context, in *CronCloseChannelReq, opts ...grpc.CallOption) (*CronCloseChannelRsp, error)
}

type videoMonitorClient struct {
	cc grpc.ClientConnInterface
}

func NewVideoMonitorClient(cc grpc.ClientConnInterface) VideoMonitorClient {
	return &videoMonitorClient{cc}
}

func (c *videoMonitorClient) ListZxMonitor(ctx context.Context, in *ListZxMonitorReq, opts ...grpc.CallOption) (*ListZxMonitorRsp, error) {
	out := new(ListZxMonitorRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_ListZxMonitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) MergeZxMonitorNode(ctx context.Context, in *MergeZxMonitorNodeReq, opts ...grpc.CallOption) (*MergeZxMonitorNodeRsp, error) {
	out := new(MergeZxMonitorNodeRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_MergeZxMonitorNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) UpdateZxMonitor(ctx context.Context, in *UpdateZxMonitorReq, opts ...grpc.CallOption) (*UpdateZxMonitorRsp, error) {
	out := new(UpdateZxMonitorRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_UpdateZxMonitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) DeleteZxMonitor(ctx context.Context, in *DeleteZxMonitorReq, opts ...grpc.CallOption) (*DeleteZxMonitorRsp, error) {
	out := new(DeleteZxMonitorRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_DeleteZxMonitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) SwitchZxMonitorQuality(ctx context.Context, in *SwitchZxMonitorQualityReq, opts ...grpc.CallOption) (*SwitchZxMonitorQualityRsp, error) {
	out := new(SwitchZxMonitorQualityRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_SwitchZxMonitorQuality_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) ListMonitorDevice(ctx context.Context, in *ListMonitorDeviceReq, opts ...grpc.CallOption) (*ListMonitorDeviceRsp, error) {
	out := new(ListMonitorDeviceRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_ListMonitorDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) CreateMonitorDevice(ctx context.Context, in *CreateMonitorDeviceReq, opts ...grpc.CallOption) (*CreateMonitorDeviceRsp, error) {
	out := new(CreateMonitorDeviceRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_CreateMonitorDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) UpdateMonitorDevice(ctx context.Context, in *UpdateMonitorDeviceReq, opts ...grpc.CallOption) (*UpdateMonitorDeviceRsp, error) {
	out := new(UpdateMonitorDeviceRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_UpdateMonitorDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) DeleteMonitorDevice(ctx context.Context, in *DeleteMonitorDeviceReq, opts ...grpc.CallOption) (*DeleteMonitorDeviceRsp, error) {
	out := new(DeleteMonitorDeviceRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_DeleteMonitorDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetMonitorDeviceStatus(ctx context.Context, in *GetMonitorDeviceStatusReq, opts ...grpc.CallOption) (*GetMonitorDeviceStatusRsp, error) {
	out := new(GetMonitorDeviceStatusRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_GetMonitorDeviceStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetMonitorDevice(ctx context.Context, in *GetMonitorDeviceReq, opts ...grpc.CallOption) (*MonitorDevice, error) {
	out := new(MonitorDevice)
	err := c.cc.Invoke(ctx, VideoMonitor_GetMonitorDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) CreateMonitorCamera(ctx context.Context, in *CreateMonitorCameraReq, opts ...grpc.CallOption) (*CreateMonitorCameraRsp, error) {
	out := new(CreateMonitorCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_CreateMonitorCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) UpdateMonitorCamera(ctx context.Context, in *UpdateMonitorCameraReq, opts ...grpc.CallOption) (*UpdateMonitorCameraRsp, error) {
	out := new(UpdateMonitorCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_UpdateMonitorCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) DeleteMonitorCamera(ctx context.Context, in *DeleteMonitorCameraReq, opts ...grpc.CallOption) (*DeleteMonitorCameraRsp, error) {
	out := new(DeleteMonitorCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_DeleteMonitorCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetClassCamera(ctx context.Context, in *GetClassCameraReq, opts ...grpc.CallOption) (*ClassCameraAuthInfo, error) {
	out := new(ClassCameraAuthInfo)
	err := c.cc.Invoke(ctx, VideoMonitor_GetClassCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) UpdateClassCamera(ctx context.Context, in *UpdateClassCameraReq, opts ...grpc.CallOption) (*UpdateClassCameraRsp, error) {
	out := new(UpdateClassCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_UpdateClassCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) ClearClassCamera(ctx context.Context, in *ClearClassCameraReq, opts ...grpc.CallOption) (*ClearClassCameraRsp, error) {
	out := new(ClearClassCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_ClearClassCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetStaffCamera(ctx context.Context, in *GetStaffCameraReq, opts ...grpc.CallOption) (*StaffCameraAuthInfo, error) {
	out := new(StaffCameraAuthInfo)
	err := c.cc.Invoke(ctx, VideoMonitor_GetStaffCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) UpdateStaffCamera(ctx context.Context, in *UpdateStaffCameraReq, opts ...grpc.CallOption) (*UpdateStaffCameraRsp, error) {
	out := new(UpdateStaffCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_UpdateStaffCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetClassAccessTime(ctx context.Context, in *GetClassAccessTimeReq, opts ...grpc.CallOption) (*GetClassAccessTimeRsp, error) {
	out := new(GetClassAccessTimeRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_GetClassAccessTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) UpdateClassAccessTime(ctx context.Context, in *UpdateClassAccessTimeReq, opts ...grpc.CallOption) (*UpdateClassAccessTimeRsp, error) {
	out := new(UpdateClassAccessTimeRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_UpdateClassAccessTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetClassIdsByCameraName(ctx context.Context, in *GetClassIdsByCameraNameReq, opts ...grpc.CallOption) (*GetClassIdsByCameraNameRsp, error) {
	out := new(GetClassIdsByCameraNameRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_GetClassIdsByCameraName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) FindClassCamera(ctx context.Context, in *FindClassCameraReq, opts ...grpc.CallOption) (*FindClassCameraRsp, error) {
	out := new(FindClassCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_FindClassCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) FindStaffCamera(ctx context.Context, in *FindStaffCameraReq, opts ...grpc.CallOption) (*FindStaffCameraRsp, error) {
	out := new(FindStaffCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_FindStaffCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetMonitorCameraConfig(ctx context.Context, in *GetMonitorCameraConfigReq, opts ...grpc.CallOption) (*GetMonitorCameraConfigRsp, error) {
	out := new(GetMonitorCameraConfigRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_GetMonitorCameraConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) ListMonitorCamera(ctx context.Context, in *ListMonitorCameraReq, opts ...grpc.CallOption) (*ListMonitorCameraRsp, error) {
	out := new(ListMonitorCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_ListMonitorCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) ListMonitorCameraOnlineStatus(ctx context.Context, in *ListMonitorCameraOnlineStatusReq, opts ...grpc.CallOption) (*ListMonitorCameraOnlineStatusRsp, error) {
	out := new(ListMonitorCameraOnlineStatusRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_ListMonitorCameraOnlineStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) ListClassCameraAccessTime(ctx context.Context, in *ListClassCameraAccessTimeReq, opts ...grpc.CallOption) (*ListClassCameraAccessTimeRsp, error) {
	out := new(ListClassCameraAccessTimeRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_ListClassCameraAccessTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetWatchUser(ctx context.Context, in *GetWatchUserReq, opts ...grpc.CallOption) (*GetWatchUserRsp, error) {
	out := new(GetWatchUserRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_GetWatchUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) ReportWatchRecord(ctx context.Context, in *ReportWatchRecordReq, opts ...grpc.CallOption) (*ReportWatchRecordRsp, error) {
	out := new(ReportWatchRecordRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_ReportWatchRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) StatsTodayWatch(ctx context.Context, in *StatsTodayWatchReq, opts ...grpc.CallOption) (*StatsTodayWatchRsp, error) {
	out := new(StatsTodayWatchRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_StatsTodayWatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) ListWatchRecord(ctx context.Context, in *ListWatchRecordReq, opts ...grpc.CallOption) (*ListWatchRecordRsp, error) {
	out := new(ListWatchRecordRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_ListWatchRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) StatsStudentWatch(ctx context.Context, in *StatsStudentWatchReq, opts ...grpc.CallOption) (*StatsStudentWatchRsp, error) {
	out := new(StatsStudentWatchRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_StatsStudentWatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) StatsParentWatch(ctx context.Context, in *StatsParentWatchReq, opts ...grpc.CallOption) (*StatsParentWatchRsp, error) {
	out := new(StatsParentWatchRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_StatsParentWatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetChannelHistoryPlayback(ctx context.Context, in *GetChannelHistoryPlaybackReq, opts ...grpc.CallOption) (*GetChannelHistoryPlaybackRsp, error) {
	out := new(GetChannelHistoryPlaybackRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_GetChannelHistoryPlayback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetParentPaybackUrl(ctx context.Context, in *GetParentPaybackUrlReq, opts ...grpc.CallOption) (*GetParentPaybackUrlRsp, error) {
	out := new(GetParentPaybackUrlRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_GetParentPaybackUrl_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) QiniuToYsy(ctx context.Context, in *QiniuToYsyReq, opts ...grpc.CallOption) (*QiniuToYsyRsp, error) {
	out := new(QiniuToYsyRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_QiniuToYsy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) YsyDeviceInfoExport(ctx context.Context, in *YsyDeviceInfoExportReq, opts ...grpc.CallOption) (*YsyDeviceInfoExportRsp, error) {
	out := new(YsyDeviceInfoExportRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_YsyDeviceInfoExport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) GetAuthorizedInstClassCamera(ctx context.Context, in *GetAuthorizedInstClassCameraReq, opts ...grpc.CallOption) (*GetAuthorizedInstClassCameraRsp, error) {
	out := new(GetAuthorizedInstClassCameraRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_GetAuthorizedInstClassCamera_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) SetLiveModel(ctx context.Context, in *SetLiveModelReq, opts ...grpc.CallOption) (*SetLiveModelRsp, error) {
	out := new(SetLiveModelRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_SetLiveModel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) QiniuBack(ctx context.Context, in *QiniuBackReq, opts ...grpc.CallOption) (*QiniuBackRsp, error) {
	out := new(QiniuBackRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_QiniuBack_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoMonitorClient) CronCloseChannel(ctx context.Context, in *CronCloseChannelReq, opts ...grpc.CallOption) (*CronCloseChannelRsp, error) {
	out := new(CronCloseChannelRsp)
	err := c.cc.Invoke(ctx, VideoMonitor_CronCloseChannel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VideoMonitorServer is the server API for VideoMonitor service.
// All implementations must embed UnimplementedVideoMonitorServer
// for forward compatibility
type VideoMonitorServer interface {
	// 获取掌心宝贝摄像头列表
	ListZxMonitor(context.Context, *ListZxMonitorReq) (*ListZxMonitorRsp, error)
	// 合并掌心宝贝摄像头所属节点
	MergeZxMonitorNode(context.Context, *MergeZxMonitorNodeReq) (*MergeZxMonitorNodeRsp, error)
	// 更新掌心宝贝摄像头信息
	UpdateZxMonitor(context.Context, *UpdateZxMonitorReq) (*UpdateZxMonitorRsp, error)
	// 删除掌心宝贝摄像头
	DeleteZxMonitor(context.Context, *DeleteZxMonitorReq) (*DeleteZxMonitorRsp, error)
	// 切换掌心宝贝摄像头清晰度
	SwitchZxMonitorQuality(context.Context, *SwitchZxMonitorQualityReq) (*SwitchZxMonitorQualityRsp, error)
	// 监控设备列表
	ListMonitorDevice(context.Context, *ListMonitorDeviceReq) (*ListMonitorDeviceRsp, error)
	// 添加监控设备
	CreateMonitorDevice(context.Context, *CreateMonitorDeviceReq) (*CreateMonitorDeviceRsp, error)
	// 编辑监控设备
	UpdateMonitorDevice(context.Context, *UpdateMonitorDeviceReq) (*UpdateMonitorDeviceRsp, error)
	// 删除监控设备
	DeleteMonitorDevice(context.Context, *DeleteMonitorDeviceReq) (*DeleteMonitorDeviceRsp, error)
	// 获取监控设备状态
	GetMonitorDeviceStatus(context.Context, *GetMonitorDeviceStatusReq) (*GetMonitorDeviceStatusRsp, error)
	// 获取监控设备信息
	GetMonitorDevice(context.Context, *GetMonitorDeviceReq) (*MonitorDevice, error)
	// 添加监控摄像头
	CreateMonitorCamera(context.Context, *CreateMonitorCameraReq) (*CreateMonitorCameraRsp, error)
	// 编辑监控摄像头
	UpdateMonitorCamera(context.Context, *UpdateMonitorCameraReq) (*UpdateMonitorCameraRsp, error)
	// 删除监控摄像头
	DeleteMonitorCamera(context.Context, *DeleteMonitorCameraReq) (*DeleteMonitorCameraRsp, error)
	// 获取班级授权摄像头
	GetClassCamera(context.Context, *GetClassCameraReq) (*ClassCameraAuthInfo, error)
	// 更新班级授权摄像头
	UpdateClassCamera(context.Context, *UpdateClassCameraReq) (*UpdateClassCameraRsp, error)
	// 清除学校班级授权摄像头
	ClearClassCamera(context.Context, *ClearClassCameraReq) (*ClearClassCameraRsp, error)
	// 教职工绑定信息
	GetStaffCamera(context.Context, *GetStaffCameraReq) (*StaffCameraAuthInfo, error)
	// 更新教职工授权
	UpdateStaffCamera(context.Context, *UpdateStaffCameraReq) (*UpdateStaffCameraRsp, error)
	// 班级访问时间段信息
	GetClassAccessTime(context.Context, *GetClassAccessTimeReq) (*GetClassAccessTimeRsp, error)
	// 设置班级访问时间
	UpdateClassAccessTime(context.Context, *UpdateClassAccessTimeReq) (*UpdateClassAccessTimeRsp, error)
	// 根据摄像头名称查询关联班级id列表
	GetClassIdsByCameraName(context.Context, *GetClassIdsByCameraNameReq) (*GetClassIdsByCameraNameRsp, error)
	// 获取班级授权摄像头信息
	FindClassCamera(context.Context, *FindClassCameraReq) (*FindClassCameraRsp, error)
	// 获取教职工授权摄像头信息
	FindStaffCamera(context.Context, *FindStaffCameraReq) (*FindStaffCameraRsp, error)
	// 获取配置信息
	GetMonitorCameraConfig(context.Context, *GetMonitorCameraConfigReq) (*GetMonitorCameraConfigRsp, error)
	// 获取视频监控摄像头列表
	ListMonitorCamera(context.Context, *ListMonitorCameraReq) (*ListMonitorCameraRsp, error)
	// 获取所有监控摄像头在线状态信息
	ListMonitorCameraOnlineStatus(context.Context, *ListMonitorCameraOnlineStatusReq) (*ListMonitorCameraOnlineStatusRsp, error)
	// 获取班级摄像头开放时间信息
	ListClassCameraAccessTime(context.Context, *ListClassCameraAccessTimeReq) (*ListClassCameraAccessTimeRsp, error)
	// 获取视频监控摄像头观看用户列表
	GetWatchUser(context.Context, *GetWatchUserReq) (*GetWatchUserRsp, error)
	// 上报观看记录
	ReportWatchRecord(context.Context, *ReportWatchRecordReq) (*ReportWatchRecordRsp, error)
	// 统计今日观看信息
	StatsTodayWatch(context.Context, *StatsTodayWatchReq) (*StatsTodayWatchRsp, error)
	// 获取观看明细记录列表
	ListWatchRecord(context.Context, *ListWatchRecordReq) (*ListWatchRecordRsp, error)
	// 获取学生观看统计信息
	StatsStudentWatch(context.Context, *StatsStudentWatchReq) (*StatsStudentWatchRsp, error)
	// 获取家长观看统计信息
	StatsParentWatch(context.Context, *StatsParentWatchReq) (*StatsParentWatchRsp, error)
	// 获取通道历史回放列表
	GetChannelHistoryPlayback(context.Context, *GetChannelHistoryPlaybackReq) (*GetChannelHistoryPlaybackRsp, error)
	// 获取家长观看回放url
	GetParentPaybackUrl(context.Context, *GetParentPaybackUrlReq) (*GetParentPaybackUrlRsp, error)
	// 七牛转萤石云
	QiniuToYsy(context.Context, *QiniuToYsyReq) (*QiniuToYsyRsp, error)
	// 导出萤石云国标
	YsyDeviceInfoExport(context.Context, *YsyDeviceInfoExportReq) (*YsyDeviceInfoExportRsp, error)
	// 查询学校班级授权摄像头信息
	GetAuthorizedInstClassCamera(context.Context, *GetAuthorizedInstClassCameraReq) (*GetAuthorizedInstClassCameraRsp, error)
	// 配置清晰度
	SetLiveModel(context.Context, *SetLiveModelReq) (*SetLiveModelRsp, error)
	// 七牛回归
	QiniuBack(context.Context, *QiniuBackReq) (*QiniuBackRsp, error)
	// 定时关闭通道
	CronCloseChannel(context.Context, *CronCloseChannelReq) (*CronCloseChannelRsp, error)
	mustEmbedUnimplementedVideoMonitorServer()
}

// UnimplementedVideoMonitorServer must be embedded to have forward compatible implementations.
type UnimplementedVideoMonitorServer struct {
}

func (UnimplementedVideoMonitorServer) ListZxMonitor(context.Context, *ListZxMonitorReq) (*ListZxMonitorRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListZxMonitor not implemented")
}
func (UnimplementedVideoMonitorServer) MergeZxMonitorNode(context.Context, *MergeZxMonitorNodeReq) (*MergeZxMonitorNodeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeZxMonitorNode not implemented")
}
func (UnimplementedVideoMonitorServer) UpdateZxMonitor(context.Context, *UpdateZxMonitorReq) (*UpdateZxMonitorRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateZxMonitor not implemented")
}
func (UnimplementedVideoMonitorServer) DeleteZxMonitor(context.Context, *DeleteZxMonitorReq) (*DeleteZxMonitorRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteZxMonitor not implemented")
}
func (UnimplementedVideoMonitorServer) SwitchZxMonitorQuality(context.Context, *SwitchZxMonitorQualityReq) (*SwitchZxMonitorQualityRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SwitchZxMonitorQuality not implemented")
}
func (UnimplementedVideoMonitorServer) ListMonitorDevice(context.Context, *ListMonitorDeviceReq) (*ListMonitorDeviceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMonitorDevice not implemented")
}
func (UnimplementedVideoMonitorServer) CreateMonitorDevice(context.Context, *CreateMonitorDeviceReq) (*CreateMonitorDeviceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMonitorDevice not implemented")
}
func (UnimplementedVideoMonitorServer) UpdateMonitorDevice(context.Context, *UpdateMonitorDeviceReq) (*UpdateMonitorDeviceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMonitorDevice not implemented")
}
func (UnimplementedVideoMonitorServer) DeleteMonitorDevice(context.Context, *DeleteMonitorDeviceReq) (*DeleteMonitorDeviceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMonitorDevice not implemented")
}
func (UnimplementedVideoMonitorServer) GetMonitorDeviceStatus(context.Context, *GetMonitorDeviceStatusReq) (*GetMonitorDeviceStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMonitorDeviceStatus not implemented")
}
func (UnimplementedVideoMonitorServer) GetMonitorDevice(context.Context, *GetMonitorDeviceReq) (*MonitorDevice, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMonitorDevice not implemented")
}
func (UnimplementedVideoMonitorServer) CreateMonitorCamera(context.Context, *CreateMonitorCameraReq) (*CreateMonitorCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMonitorCamera not implemented")
}
func (UnimplementedVideoMonitorServer) UpdateMonitorCamera(context.Context, *UpdateMonitorCameraReq) (*UpdateMonitorCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMonitorCamera not implemented")
}
func (UnimplementedVideoMonitorServer) DeleteMonitorCamera(context.Context, *DeleteMonitorCameraReq) (*DeleteMonitorCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMonitorCamera not implemented")
}
func (UnimplementedVideoMonitorServer) GetClassCamera(context.Context, *GetClassCameraReq) (*ClassCameraAuthInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClassCamera not implemented")
}
func (UnimplementedVideoMonitorServer) UpdateClassCamera(context.Context, *UpdateClassCameraReq) (*UpdateClassCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateClassCamera not implemented")
}
func (UnimplementedVideoMonitorServer) ClearClassCamera(context.Context, *ClearClassCameraReq) (*ClearClassCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearClassCamera not implemented")
}
func (UnimplementedVideoMonitorServer) GetStaffCamera(context.Context, *GetStaffCameraReq) (*StaffCameraAuthInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffCamera not implemented")
}
func (UnimplementedVideoMonitorServer) UpdateStaffCamera(context.Context, *UpdateStaffCameraReq) (*UpdateStaffCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffCamera not implemented")
}
func (UnimplementedVideoMonitorServer) GetClassAccessTime(context.Context, *GetClassAccessTimeReq) (*GetClassAccessTimeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClassAccessTime not implemented")
}
func (UnimplementedVideoMonitorServer) UpdateClassAccessTime(context.Context, *UpdateClassAccessTimeReq) (*UpdateClassAccessTimeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateClassAccessTime not implemented")
}
func (UnimplementedVideoMonitorServer) GetClassIdsByCameraName(context.Context, *GetClassIdsByCameraNameReq) (*GetClassIdsByCameraNameRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClassIdsByCameraName not implemented")
}
func (UnimplementedVideoMonitorServer) FindClassCamera(context.Context, *FindClassCameraReq) (*FindClassCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindClassCamera not implemented")
}
func (UnimplementedVideoMonitorServer) FindStaffCamera(context.Context, *FindStaffCameraReq) (*FindStaffCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindStaffCamera not implemented")
}
func (UnimplementedVideoMonitorServer) GetMonitorCameraConfig(context.Context, *GetMonitorCameraConfigReq) (*GetMonitorCameraConfigRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMonitorCameraConfig not implemented")
}
func (UnimplementedVideoMonitorServer) ListMonitorCamera(context.Context, *ListMonitorCameraReq) (*ListMonitorCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMonitorCamera not implemented")
}
func (UnimplementedVideoMonitorServer) ListMonitorCameraOnlineStatus(context.Context, *ListMonitorCameraOnlineStatusReq) (*ListMonitorCameraOnlineStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMonitorCameraOnlineStatus not implemented")
}
func (UnimplementedVideoMonitorServer) ListClassCameraAccessTime(context.Context, *ListClassCameraAccessTimeReq) (*ListClassCameraAccessTimeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClassCameraAccessTime not implemented")
}
func (UnimplementedVideoMonitorServer) GetWatchUser(context.Context, *GetWatchUserReq) (*GetWatchUserRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWatchUser not implemented")
}
func (UnimplementedVideoMonitorServer) ReportWatchRecord(context.Context, *ReportWatchRecordReq) (*ReportWatchRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportWatchRecord not implemented")
}
func (UnimplementedVideoMonitorServer) StatsTodayWatch(context.Context, *StatsTodayWatchReq) (*StatsTodayWatchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StatsTodayWatch not implemented")
}
func (UnimplementedVideoMonitorServer) ListWatchRecord(context.Context, *ListWatchRecordReq) (*ListWatchRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWatchRecord not implemented")
}
func (UnimplementedVideoMonitorServer) StatsStudentWatch(context.Context, *StatsStudentWatchReq) (*StatsStudentWatchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StatsStudentWatch not implemented")
}
func (UnimplementedVideoMonitorServer) StatsParentWatch(context.Context, *StatsParentWatchReq) (*StatsParentWatchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StatsParentWatch not implemented")
}
func (UnimplementedVideoMonitorServer) GetChannelHistoryPlayback(context.Context, *GetChannelHistoryPlaybackReq) (*GetChannelHistoryPlaybackRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChannelHistoryPlayback not implemented")
}
func (UnimplementedVideoMonitorServer) GetParentPaybackUrl(context.Context, *GetParentPaybackUrlReq) (*GetParentPaybackUrlRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParentPaybackUrl not implemented")
}
func (UnimplementedVideoMonitorServer) QiniuToYsy(context.Context, *QiniuToYsyReq) (*QiniuToYsyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QiniuToYsy not implemented")
}
func (UnimplementedVideoMonitorServer) YsyDeviceInfoExport(context.Context, *YsyDeviceInfoExportReq) (*YsyDeviceInfoExportRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method YsyDeviceInfoExport not implemented")
}
func (UnimplementedVideoMonitorServer) GetAuthorizedInstClassCamera(context.Context, *GetAuthorizedInstClassCameraReq) (*GetAuthorizedInstClassCameraRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthorizedInstClassCamera not implemented")
}
func (UnimplementedVideoMonitorServer) SetLiveModel(context.Context, *SetLiveModelReq) (*SetLiveModelRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLiveModel not implemented")
}
func (UnimplementedVideoMonitorServer) QiniuBack(context.Context, *QiniuBackReq) (*QiniuBackRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QiniuBack not implemented")
}
func (UnimplementedVideoMonitorServer) CronCloseChannel(context.Context, *CronCloseChannelReq) (*CronCloseChannelRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CronCloseChannel not implemented")
}
func (UnimplementedVideoMonitorServer) mustEmbedUnimplementedVideoMonitorServer() {}

// UnsafeVideoMonitorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VideoMonitorServer will
// result in compilation errors.
type UnsafeVideoMonitorServer interface {
	mustEmbedUnimplementedVideoMonitorServer()
}

func RegisterVideoMonitorServer(s grpc.ServiceRegistrar, srv VideoMonitorServer) {
	s.RegisterService(&VideoMonitor_ServiceDesc, srv)
}

func _VideoMonitor_ListZxMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListZxMonitorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).ListZxMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_ListZxMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).ListZxMonitor(ctx, req.(*ListZxMonitorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_MergeZxMonitorNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeZxMonitorNodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).MergeZxMonitorNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_MergeZxMonitorNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).MergeZxMonitorNode(ctx, req.(*MergeZxMonitorNodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_UpdateZxMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateZxMonitorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).UpdateZxMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_UpdateZxMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).UpdateZxMonitor(ctx, req.(*UpdateZxMonitorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_DeleteZxMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteZxMonitorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).DeleteZxMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_DeleteZxMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).DeleteZxMonitor(ctx, req.(*DeleteZxMonitorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_SwitchZxMonitorQuality_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchZxMonitorQualityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).SwitchZxMonitorQuality(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_SwitchZxMonitorQuality_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).SwitchZxMonitorQuality(ctx, req.(*SwitchZxMonitorQualityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_ListMonitorDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMonitorDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).ListMonitorDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_ListMonitorDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).ListMonitorDevice(ctx, req.(*ListMonitorDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_CreateMonitorDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMonitorDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).CreateMonitorDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_CreateMonitorDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).CreateMonitorDevice(ctx, req.(*CreateMonitorDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_UpdateMonitorDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMonitorDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).UpdateMonitorDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_UpdateMonitorDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).UpdateMonitorDevice(ctx, req.(*UpdateMonitorDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_DeleteMonitorDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMonitorDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).DeleteMonitorDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_DeleteMonitorDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).DeleteMonitorDevice(ctx, req.(*DeleteMonitorDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetMonitorDeviceStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonitorDeviceStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetMonitorDeviceStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetMonitorDeviceStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetMonitorDeviceStatus(ctx, req.(*GetMonitorDeviceStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetMonitorDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonitorDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetMonitorDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetMonitorDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetMonitorDevice(ctx, req.(*GetMonitorDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_CreateMonitorCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMonitorCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).CreateMonitorCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_CreateMonitorCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).CreateMonitorCamera(ctx, req.(*CreateMonitorCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_UpdateMonitorCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMonitorCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).UpdateMonitorCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_UpdateMonitorCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).UpdateMonitorCamera(ctx, req.(*UpdateMonitorCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_DeleteMonitorCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMonitorCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).DeleteMonitorCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_DeleteMonitorCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).DeleteMonitorCamera(ctx, req.(*DeleteMonitorCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetClassCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClassCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetClassCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetClassCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetClassCamera(ctx, req.(*GetClassCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_UpdateClassCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClassCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).UpdateClassCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_UpdateClassCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).UpdateClassCamera(ctx, req.(*UpdateClassCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_ClearClassCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearClassCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).ClearClassCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_ClearClassCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).ClearClassCamera(ctx, req.(*ClearClassCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetStaffCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetStaffCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetStaffCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetStaffCamera(ctx, req.(*GetStaffCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_UpdateStaffCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).UpdateStaffCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_UpdateStaffCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).UpdateStaffCamera(ctx, req.(*UpdateStaffCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetClassAccessTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClassAccessTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetClassAccessTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetClassAccessTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetClassAccessTime(ctx, req.(*GetClassAccessTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_UpdateClassAccessTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClassAccessTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).UpdateClassAccessTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_UpdateClassAccessTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).UpdateClassAccessTime(ctx, req.(*UpdateClassAccessTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetClassIdsByCameraName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClassIdsByCameraNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetClassIdsByCameraName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetClassIdsByCameraName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetClassIdsByCameraName(ctx, req.(*GetClassIdsByCameraNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_FindClassCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindClassCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).FindClassCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_FindClassCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).FindClassCamera(ctx, req.(*FindClassCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_FindStaffCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindStaffCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).FindStaffCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_FindStaffCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).FindStaffCamera(ctx, req.(*FindStaffCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetMonitorCameraConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonitorCameraConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetMonitorCameraConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetMonitorCameraConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetMonitorCameraConfig(ctx, req.(*GetMonitorCameraConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_ListMonitorCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMonitorCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).ListMonitorCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_ListMonitorCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).ListMonitorCamera(ctx, req.(*ListMonitorCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_ListMonitorCameraOnlineStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMonitorCameraOnlineStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).ListMonitorCameraOnlineStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_ListMonitorCameraOnlineStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).ListMonitorCameraOnlineStatus(ctx, req.(*ListMonitorCameraOnlineStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_ListClassCameraAccessTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClassCameraAccessTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).ListClassCameraAccessTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_ListClassCameraAccessTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).ListClassCameraAccessTime(ctx, req.(*ListClassCameraAccessTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetWatchUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWatchUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetWatchUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetWatchUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetWatchUser(ctx, req.(*GetWatchUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_ReportWatchRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportWatchRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).ReportWatchRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_ReportWatchRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).ReportWatchRecord(ctx, req.(*ReportWatchRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_StatsTodayWatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatsTodayWatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).StatsTodayWatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_StatsTodayWatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).StatsTodayWatch(ctx, req.(*StatsTodayWatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_ListWatchRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWatchRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).ListWatchRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_ListWatchRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).ListWatchRecord(ctx, req.(*ListWatchRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_StatsStudentWatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatsStudentWatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).StatsStudentWatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_StatsStudentWatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).StatsStudentWatch(ctx, req.(*StatsStudentWatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_StatsParentWatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatsParentWatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).StatsParentWatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_StatsParentWatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).StatsParentWatch(ctx, req.(*StatsParentWatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetChannelHistoryPlayback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelHistoryPlaybackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetChannelHistoryPlayback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetChannelHistoryPlayback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetChannelHistoryPlayback(ctx, req.(*GetChannelHistoryPlaybackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetParentPaybackUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetParentPaybackUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetParentPaybackUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetParentPaybackUrl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetParentPaybackUrl(ctx, req.(*GetParentPaybackUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_QiniuToYsy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QiniuToYsyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).QiniuToYsy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_QiniuToYsy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).QiniuToYsy(ctx, req.(*QiniuToYsyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_YsyDeviceInfoExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(YsyDeviceInfoExportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).YsyDeviceInfoExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_YsyDeviceInfoExport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).YsyDeviceInfoExport(ctx, req.(*YsyDeviceInfoExportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_GetAuthorizedInstClassCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthorizedInstClassCameraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).GetAuthorizedInstClassCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_GetAuthorizedInstClassCamera_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).GetAuthorizedInstClassCamera(ctx, req.(*GetAuthorizedInstClassCameraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_SetLiveModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLiveModelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).SetLiveModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_SetLiveModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).SetLiveModel(ctx, req.(*SetLiveModelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_QiniuBack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QiniuBackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).QiniuBack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_QiniuBack_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).QiniuBack(ctx, req.(*QiniuBackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoMonitor_CronCloseChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CronCloseChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoMonitorServer).CronCloseChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoMonitor_CronCloseChannel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoMonitorServer).CronCloseChannel(ctx, req.(*CronCloseChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

// VideoMonitor_ServiceDesc is the grpc.ServiceDesc for VideoMonitor service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VideoMonitor_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.monitor.v1.VideoMonitor",
	HandlerType: (*VideoMonitorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListZxMonitor",
			Handler:    _VideoMonitor_ListZxMonitor_Handler,
		},
		{
			MethodName: "MergeZxMonitorNode",
			Handler:    _VideoMonitor_MergeZxMonitorNode_Handler,
		},
		{
			MethodName: "UpdateZxMonitor",
			Handler:    _VideoMonitor_UpdateZxMonitor_Handler,
		},
		{
			MethodName: "DeleteZxMonitor",
			Handler:    _VideoMonitor_DeleteZxMonitor_Handler,
		},
		{
			MethodName: "SwitchZxMonitorQuality",
			Handler:    _VideoMonitor_SwitchZxMonitorQuality_Handler,
		},
		{
			MethodName: "ListMonitorDevice",
			Handler:    _VideoMonitor_ListMonitorDevice_Handler,
		},
		{
			MethodName: "CreateMonitorDevice",
			Handler:    _VideoMonitor_CreateMonitorDevice_Handler,
		},
		{
			MethodName: "UpdateMonitorDevice",
			Handler:    _VideoMonitor_UpdateMonitorDevice_Handler,
		},
		{
			MethodName: "DeleteMonitorDevice",
			Handler:    _VideoMonitor_DeleteMonitorDevice_Handler,
		},
		{
			MethodName: "GetMonitorDeviceStatus",
			Handler:    _VideoMonitor_GetMonitorDeviceStatus_Handler,
		},
		{
			MethodName: "GetMonitorDevice",
			Handler:    _VideoMonitor_GetMonitorDevice_Handler,
		},
		{
			MethodName: "CreateMonitorCamera",
			Handler:    _VideoMonitor_CreateMonitorCamera_Handler,
		},
		{
			MethodName: "UpdateMonitorCamera",
			Handler:    _VideoMonitor_UpdateMonitorCamera_Handler,
		},
		{
			MethodName: "DeleteMonitorCamera",
			Handler:    _VideoMonitor_DeleteMonitorCamera_Handler,
		},
		{
			MethodName: "GetClassCamera",
			Handler:    _VideoMonitor_GetClassCamera_Handler,
		},
		{
			MethodName: "UpdateClassCamera",
			Handler:    _VideoMonitor_UpdateClassCamera_Handler,
		},
		{
			MethodName: "ClearClassCamera",
			Handler:    _VideoMonitor_ClearClassCamera_Handler,
		},
		{
			MethodName: "GetStaffCamera",
			Handler:    _VideoMonitor_GetStaffCamera_Handler,
		},
		{
			MethodName: "UpdateStaffCamera",
			Handler:    _VideoMonitor_UpdateStaffCamera_Handler,
		},
		{
			MethodName: "GetClassAccessTime",
			Handler:    _VideoMonitor_GetClassAccessTime_Handler,
		},
		{
			MethodName: "UpdateClassAccessTime",
			Handler:    _VideoMonitor_UpdateClassAccessTime_Handler,
		},
		{
			MethodName: "GetClassIdsByCameraName",
			Handler:    _VideoMonitor_GetClassIdsByCameraName_Handler,
		},
		{
			MethodName: "FindClassCamera",
			Handler:    _VideoMonitor_FindClassCamera_Handler,
		},
		{
			MethodName: "FindStaffCamera",
			Handler:    _VideoMonitor_FindStaffCamera_Handler,
		},
		{
			MethodName: "GetMonitorCameraConfig",
			Handler:    _VideoMonitor_GetMonitorCameraConfig_Handler,
		},
		{
			MethodName: "ListMonitorCamera",
			Handler:    _VideoMonitor_ListMonitorCamera_Handler,
		},
		{
			MethodName: "ListMonitorCameraOnlineStatus",
			Handler:    _VideoMonitor_ListMonitorCameraOnlineStatus_Handler,
		},
		{
			MethodName: "ListClassCameraAccessTime",
			Handler:    _VideoMonitor_ListClassCameraAccessTime_Handler,
		},
		{
			MethodName: "GetWatchUser",
			Handler:    _VideoMonitor_GetWatchUser_Handler,
		},
		{
			MethodName: "ReportWatchRecord",
			Handler:    _VideoMonitor_ReportWatchRecord_Handler,
		},
		{
			MethodName: "StatsTodayWatch",
			Handler:    _VideoMonitor_StatsTodayWatch_Handler,
		},
		{
			MethodName: "ListWatchRecord",
			Handler:    _VideoMonitor_ListWatchRecord_Handler,
		},
		{
			MethodName: "StatsStudentWatch",
			Handler:    _VideoMonitor_StatsStudentWatch_Handler,
		},
		{
			MethodName: "StatsParentWatch",
			Handler:    _VideoMonitor_StatsParentWatch_Handler,
		},
		{
			MethodName: "GetChannelHistoryPlayback",
			Handler:    _VideoMonitor_GetChannelHistoryPlayback_Handler,
		},
		{
			MethodName: "GetParentPaybackUrl",
			Handler:    _VideoMonitor_GetParentPaybackUrl_Handler,
		},
		{
			MethodName: "QiniuToYsy",
			Handler:    _VideoMonitor_QiniuToYsy_Handler,
		},
		{
			MethodName: "YsyDeviceInfoExport",
			Handler:    _VideoMonitor_YsyDeviceInfoExport_Handler,
		},
		{
			MethodName: "GetAuthorizedInstClassCamera",
			Handler:    _VideoMonitor_GetAuthorizedInstClassCamera_Handler,
		},
		{
			MethodName: "SetLiveModel",
			Handler:    _VideoMonitor_SetLiveModel_Handler,
		},
		{
			MethodName: "QiniuBack",
			Handler:    _VideoMonitor_QiniuBack_Handler,
		},
		{
			MethodName: "CronCloseChannel",
			Handler:    _VideoMonitor_CronCloseChannel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/monitor/v1/monitor.proto",
}
