// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.19.1
// source: api/monitor/v1/video_integration.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 同步学校信息到掌心宝贝云监控平台请求参数
type SyncToAncdaReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *SyncToAncdaReq) Reset() {
	*x = SyncToAncdaReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncToAncdaReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncToAncdaReq) ProtoMessage() {}

func (x *SyncToAncdaReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncToAncdaReq.ProtoReflect.Descriptor instead.
func (*SyncToAncdaReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{0}
}

func (x *SyncToAncdaReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 同步学校信息到掌心宝贝云监控平台返回参数
type SyncToAncdaRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncToAncdaRsp) Reset() {
	*x = SyncToAncdaRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncToAncdaRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncToAncdaRsp) ProtoMessage() {}

func (x *SyncToAncdaRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncToAncdaRsp.ProtoReflect.Descriptor instead.
func (*SyncToAncdaRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{1}
}

// 注册设备请求参数
type RegisterDeviceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 设备序列号
	SerialNo string `protobuf:"bytes,2,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 设备类型 (1：IPC摄像头，2：NVR)
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	// 通道数
	ChannelNum int32 `protobuf:"varint,4,opt,name=channel_num,json=channelNum,proto3" json:"channel_num,omitempty"`
	// 接入平台（1：Ehome（占位，EHome不需要注册）， 2：国标）
	Platform int32 `protobuf:"varint,5,opt,name=platform,proto3" json:"platform,omitempty"`
}

func (x *RegisterDeviceReq) Reset() {
	*x = RegisterDeviceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterDeviceReq) ProtoMessage() {}

func (x *RegisterDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterDeviceReq.ProtoReflect.Descriptor instead.
func (*RegisterDeviceReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{2}
}

func (x *RegisterDeviceReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RegisterDeviceReq) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *RegisterDeviceReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *RegisterDeviceReq) GetChannelNum() int32 {
	if x != nil {
		return x.ChannelNum
	}
	return 0
}

func (x *RegisterDeviceReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

// 注册设备返回参数
type RegisterDeviceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RegisterDeviceRsp) Reset() {
	*x = RegisterDeviceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterDeviceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterDeviceRsp) ProtoMessage() {}

func (x *RegisterDeviceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterDeviceRsp.ProtoReflect.Descriptor instead.
func (*RegisterDeviceRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{3}
}

// 解绑设备请求参数
type UnRegisterDeviceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备序列号
	SerialNo string `protobuf:"bytes,1,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 接入平台（1：Ehome（占位，EHome不需要解绑）， 2：国标）
	Platform int32 `protobuf:"varint,2,opt,name=platform,proto3" json:"platform,omitempty"`
}

func (x *UnRegisterDeviceReq) Reset() {
	*x = UnRegisterDeviceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnRegisterDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnRegisterDeviceReq) ProtoMessage() {}

func (x *UnRegisterDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnRegisterDeviceReq.ProtoReflect.Descriptor instead.
func (*UnRegisterDeviceReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{4}
}

func (x *UnRegisterDeviceReq) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *UnRegisterDeviceReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

// 解绑设备返回参数
type UnRegisterDeviceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnRegisterDeviceRsp) Reset() {
	*x = UnRegisterDeviceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnRegisterDeviceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnRegisterDeviceRsp) ProtoMessage() {}

func (x *UnRegisterDeviceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnRegisterDeviceRsp.ProtoReflect.Descriptor instead.
func (*UnRegisterDeviceRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{5}
}

//------->>>>>>>------播放地址------<<<<<<<&&&&>>>>>>---MaWei@2023-07-28 08:49----<<<<<<----//
type PlayReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构Id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 用户ID
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户角色 1老师  3家长
	UserRole int32 `protobuf:"varint,3,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 学生
	StudentId int64 `protobuf:"varint,4,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// liveid
	LiveId string `protobuf:"bytes,5,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// ip
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *PlayReq) Reset() {
	*x = PlayReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayReq) ProtoMessage() {}

func (x *PlayReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayReq.ProtoReflect.Descriptor instead.
func (*PlayReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{6}
}

func (x *PlayReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *PlayReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PlayReq) GetUserRole() int32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *PlayReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *PlayReq) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *PlayReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type PlayRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 播放地址
	RtmpUrl string `protobuf:"bytes,1,opt,name=rtmp_url,json=rtmpUrl,proto3" json:"rtmp_url,omitempty"`
	// liveid
	LiveId string `protobuf:"bytes,2,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 在线状态
	OnlineStatus int32 `protobuf:"varint,3,opt,name=online_status,json=onlineStatus,proto3" json:"online_status,omitempty"`
	// 服务时间
	ServiceTime int64 `protobuf:"varint,4,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
}

func (x *PlayRsp) Reset() {
	*x = PlayRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayRsp) ProtoMessage() {}

func (x *PlayRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayRsp.ProtoReflect.Descriptor instead.
func (*PlayRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{7}
}

func (x *PlayRsp) GetRtmpUrl() string {
	if x != nil {
		return x.RtmpUrl
	}
	return ""
}

func (x *PlayRsp) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *PlayRsp) GetOnlineStatus() int32 {
	if x != nil {
		return x.OnlineStatus
	}
	return 0
}

func (x *PlayRsp) GetServiceTime() int64 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

//------->>>>>>>------进入播放------<<<<<<<&&&&>>>>>>---MaWei@2023-07-28 20:36----<<<<<<----//
type ReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构Id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 用户ID
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户角色 1老师  3家长
	UserRole int32 `protobuf:"varint,3,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 学生
	StudentId int64 `protobuf:"varint,4,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// liveid
	LiveId string `protobuf:"bytes,5,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 上报类型（1：进入房间，2：退出房间）
	ReportType int32 `protobuf:"varint,6,opt,name=report_type,json=reportType,proto3" json:"report_type,omitempty"`
	// 注册平台
	Platform int32 `protobuf:"varint,7,opt,name=platform,proto3" json:"platform,omitempty"`
	// 视频直播快照
	SnapshotUrl string `protobuf:"bytes,8,opt,name=snapshot_url,json=snapshotUrl,proto3" json:"snapshot_url,omitempty"`
}

func (x *ReportReq) Reset() {
	*x = ReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportReq) ProtoMessage() {}

func (x *ReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportReq.ProtoReflect.Descriptor instead.
func (*ReportReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{8}
}

func (x *ReportReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ReportReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReportReq) GetUserRole() int32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *ReportReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *ReportReq) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *ReportReq) GetReportType() int32 {
	if x != nil {
		return x.ReportType
	}
	return 0
}

func (x *ReportReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *ReportReq) GetSnapshotUrl() string {
	if x != nil {
		return x.SnapshotUrl
	}
	return ""
}

//------->>>>>>>------七牛回调------<<<<<<<&&&&>>>>>>---MaWei@2023-07-28 08:49----<<<<<<----//
type QnCallBackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 类型
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// 空间id
	NsId string `protobuf:"bytes,2,opt,name=ns_id,json=nsId,proto3" json:"ns_id,omitempty"`
	// 国标id
	GbId string `protobuf:"bytes,3,opt,name=gb_id,json=gbId,proto3" json:"gb_id,omitempty"`
	// 频道id
	ChGbId string `protobuf:"bytes,4,opt,name=ch_gb_id,json=chGbId,proto3" json:"ch_gb_id,omitempty"`
	// 设备状态
	DeviceState string `protobuf:"bytes,5,opt,name=device_state,json=deviceState,proto3" json:"device_state,omitempty"`
	// 时间
	TimeSec int64 `protobuf:"varint,6,opt,name=time_sec,json=timeSec,proto3" json:"time_sec,omitempty"`
	// 请求id
	ReqId string `protobuf:"bytes,7,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// 流id
	StreamId string `protobuf:"bytes,8,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	// 流状态
	StreamStatus int32 `protobuf:"varint,9,opt,name=stream_status,json=streamStatus,proto3" json:"stream_status,omitempty"`
	// code
	Code int32 `protobuf:"varint,10,opt,name=code,proto3" json:"code,omitempty"`
	// 截图
	SnapItems []*SnapItems `protobuf:"bytes,11,rep,name=snap_items,json=snapItems,proto3" json:"snap_items,omitempty"`
	// 错误
	Errmsg string `protobuf:"bytes,12,opt,name=errmsg,proto3" json:"errmsg,omitempty"`
	// 描述
	Desc string `protobuf:"bytes,13,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (x *QnCallBackReq) Reset() {
	*x = QnCallBackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QnCallBackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QnCallBackReq) ProtoMessage() {}

func (x *QnCallBackReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QnCallBackReq.ProtoReflect.Descriptor instead.
func (*QnCallBackReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{9}
}

func (x *QnCallBackReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *QnCallBackReq) GetNsId() string {
	if x != nil {
		return x.NsId
	}
	return ""
}

func (x *QnCallBackReq) GetGbId() string {
	if x != nil {
		return x.GbId
	}
	return ""
}

func (x *QnCallBackReq) GetChGbId() string {
	if x != nil {
		return x.ChGbId
	}
	return ""
}

func (x *QnCallBackReq) GetDeviceState() string {
	if x != nil {
		return x.DeviceState
	}
	return ""
}

func (x *QnCallBackReq) GetTimeSec() int64 {
	if x != nil {
		return x.TimeSec
	}
	return 0
}

func (x *QnCallBackReq) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *QnCallBackReq) GetStreamId() string {
	if x != nil {
		return x.StreamId
	}
	return ""
}

func (x *QnCallBackReq) GetStreamStatus() int32 {
	if x != nil {
		return x.StreamStatus
	}
	return 0
}

func (x *QnCallBackReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *QnCallBackReq) GetSnapItems() []*SnapItems {
	if x != nil {
		return x.SnapItems
	}
	return nil
}

func (x *QnCallBackReq) GetErrmsg() string {
	if x != nil {
		return x.Errmsg
	}
	return ""
}

func (x *QnCallBackReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type SnapItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 错误
	Error string `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	// 截图
	Fname string `protobuf:"bytes,3,opt,name=fname,proto3" json:"fname,omitempty"`
}

func (x *SnapItems) Reset() {
	*x = SnapItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_video_integration_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapItems) ProtoMessage() {}

func (x *SnapItems) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_video_integration_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapItems.ProtoReflect.Descriptor instead.
func (*SnapItems) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_video_integration_proto_rawDescGZIP(), []int{10}
}

func (x *SnapItems) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SnapItems) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *SnapItems) GetFname() string {
	if x != nil {
		return x.Fname
	}
	return ""
}

var File_api_monitor_v1_video_integration_proto protoreflect.FileDescriptor

var file_api_monitor_v1_video_integration_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x32,
	0x0a, 0x0e, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x6f, 0x41, 0x6e, 0x63, 0x64, 0x61, 0x52, 0x65, 0x71,
	0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74,
	0x49, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x6f, 0x41, 0x6e, 0x63, 0x64,
	0x61, 0x52, 0x73, 0x70, 0x22, 0x95, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x75, 0x6d,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x13, 0x0a, 0x11,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73,
	0x70, 0x22, 0x4e, 0x0a, 0x13, 0x55, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x22, 0x15, 0x0a, 0x13, 0x55, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0xd1, 0x01, 0x0a, 0x07, 0x50, 0x6c, 0x61,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x1a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x52, 0x6f,
	0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x07, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x69, 0x76,
	0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x02, 0x69, 0x70, 0x22, 0x85, 0x01, 0x0a,
	0x07, 0x50, 0x6c, 0x61, 0x79, 0x52, 0x73, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x74, 0x6d, 0x70,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x74, 0x6d, 0x70,
	0x55, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x9a, 0x02, 0x0a, 0x09, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x06, 0x69, 0x6e,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72,
	0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06,
	0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x07, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x55, 0x72,
	0x6c, 0x22, 0xf8, 0x02, 0x0a, 0x0d, 0x51, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x6e, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x13, 0x0a, 0x05,
	0x67, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x62, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x08, 0x63, 0x68, 0x5f, 0x67, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x47, 0x62, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x73, 0x6e, 0x61, 0x70, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6e, 0x61, 0x70,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x09, 0x73, 0x6e, 0x61, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6d, 0x73, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x65, 0x72, 0x72, 0x6d, 0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x4b, 0x0a, 0x09,
	0x53, 0x6e, 0x61, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x66, 0x6e, 0x61, 0x6d, 0x65, 0x32, 0xe5, 0x03, 0x0a, 0x10, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x58,
	0x0a, 0x0e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5e, 0x0a, 0x10, 0x55, 0x6e, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x6e,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x6e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3a, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x79,
	0x12, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x3d, 0x0a, 0x06, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x19,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x10, 0x51, 0x69, 0x6e, 0x69, 0x75, 0x51, 0x76, 0x73, 0x43,
	0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x42,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x4f, 0x0a, 0x0b, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x6f, 0x41, 0x6e, 0x63, 0x64, 0x61, 0x12,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x6f, 0x41, 0x6e, 0x63, 0x64, 0x61, 0x52, 0x65, 0x71, 0x1a,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x6f, 0x41, 0x6e, 0x63, 0x64, 0x61, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x42, 0x13, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_monitor_v1_video_integration_proto_rawDescOnce sync.Once
	file_api_monitor_v1_video_integration_proto_rawDescData = file_api_monitor_v1_video_integration_proto_rawDesc
)

func file_api_monitor_v1_video_integration_proto_rawDescGZIP() []byte {
	file_api_monitor_v1_video_integration_proto_rawDescOnce.Do(func() {
		file_api_monitor_v1_video_integration_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_monitor_v1_video_integration_proto_rawDescData)
	})
	return file_api_monitor_v1_video_integration_proto_rawDescData
}

var file_api_monitor_v1_video_integration_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_monitor_v1_video_integration_proto_goTypes = []interface{}{
	(*SyncToAncdaReq)(nil),      // 0: api.monitor.v1.SyncToAncdaReq
	(*SyncToAncdaRsp)(nil),      // 1: api.monitor.v1.SyncToAncdaRsp
	(*RegisterDeviceReq)(nil),   // 2: api.monitor.v1.RegisterDeviceReq
	(*RegisterDeviceRsp)(nil),   // 3: api.monitor.v1.RegisterDeviceRsp
	(*UnRegisterDeviceReq)(nil), // 4: api.monitor.v1.UnRegisterDeviceReq
	(*UnRegisterDeviceRsp)(nil), // 5: api.monitor.v1.UnRegisterDeviceRsp
	(*PlayReq)(nil),             // 6: api.monitor.v1.PlayReq
	(*PlayRsp)(nil),             // 7: api.monitor.v1.PlayRsp
	(*ReportReq)(nil),           // 8: api.monitor.v1.ReportReq
	(*QnCallBackReq)(nil),       // 9: api.monitor.v1.QnCallBackReq
	(*SnapItems)(nil),           // 10: api.monitor.v1.SnapItems
	(*emptypb.Empty)(nil),       // 11: google.protobuf.Empty
}
var file_api_monitor_v1_video_integration_proto_depIdxs = []int32{
	10, // 0: api.monitor.v1.QnCallBackReq.snap_items:type_name -> api.monitor.v1.SnapItems
	2,  // 1: api.monitor.v1.VideoIntegration.RegisterDevice:input_type -> api.monitor.v1.RegisterDeviceReq
	4,  // 2: api.monitor.v1.VideoIntegration.UnRegisterDevice:input_type -> api.monitor.v1.UnRegisterDeviceReq
	6,  // 3: api.monitor.v1.VideoIntegration.Play:input_type -> api.monitor.v1.PlayReq
	8,  // 4: api.monitor.v1.VideoIntegration.Report:input_type -> api.monitor.v1.ReportReq
	9,  // 5: api.monitor.v1.VideoIntegration.QiniuQvsCallBack:input_type -> api.monitor.v1.QnCallBackReq
	0,  // 6: api.monitor.v1.VideoIntegration.SyncToAncda:input_type -> api.monitor.v1.SyncToAncdaReq
	3,  // 7: api.monitor.v1.VideoIntegration.RegisterDevice:output_type -> api.monitor.v1.RegisterDeviceRsp
	5,  // 8: api.monitor.v1.VideoIntegration.UnRegisterDevice:output_type -> api.monitor.v1.UnRegisterDeviceRsp
	7,  // 9: api.monitor.v1.VideoIntegration.Play:output_type -> api.monitor.v1.PlayRsp
	11, // 10: api.monitor.v1.VideoIntegration.Report:output_type -> google.protobuf.Empty
	11, // 11: api.monitor.v1.VideoIntegration.QiniuQvsCallBack:output_type -> google.protobuf.Empty
	1,  // 12: api.monitor.v1.VideoIntegration.SyncToAncda:output_type -> api.monitor.v1.SyncToAncdaRsp
	7,  // [7:13] is the sub-list for method output_type
	1,  // [1:7] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_api_monitor_v1_video_integration_proto_init() }
func file_api_monitor_v1_video_integration_proto_init() {
	if File_api_monitor_v1_video_integration_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_monitor_v1_video_integration_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncToAncdaReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncToAncdaRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterDeviceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterDeviceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnRegisterDeviceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnRegisterDeviceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QnCallBackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_video_integration_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_monitor_v1_video_integration_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_monitor_v1_video_integration_proto_goTypes,
		DependencyIndexes: file_api_monitor_v1_video_integration_proto_depIdxs,
		MessageInfos:      file_api_monitor_v1_video_integration_proto_msgTypes,
	}.Build()
	File_api_monitor_v1_video_integration_proto = out.File
	file_api_monitor_v1_video_integration_proto_rawDesc = nil
	file_api_monitor_v1_video_integration_proto_goTypes = nil
	file_api_monitor_v1_video_integration_proto_depIdxs = nil
}
