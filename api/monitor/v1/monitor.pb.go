// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v3.19.0
// source: api/monitor/v1/monitor.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetAuthorizedInstClassCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *GetAuthorizedInstClassCameraReq) Reset() {
	*x = GetAuthorizedInstClassCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthorizedInstClassCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthorizedInstClassCameraReq) ProtoMessage() {}

func (x *GetAuthorizedInstClassCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthorizedInstClassCameraReq.ProtoReflect.Descriptor instead.
func (*GetAuthorizedInstClassCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{0}
}

func (x *GetAuthorizedInstClassCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

type GetAuthorizedInstClassCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否需要校验绑定摄像头数量
	NeedVerifyCount bool `protobuf:"varint,1,opt,name=need_verify_count,json=needVerifyCount,proto3" json:"need_verify_count,omitempty"`
	// 可授权摄像头数量
	AuthorizableCount int32 `protobuf:"varint,2,opt,name=authorizable_count,json=authorizableCount,proto3" json:"authorizable_count,omitempty"`
	// 已授权摄像头列表
	AuthorizedCameraList []*AuthorizedCameraInfo `protobuf:"bytes,3,rep,name=authorized_camera_list,json=authorizedCameraList,proto3" json:"authorized_camera_list,omitempty"`
}

func (x *GetAuthorizedInstClassCameraRsp) Reset() {
	*x = GetAuthorizedInstClassCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthorizedInstClassCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthorizedInstClassCameraRsp) ProtoMessage() {}

func (x *GetAuthorizedInstClassCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthorizedInstClassCameraRsp.ProtoReflect.Descriptor instead.
func (*GetAuthorizedInstClassCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{1}
}

func (x *GetAuthorizedInstClassCameraRsp) GetNeedVerifyCount() bool {
	if x != nil {
		return x.NeedVerifyCount
	}
	return false
}

func (x *GetAuthorizedInstClassCameraRsp) GetAuthorizableCount() int32 {
	if x != nil {
		return x.AuthorizableCount
	}
	return 0
}

func (x *GetAuthorizedInstClassCameraRsp) GetAuthorizedCameraList() []*AuthorizedCameraInfo {
	if x != nil {
		return x.AuthorizedCameraList
	}
	return nil
}

// 切换掌心宝贝摄像头画质请求参数
type SwitchZxMonitorQualityReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备唯一标志
	MonitorId string `protobuf:"bytes,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	// 画质（1：标清，2：高清）
	Quality int32 `protobuf:"varint,2,opt,name=quality,proto3" json:"quality,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，3：家长端）
	Channel uint32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,6,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *SwitchZxMonitorQualityReq) Reset() {
	*x = SwitchZxMonitorQualityReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchZxMonitorQualityReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchZxMonitorQualityReq) ProtoMessage() {}

func (x *SwitchZxMonitorQualityReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchZxMonitorQualityReq.ProtoReflect.Descriptor instead.
func (*SwitchZxMonitorQualityReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{2}
}

func (x *SwitchZxMonitorQualityReq) GetMonitorId() string {
	if x != nil {
		return x.MonitorId
	}
	return ""
}

func (x *SwitchZxMonitorQualityReq) GetQuality() int32 {
	if x != nil {
		return x.Quality
	}
	return 0
}

func (x *SwitchZxMonitorQualityReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *SwitchZxMonitorQualityReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *SwitchZxMonitorQualityReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *SwitchZxMonitorQualityReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *SwitchZxMonitorQualityReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 切换掌心宝贝摄像头画质请求参数
type SwitchZxMonitorQualityRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SwitchZxMonitorQualityRsp) Reset() {
	*x = SwitchZxMonitorQualityRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchZxMonitorQualityRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchZxMonitorQualityRsp) ProtoMessage() {}

func (x *SwitchZxMonitorQualityRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchZxMonitorQualityRsp.ProtoReflect.Descriptor instead.
func (*SwitchZxMonitorQualityRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{3}
}

// 删除掌心宝贝摄像头请求参数
type DeleteZxMonitorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 监控点id
	MonitorId string `protobuf:"bytes,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，3：家长端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *DeleteZxMonitorReq) Reset() {
	*x = DeleteZxMonitorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteZxMonitorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteZxMonitorReq) ProtoMessage() {}

func (x *DeleteZxMonitorReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteZxMonitorReq.ProtoReflect.Descriptor instead.
func (*DeleteZxMonitorReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteZxMonitorReq) GetMonitorId() string {
	if x != nil {
		return x.MonitorId
	}
	return ""
}

func (x *DeleteZxMonitorReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *DeleteZxMonitorReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *DeleteZxMonitorReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *DeleteZxMonitorReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *DeleteZxMonitorReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 删除掌心宝贝摄像头返回参数
type DeleteZxMonitorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteZxMonitorRsp) Reset() {
	*x = DeleteZxMonitorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteZxMonitorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteZxMonitorRsp) ProtoMessage() {}

func (x *DeleteZxMonitorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteZxMonitorRsp.ProtoReflect.Descriptor instead.
func (*DeleteZxMonitorRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{5}
}

// 更新掌心宝贝摄像头信息请求参数
type UpdateZxMonitorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 监控点id
	MonitorId string `protobuf:"bytes,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	// 摄像头名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 位置
	Location string `protobuf:"bytes,3,opt,name=location,proto3" json:"location,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，3：家长端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateZxMonitorReq) Reset() {
	*x = UpdateZxMonitorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateZxMonitorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateZxMonitorReq) ProtoMessage() {}

func (x *UpdateZxMonitorReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateZxMonitorReq.ProtoReflect.Descriptor instead.
func (*UpdateZxMonitorReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateZxMonitorReq) GetMonitorId() string {
	if x != nil {
		return x.MonitorId
	}
	return ""
}

func (x *UpdateZxMonitorReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateZxMonitorReq) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *UpdateZxMonitorReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateZxMonitorReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateZxMonitorReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateZxMonitorReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateZxMonitorReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 更新掌心宝贝摄像头信息返回参数
type UpdateZxMonitorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateZxMonitorRsp) Reset() {
	*x = UpdateZxMonitorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateZxMonitorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateZxMonitorRsp) ProtoMessage() {}

func (x *UpdateZxMonitorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateZxMonitorRsp.ProtoReflect.Descriptor instead.
func (*UpdateZxMonitorRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{7}
}

// 合并掌心宝贝摄像头节点请求参数
type MergeZxMonitorNodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，3：家长端）
	Channel uint32 `protobuf:"varint,2,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,4,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *MergeZxMonitorNodeReq) Reset() {
	*x = MergeZxMonitorNodeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeZxMonitorNodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeZxMonitorNodeReq) ProtoMessage() {}

func (x *MergeZxMonitorNodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeZxMonitorNodeReq.ProtoReflect.Descriptor instead.
func (*MergeZxMonitorNodeReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{8}
}

func (x *MergeZxMonitorNodeReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *MergeZxMonitorNodeReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *MergeZxMonitorNodeReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *MergeZxMonitorNodeReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *MergeZxMonitorNodeReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 合并掌心宝贝摄像头节点返回参数
type MergeZxMonitorNodeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MergeZxMonitorNodeRsp) Reset() {
	*x = MergeZxMonitorNodeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeZxMonitorNodeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeZxMonitorNodeRsp) ProtoMessage() {}

func (x *MergeZxMonitorNodeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeZxMonitorNodeRsp.ProtoReflect.Descriptor instead.
func (*MergeZxMonitorNodeRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{9}
}

// 获取掌心宝贝品牌摄像头列表请求参数
type ListZxMonitorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 页数
	Page int32 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,4,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，3：家长端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *ListZxMonitorReq) Reset() {
	*x = ListZxMonitorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListZxMonitorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListZxMonitorReq) ProtoMessage() {}

func (x *ListZxMonitorReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListZxMonitorReq.ProtoReflect.Descriptor instead.
func (*ListZxMonitorReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{10}
}

func (x *ListZxMonitorReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListZxMonitorReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListZxMonitorReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListZxMonitorReq) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListZxMonitorReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ListZxMonitorReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *ListZxMonitorReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ListZxMonitorReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 获取掌心宝贝品牌摄像头列表返回参数
type ListZxMonitorRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 设备列表
	List []*ZxMonitor `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListZxMonitorRsp) Reset() {
	*x = ListZxMonitorRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListZxMonitorRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListZxMonitorRsp) ProtoMessage() {}

func (x *ListZxMonitorRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListZxMonitorRsp.ProtoReflect.Descriptor instead.
func (*ListZxMonitorRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{11}
}

func (x *ListZxMonitorRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListZxMonitorRsp) GetList() []*ZxMonitor {
	if x != nil {
		return x.List
	}
	return nil
}

// 掌心宝贝摄像头信息
type ZxMonitor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 监控id
	MonitorId string `protobuf:"bytes,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	// 归属节点id
	NodeId string `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	// 原归属节点id
	OldNodeId string `protobuf:"bytes,3,opt,name=old_node_id,json=oldNodeId,proto3" json:"old_node_id,omitempty"`
	// 摄像头名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 位置
	Location string `protobuf:"bytes,5,opt,name=location,proto3" json:"location,omitempty"`
	// 画质（1：标清，2：高清）
	Quality int32 `protobuf:"varint,6,opt,name=quality,proto3" json:"quality,omitempty"`
	// 设备唯一标志
	MonitorKey string `protobuf:"bytes,7,opt,name=monitor_key,json=monitorKey,proto3" json:"monitor_key,omitempty"`
	// 创建时间
	CreateTime int32 `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *ZxMonitor) Reset() {
	*x = ZxMonitor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZxMonitor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZxMonitor) ProtoMessage() {}

func (x *ZxMonitor) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZxMonitor.ProtoReflect.Descriptor instead.
func (*ZxMonitor) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{12}
}

func (x *ZxMonitor) GetMonitorId() string {
	if x != nil {
		return x.MonitorId
	}
	return ""
}

func (x *ZxMonitor) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *ZxMonitor) GetOldNodeId() string {
	if x != nil {
		return x.OldNodeId
	}
	return ""
}

func (x *ZxMonitor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ZxMonitor) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ZxMonitor) GetQuality() int32 {
	if x != nil {
		return x.Quality
	}
	return 0
}

func (x *ZxMonitor) GetMonitorKey() string {
	if x != nil {
		return x.MonitorKey
	}
	return ""
}

func (x *ZxMonitor) GetCreateTime() int32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

// 获取家长回放url请求参数
type GetParentPaybackUrlReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 直播房间id
	LiveId string `protobuf:"bytes,1,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 开始时间戳，单位：秒
	StartTime int32 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间戳，单位：秒
	EndTime int32 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *GetParentPaybackUrlReq) Reset() {
	*x = GetParentPaybackUrlReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetParentPaybackUrlReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetParentPaybackUrlReq) ProtoMessage() {}

func (x *GetParentPaybackUrlReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetParentPaybackUrlReq.ProtoReflect.Descriptor instead.
func (*GetParentPaybackUrlReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{13}
}

func (x *GetParentPaybackUrlReq) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *GetParentPaybackUrlReq) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GetParentPaybackUrlReq) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GetParentPaybackUrlReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 获取家长回放url返回参数
type GetParentPaybackUrlRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 回放url
	RtmpUrl string `protobuf:"bytes,1,opt,name=rtmp_url,json=rtmpUrl,proto3" json:"rtmp_url,omitempty"`
}

func (x *GetParentPaybackUrlRsp) Reset() {
	*x = GetParentPaybackUrlRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetParentPaybackUrlRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetParentPaybackUrlRsp) ProtoMessage() {}

func (x *GetParentPaybackUrlRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetParentPaybackUrlRsp.ProtoReflect.Descriptor instead.
func (*GetParentPaybackUrlRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{14}
}

func (x *GetParentPaybackUrlRsp) GetRtmpUrl() string {
	if x != nil {
		return x.RtmpUrl
	}
	return ""
}

// 获取通道历史回放列表请求参数
type GetChannelHistoryPlaybackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 直播房间id
	LiveId string `protobuf:"bytes,1,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 开始时间戳，单位：秒
	StartTime int32 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间戳，单位：秒
	EndTime int32 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *GetChannelHistoryPlaybackReq) Reset() {
	*x = GetChannelHistoryPlaybackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelHistoryPlaybackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelHistoryPlaybackReq) ProtoMessage() {}

func (x *GetChannelHistoryPlaybackReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelHistoryPlaybackReq.ProtoReflect.Descriptor instead.
func (*GetChannelHistoryPlaybackReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{15}
}

func (x *GetChannelHistoryPlaybackReq) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *GetChannelHistoryPlaybackReq) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GetChannelHistoryPlaybackReq) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GetChannelHistoryPlaybackReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 获取通道历史回放列表返回参数
type GetChannelHistoryPlaybackRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 回放列表
	List []*PlaybackInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetChannelHistoryPlaybackRsp) Reset() {
	*x = GetChannelHistoryPlaybackRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelHistoryPlaybackRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelHistoryPlaybackRsp) ProtoMessage() {}

func (x *GetChannelHistoryPlaybackRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelHistoryPlaybackRsp.ProtoReflect.Descriptor instead.
func (*GetChannelHistoryPlaybackRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{16}
}

func (x *GetChannelHistoryPlaybackRsp) GetList() []*PlaybackInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 回放信息
type PlaybackInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rtmp url
	RtmpUrl string `protobuf:"bytes,1,opt,name=rtmp_url,json=rtmpUrl,proto3" json:"rtmp_url,omitempty"`
	// 起始时间
	StartTime int32 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime int32 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *PlaybackInfo) Reset() {
	*x = PlaybackInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaybackInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaybackInfo) ProtoMessage() {}

func (x *PlaybackInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaybackInfo.ProtoReflect.Descriptor instead.
func (*PlaybackInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{17}
}

func (x *PlaybackInfo) GetRtmpUrl() string {
	if x != nil {
		return x.RtmpUrl
	}
	return ""
}

func (x *PlaybackInfo) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *PlaybackInfo) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

// 获取家长观看统计信息请求参数
type StatsParentWatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,2,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,3,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 起始时间戳，单位：秒
	StartTime int64 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间戳，单位：秒
	EndTime int64 `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 页码
	Page int32 `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage int32 `protobuf:"varint,7,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,8,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *StatsParentWatchReq) Reset() {
	*x = StatsParentWatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsParentWatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsParentWatchReq) ProtoMessage() {}

func (x *StatsParentWatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsParentWatchReq.ProtoReflect.Descriptor instead.
func (*StatsParentWatchReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{18}
}

func (x *StatsParentWatchReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *StatsParentWatchReq) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *StatsParentWatchReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *StatsParentWatchReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *StatsParentWatchReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *StatsParentWatchReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *StatsParentWatchReq) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *StatsParentWatchReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 获取家长观看统计信息返回参数
type StatsParentWatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 家长观看统计信息
	List []*ParentWatchStatsInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *StatsParentWatchRsp) Reset() {
	*x = StatsParentWatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsParentWatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsParentWatchRsp) ProtoMessage() {}

func (x *StatsParentWatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsParentWatchRsp.ProtoReflect.Descriptor instead.
func (*StatsParentWatchRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{19}
}

func (x *StatsParentWatchRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *StatsParentWatchRsp) GetList() []*ParentWatchStatsInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 家长观看统计信息
type ParentWatchStatsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	StudentId int64 `protobuf:"varint,1,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,2,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 家长id
	ParentId int64 `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 家长姓名
	ParentName string `protobuf:"bytes,4,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,5,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,6,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
	// 累计观看次数
	Times int32 `protobuf:"varint,7,opt,name=times,proto3" json:"times,omitempty"`
	// 累计观看时长
	Duration int32 `protobuf:"varint,8,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *ParentWatchStatsInfo) Reset() {
	*x = ParentWatchStatsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParentWatchStatsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParentWatchStatsInfo) ProtoMessage() {}

func (x *ParentWatchStatsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParentWatchStatsInfo.ProtoReflect.Descriptor instead.
func (*ParentWatchStatsInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{20}
}

func (x *ParentWatchStatsInfo) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *ParentWatchStatsInfo) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *ParentWatchStatsInfo) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *ParentWatchStatsInfo) GetParentName() string {
	if x != nil {
		return x.ParentName
	}
	return ""
}

func (x *ParentWatchStatsInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *ParentWatchStatsInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *ParentWatchStatsInfo) GetTimes() int32 {
	if x != nil {
		return x.Times
	}
	return 0
}

func (x *ParentWatchStatsInfo) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

// 获取学生观看统计信息请求参数
type StatsStudentWatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,2,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 起始时间戳，单位：秒
	StartTime int64 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间戳，单位：秒
	EndTime int64 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 页码
	Page int32 `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage int32 `protobuf:"varint,6,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,7,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *StatsStudentWatchReq) Reset() {
	*x = StatsStudentWatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsStudentWatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsStudentWatchReq) ProtoMessage() {}

func (x *StatsStudentWatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsStudentWatchReq.ProtoReflect.Descriptor instead.
func (*StatsStudentWatchReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{21}
}

func (x *StatsStudentWatchReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *StatsStudentWatchReq) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *StatsStudentWatchReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *StatsStudentWatchReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *StatsStudentWatchReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *StatsStudentWatchReq) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *StatsStudentWatchReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 获取学生观看统计信息返回参数
type StatsStudentWatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 学生观看统计信息
	List []*StudentWatchStatsInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *StatsStudentWatchRsp) Reset() {
	*x = StatsStudentWatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsStudentWatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsStudentWatchRsp) ProtoMessage() {}

func (x *StatsStudentWatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsStudentWatchRsp.ProtoReflect.Descriptor instead.
func (*StatsStudentWatchRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{22}
}

func (x *StatsStudentWatchRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *StatsStudentWatchRsp) GetList() []*StudentWatchStatsInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 学生观看统计明细
type StudentWatchStatsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	StudentId int64 `protobuf:"varint,1,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,2,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,3,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,4,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
	// 累计观看次数
	Times int32 `protobuf:"varint,5,opt,name=times,proto3" json:"times,omitempty"`
	// 观看时长
	Duration int32 `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *StudentWatchStatsInfo) Reset() {
	*x = StudentWatchStatsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StudentWatchStatsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StudentWatchStatsInfo) ProtoMessage() {}

func (x *StudentWatchStatsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StudentWatchStatsInfo.ProtoReflect.Descriptor instead.
func (*StudentWatchStatsInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{23}
}

func (x *StudentWatchStatsInfo) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *StudentWatchStatsInfo) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *StudentWatchStatsInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *StudentWatchStatsInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *StudentWatchStatsInfo) GetTimes() int32 {
	if x != nil {
		return x.Times
	}
	return 0
}

func (x *StudentWatchStatsInfo) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

// 获取观看明细记录列表请求参数
type ListWatchRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 家长id
	ParentId int64 `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,3,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 观看时间段开始时间
	StartTime int64 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 观看时间段结束时间
	EndTime int64 `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 页码
	Page int32 `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	// 每页数量
	PerPage int32 `protobuf:"varint,7,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,8,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 是否根据观看状态排序
	IsSort bool `protobuf:"varint,9,opt,name=is_sort,json=isSort,proto3" json:"is_sort,omitempty"`
}

func (x *ListWatchRecordReq) Reset() {
	*x = ListWatchRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWatchRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWatchRecordReq) ProtoMessage() {}

func (x *ListWatchRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWatchRecordReq.ProtoReflect.Descriptor instead.
func (*ListWatchRecordReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{24}
}

func (x *ListWatchRecordReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *ListWatchRecordReq) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *ListWatchRecordReq) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *ListWatchRecordReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListWatchRecordReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListWatchRecordReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListWatchRecordReq) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListWatchRecordReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListWatchRecordReq) GetIsSort() bool {
	if x != nil {
		return x.IsSort
	}
	return false
}

// 获取观看明细记录列表返回参数
type ListWatchRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 观看记录
	List []*WatchRecord `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListWatchRecordRsp) Reset() {
	*x = ListWatchRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWatchRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWatchRecordRsp) ProtoMessage() {}

func (x *ListWatchRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWatchRecordRsp.ProtoReflect.Descriptor instead.
func (*ListWatchRecordRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{25}
}

func (x *ListWatchRecordRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListWatchRecordRsp) GetList() []*WatchRecord {
	if x != nil {
		return x.List
	}
	return nil
}

// 观看明细信息
type WatchRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学生id
	StudentId int64 `protobuf:"varint,1,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 家长id
	ParentId int64 `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,3,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 家长姓名
	ParentName string `protobuf:"bytes,4,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,5,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,6,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
	// 开始观看时间戳，单位：秒
	StartTime int64 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 观看时长
	Duration int32 `protobuf:"varint,8,opt,name=duration,proto3" json:"duration,omitempty"`
	// 是否正在观看（1：否，2：是）
	Status int32 `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	// 观看的通道id
	LiveId string `protobuf:"bytes,10,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 平台（1：EHome，2：国标，3：私有协议，4：萤石云，5：萤石国标，6：掌心魔方）
	Platform int32 `protobuf:"varint,11,opt,name=platform,proto3" json:"platform,omitempty"`
	// 设备序列号
	SerialNo string `protobuf:"bytes,12,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 观看的通道号
	Channel int32 `protobuf:"varint,13,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *WatchRecord) Reset() {
	*x = WatchRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchRecord) ProtoMessage() {}

func (x *WatchRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchRecord.ProtoReflect.Descriptor instead.
func (*WatchRecord) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{26}
}

func (x *WatchRecord) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *WatchRecord) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *WatchRecord) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *WatchRecord) GetParentName() string {
	if x != nil {
		return x.ParentName
	}
	return ""
}

func (x *WatchRecord) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *WatchRecord) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *WatchRecord) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *WatchRecord) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *WatchRecord) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *WatchRecord) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *WatchRecord) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *WatchRecord) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *WatchRecord) GetChannel() int32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

// 统计今日观看信息请求参数
type StatsTodayWatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 班级id
	ClassId int64 `protobuf:"varint,2,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,3,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
}

func (x *StatsTodayWatchReq) Reset() {
	*x = StatsTodayWatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsTodayWatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsTodayWatchReq) ProtoMessage() {}

func (x *StatsTodayWatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsTodayWatchReq.ProtoReflect.Descriptor instead.
func (*StatsTodayWatchReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{27}
}

func (x *StatsTodayWatchReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *StatsTodayWatchReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *StatsTodayWatchReq) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

// 统计今日观看信息返回参数
type StatsTodayWatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 正在观看人数
	WatchingCount int64 `protobuf:"varint,1,opt,name=watching_count,json=watchingCount,proto3" json:"watching_count,omitempty"`
	// 累计观看人次
	CumulativeViews int64 `protobuf:"varint,2,opt,name=cumulative_views,json=cumulativeViews,proto3" json:"cumulative_views,omitempty"`
}

func (x *StatsTodayWatchRsp) Reset() {
	*x = StatsTodayWatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsTodayWatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsTodayWatchRsp) ProtoMessage() {}

func (x *StatsTodayWatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsTodayWatchRsp.ProtoReflect.Descriptor instead.
func (*StatsTodayWatchRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{28}
}

func (x *StatsTodayWatchRsp) GetWatchingCount() int64 {
	if x != nil {
		return x.WatchingCount
	}
	return 0
}

func (x *StatsTodayWatchRsp) GetCumulativeViews() int64 {
	if x != nil {
		return x.CumulativeViews
	}
	return 0
}

// 上报观看记录请求参数
type ReportWatchRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 上报的记录id
	RecordId string `protobuf:"bytes,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	// 直播id
	LiveId string `protobuf:"bytes,2,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 观看用户id
	UserId int64 `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 观看用户角色 3：家长 目前只统计家长观看
	UserRole int32 `protobuf:"varint,5,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 观看用户学生id
	StudentId int64 `protobuf:"varint,6,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 摄像头ID
	CameraId string `protobuf:"bytes,7,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
}

func (x *ReportWatchRecordReq) Reset() {
	*x = ReportWatchRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportWatchRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportWatchRecordReq) ProtoMessage() {}

func (x *ReportWatchRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportWatchRecordReq.ProtoReflect.Descriptor instead.
func (*ReportWatchRecordReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{29}
}

func (x *ReportWatchRecordReq) GetRecordId() string {
	if x != nil {
		return x.RecordId
	}
	return ""
}

func (x *ReportWatchRecordReq) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *ReportWatchRecordReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ReportWatchRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReportWatchRecordReq) GetUserRole() int32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *ReportWatchRecordReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *ReportWatchRecordReq) GetCameraId() string {
	if x != nil {
		return x.CameraId
	}
	return ""
}

// 上报观看记录返回参数
type ReportWatchRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 上报的记录id
	RecordId string `protobuf:"bytes,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
}

func (x *ReportWatchRecordRsp) Reset() {
	*x = ReportWatchRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportWatchRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportWatchRecordRsp) ProtoMessage() {}

func (x *ReportWatchRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportWatchRecordRsp.ProtoReflect.Descriptor instead.
func (*ReportWatchRecordRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{30}
}

func (x *ReportWatchRecordRsp) GetRecordId() string {
	if x != nil {
		return x.RecordId
	}
	return ""
}

// 获取所有监控摄像头在线状态信息请求参数
type ListMonitorCameraOnlineStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户角色（1：教职工，3：家长）
	UserRole uint32 `protobuf:"varint,2,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,4,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
}

func (x *ListMonitorCameraOnlineStatusReq) Reset() {
	*x = ListMonitorCameraOnlineStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonitorCameraOnlineStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonitorCameraOnlineStatusReq) ProtoMessage() {}

func (x *ListMonitorCameraOnlineStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonitorCameraOnlineStatusReq.ProtoReflect.Descriptor instead.
func (*ListMonitorCameraOnlineStatusReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{31}
}

func (x *ListMonitorCameraOnlineStatusReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ListMonitorCameraOnlineStatusReq) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *ListMonitorCameraOnlineStatusReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListMonitorCameraOnlineStatusReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

// 获取所有监控摄像头在线状态信息返回参数
type ListMonitorCameraOnlineStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 服务器当前时间，单位：秒
	ServerTime int32               `protobuf:"varint,1,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	Cameras    []*CameraDetailInfo `protobuf:"bytes,2,rep,name=cameras,proto3" json:"cameras,omitempty"`
}

func (x *ListMonitorCameraOnlineStatusRsp) Reset() {
	*x = ListMonitorCameraOnlineStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonitorCameraOnlineStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonitorCameraOnlineStatusRsp) ProtoMessage() {}

func (x *ListMonitorCameraOnlineStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonitorCameraOnlineStatusRsp.ProtoReflect.Descriptor instead.
func (*ListMonitorCameraOnlineStatusRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{32}
}

func (x *ListMonitorCameraOnlineStatusRsp) GetServerTime() int32 {
	if x != nil {
		return x.ServerTime
	}
	return 0
}

func (x *ListMonitorCameraOnlineStatusRsp) GetCameras() []*CameraDetailInfo {
	if x != nil {
		return x.Cameras
	}
	return nil
}

// 获取视频监控摄像头观看用户列表请求参数
type GetWatchUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 注册平台（1：EHome，2：国标，3：私有协议, 4：萤石云, 5：萤石国标, 6：掌心魔方）
	Platform int32 `protobuf:"varint,1,opt,name=platform,proto3" json:"platform,omitempty"`
	// 视频监控房间id
	LiveId string `protobuf:"bytes,2,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *GetWatchUserReq) Reset() {
	*x = GetWatchUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWatchUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWatchUserReq) ProtoMessage() {}

func (x *GetWatchUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWatchUserReq.ProtoReflect.Descriptor instead.
func (*GetWatchUserReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{33}
}

func (x *GetWatchUserReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *GetWatchUserReq) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *GetWatchUserReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

// 获取视频监控摄像头观看用户列表返回参数
type GetWatchUserRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 观看用户列表
	Users []*WatchUserInfo `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *GetWatchUserRsp) Reset() {
	*x = GetWatchUserRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWatchUserRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWatchUserRsp) ProtoMessage() {}

func (x *GetWatchUserRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWatchUserRsp.ProtoReflect.Descriptor instead.
func (*GetWatchUserRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{34}
}

func (x *GetWatchUserRsp) GetUsers() []*WatchUserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

type WatchUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 家长id
	ParentId int64 `protobuf:"varint,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 家长头像
	ParentAvatar string `protobuf:"bytes,2,opt,name=parent_avatar,json=parentAvatar,proto3" json:"parent_avatar,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,3,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 学生姓名
	StudentName string `protobuf:"bytes,4,opt,name=student_name,json=studentName,proto3" json:"student_name,omitempty"`
	// 家长与学生关系
	Relation string `protobuf:"bytes,5,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *WatchUserInfo) Reset() {
	*x = WatchUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchUserInfo) ProtoMessage() {}

func (x *WatchUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchUserInfo.ProtoReflect.Descriptor instead.
func (*WatchUserInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{35}
}

func (x *WatchUserInfo) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *WatchUserInfo) GetParentAvatar() string {
	if x != nil {
		return x.ParentAvatar
	}
	return ""
}

func (x *WatchUserInfo) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *WatchUserInfo) GetStudentName() string {
	if x != nil {
		return x.StudentName
	}
	return ""
}

func (x *WatchUserInfo) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

// 获取班级视频监控摄像头开放时间信息请求参数
type ListClassCameraAccessTimeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 班级id
	ClassIds []int64 `protobuf:"varint,2,rep,packed,name=class_ids,json=classIds,proto3" json:"class_ids,omitempty"`
}

func (x *ListClassCameraAccessTimeReq) Reset() {
	*x = ListClassCameraAccessTimeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassCameraAccessTimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassCameraAccessTimeReq) ProtoMessage() {}

func (x *ListClassCameraAccessTimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassCameraAccessTimeReq.ProtoReflect.Descriptor instead.
func (*ListClassCameraAccessTimeReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{36}
}

func (x *ListClassCameraAccessTimeReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListClassCameraAccessTimeReq) GetClassIds() []int64 {
	if x != nil {
		return x.ClassIds
	}
	return nil
}

// 获取班级视频监控摄像头开放时间信息返回参数
type ListClassCameraAccessTimeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前是否是节假日
	IsHolidays bool `protobuf:"varint,1,opt,name=is_holidays,json=isHolidays,proto3" json:"is_holidays,omitempty"`
	// 班级摄像头开放时间信息列表
	AccessList []*CameraAccessInfo `protobuf:"bytes,2,rep,name=access_list,json=accessList,proto3" json:"access_list,omitempty"`
}

func (x *ListClassCameraAccessTimeRsp) Reset() {
	*x = ListClassCameraAccessTimeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClassCameraAccessTimeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClassCameraAccessTimeRsp) ProtoMessage() {}

func (x *ListClassCameraAccessTimeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClassCameraAccessTimeRsp.ProtoReflect.Descriptor instead.
func (*ListClassCameraAccessTimeRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{37}
}

func (x *ListClassCameraAccessTimeRsp) GetIsHolidays() bool {
	if x != nil {
		return x.IsHolidays
	}
	return false
}

func (x *ListClassCameraAccessTimeRsp) GetAccessList() []*CameraAccessInfo {
	if x != nil {
		return x.AccessList
	}
	return nil
}

// 摄像头开放时间信息
type CameraAccessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 摄像头id
	CameraId string `protobuf:"bytes,2,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// 直播间id
	LiveId string `protobuf:"bytes,3,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 注册平台（1：EHome，2：国标，3：私有协议，4：萤石云，5：萤石国标，6：掌心魔方)
	Platform int32 `protobuf:"varint,4,opt,name=platform,proto3" json:"platform,omitempty"`
	// 开放时间
	AccessTimeInfo *AccessTimeInfo `protobuf:"bytes,5,opt,name=access_time_info,json=accessTimeInfo,proto3" json:"access_time_info,omitempty"`
}

func (x *CameraAccessInfo) Reset() {
	*x = CameraAccessInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraAccessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraAccessInfo) ProtoMessage() {}

func (x *CameraAccessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraAccessInfo.ProtoReflect.Descriptor instead.
func (*CameraAccessInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{38}
}

func (x *CameraAccessInfo) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *CameraAccessInfo) GetCameraId() string {
	if x != nil {
		return x.CameraId
	}
	return ""
}

func (x *CameraAccessInfo) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *CameraAccessInfo) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *CameraAccessInfo) GetAccessTimeInfo() *AccessTimeInfo {
	if x != nil {
		return x.AccessTimeInfo
	}
	return nil
}

// 获取视频监控摄像头列表请求参数
type ListMonitorCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户角色（1：教职工，3：家长）
	UserRole uint32 `protobuf:"varint,2,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,4,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
}

func (x *ListMonitorCameraReq) Reset() {
	*x = ListMonitorCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonitorCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonitorCameraReq) ProtoMessage() {}

func (x *ListMonitorCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonitorCameraReq.ProtoReflect.Descriptor instead.
func (*ListMonitorCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{39}
}

func (x *ListMonitorCameraReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ListMonitorCameraReq) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *ListMonitorCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListMonitorCameraReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

// 获取视频监控摄像头列表响应参数
type ListMonitorCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级摄像头信息列表
	Cameras []*ClassCameraInfo `protobuf:"bytes,1,rep,name=cameras,proto3" json:"cameras,omitempty"`
	// 当天是否是节假日
	IsHolidays bool `protobuf:"varint,2,opt,name=is_holidays,json=isHolidays,proto3" json:"is_holidays,omitempty"`
	// 服务器当前时间戳，单位：秒
	ServerTime int32 `protobuf:"varint,3,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
}

func (x *ListMonitorCameraRsp) Reset() {
	*x = ListMonitorCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonitorCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonitorCameraRsp) ProtoMessage() {}

func (x *ListMonitorCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonitorCameraRsp.ProtoReflect.Descriptor instead.
func (*ListMonitorCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{40}
}

func (x *ListMonitorCameraRsp) GetCameras() []*ClassCameraInfo {
	if x != nil {
		return x.Cameras
	}
	return nil
}

func (x *ListMonitorCameraRsp) GetIsHolidays() bool {
	if x != nil {
		return x.IsHolidays
	}
	return false
}

func (x *ListMonitorCameraRsp) GetServerTime() int32 {
	if x != nil {
		return x.ServerTime
	}
	return 0
}

// 班级摄像头信息
type ClassCameraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id，0：表示不在所有班级的摄像头分组
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,2,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级id
	GradeId int64 `protobuf:"varint,3,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,4,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
	// 摄像头信息
	Cameras []*CameraDetailInfo `protobuf:"bytes,5,rep,name=cameras,proto3" json:"cameras,omitempty"`
}

func (x *ClassCameraInfo) Reset() {
	*x = ClassCameraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassCameraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassCameraInfo) ProtoMessage() {}

func (x *ClassCameraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassCameraInfo.ProtoReflect.Descriptor instead.
func (*ClassCameraInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{41}
}

func (x *ClassCameraInfo) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *ClassCameraInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *ClassCameraInfo) GetGradeId() int64 {
	if x != nil {
		return x.GradeId
	}
	return 0
}

func (x *ClassCameraInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *ClassCameraInfo) GetCameras() []*CameraDetailInfo {
	if x != nil {
		return x.Cameras
	}
	return nil
}

// 摄像头信息
type CameraDetailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头id
	CameraId string `protobuf:"bytes,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// 摄像头名称
	CameraName string `protobuf:"bytes,2,opt,name=camera_name,json=cameraName,proto3" json:"camera_name,omitempty"`
	// 直播间id
	LiveId string `protobuf:"bytes,3,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 视频清晰度（1：普清，2：高清）
	LiveMode int32 `protobuf:"varint,4,opt,name=live_mode,json=liveMode,proto3" json:"live_mode,omitempty"`
	// 视频截图url
	SnapshotUrl string `protobuf:"bytes,5,opt,name=snapshot_url,json=snapshotUrl,proto3" json:"snapshot_url,omitempty"`
	// 是否在线
	Online bool `protobuf:"varint,6,opt,name=online,proto3" json:"online,omitempty"`
	// 注册平台（1：EHome，2：国标，3：私有协议，4：萤石云，5：萤石国标，6：掌心魔方）
	Platform int32 `protobuf:"varint,7,opt,name=platform,proto3" json:"platform,omitempty"`
	// 当前观看人数
	WatchNum int32 `protobuf:"varint,8,opt,name=watch_num,json=watchNum,proto3" json:"watch_num,omitempty"`
	// 摄像头开放时间信息，家长端才返回该字段，园丁端默认全天开放
	AccessTimeInfo *AccessTimeInfo `protobuf:"bytes,9,opt,name=access_time_info,json=accessTimeInfo,proto3" json:"access_time_info,omitempty"`
	// 设备序列号
	SerialNo string `protobuf:"bytes,10,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 通道号
	Channel int32 `protobuf:"varint,11,opt,name=channel,proto3" json:"channel,omitempty"`
	// 是否有回放(仅魔方 miku)
	HasPlayback int32 `protobuf:"varint,12,opt,name=has_playback,json=hasPlayback,proto3" json:"has_playback,omitempty"`
}

func (x *CameraDetailInfo) Reset() {
	*x = CameraDetailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraDetailInfo) ProtoMessage() {}

func (x *CameraDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraDetailInfo.ProtoReflect.Descriptor instead.
func (*CameraDetailInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{42}
}

func (x *CameraDetailInfo) GetCameraId() string {
	if x != nil {
		return x.CameraId
	}
	return ""
}

func (x *CameraDetailInfo) GetCameraName() string {
	if x != nil {
		return x.CameraName
	}
	return ""
}

func (x *CameraDetailInfo) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *CameraDetailInfo) GetLiveMode() int32 {
	if x != nil {
		return x.LiveMode
	}
	return 0
}

func (x *CameraDetailInfo) GetSnapshotUrl() string {
	if x != nil {
		return x.SnapshotUrl
	}
	return ""
}

func (x *CameraDetailInfo) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *CameraDetailInfo) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *CameraDetailInfo) GetWatchNum() int32 {
	if x != nil {
		return x.WatchNum
	}
	return 0
}

func (x *CameraDetailInfo) GetAccessTimeInfo() *AccessTimeInfo {
	if x != nil {
		return x.AccessTimeInfo
	}
	return nil
}

func (x *CameraDetailInfo) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *CameraDetailInfo) GetChannel() int32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *CameraDetailInfo) GetHasPlayback() int32 {
	if x != nil {
		return x.HasPlayback
	}
	return 0
}

// 获取视频监控配置信息请求参数
type GetMonitorCameraConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 用户角色（1：教职工，3：家长）
	UserRole uint32 `protobuf:"varint,3,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	// 学生id
	StudentId int64 `protobuf:"varint,4,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，3：家长端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *GetMonitorCameraConfigReq) Reset() {
	*x = GetMonitorCameraConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonitorCameraConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonitorCameraConfigReq) ProtoMessage() {}

func (x *GetMonitorCameraConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonitorCameraConfigReq.ProtoReflect.Descriptor instead.
func (*GetMonitorCameraConfigReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{43}
}

func (x *GetMonitorCameraConfigReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetMonitorCameraConfigReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetMonitorCameraConfigReq) GetUserRole() uint32 {
	if x != nil {
		return x.UserRole
	}
	return 0
}

func (x *GetMonitorCameraConfigReq) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

func (x *GetMonitorCameraConfigReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

// 获取视频监控配置信息返回参数
type GetMonitorCameraConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 心跳时间间隔，单位：秒
	HeartbeatInterval int32 `protobuf:"varint,1,opt,name=heartbeat_interval,json=heartbeatInterval,proto3" json:"heartbeat_interval,omitempty"`
	// 上报时间间隔，单位：秒
	ReportInterval int32 `protobuf:"varint,2,opt,name=report_interval,json=reportInterval,proto3" json:"report_interval,omitempty"`
	// 私有协议用户账号
	AncdaUsername string `protobuf:"bytes,3,opt,name=ancda_username,json=ancdaUsername,proto3" json:"ancda_username,omitempty"`
	// 私有协议用户密码
	AncdaPassword string `protobuf:"bytes,4,opt,name=ancda_password,json=ancdaPassword,proto3" json:"ancda_password,omitempty"`
	// 私有协议服务器地址
	AncdaServerUrl string `protobuf:"bytes,5,opt,name=ancda_server_url,json=ancdaServerUrl,proto3" json:"ancda_server_url,omitempty"`
	// 私有协议服务器端口
	AncdaServerPort int32 `protobuf:"varint,6,opt,name=ancda_server_port,json=ancdaServerPort,proto3" json:"ancda_server_port,omitempty"`
	// 萤石云token
	YsAccessToken string `protobuf:"bytes,7,opt,name=ys_access_token,json=ysAccessToken,proto3" json:"ys_access_token,omitempty"`
}

func (x *GetMonitorCameraConfigRsp) Reset() {
	*x = GetMonitorCameraConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonitorCameraConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonitorCameraConfigRsp) ProtoMessage() {}

func (x *GetMonitorCameraConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonitorCameraConfigRsp.ProtoReflect.Descriptor instead.
func (*GetMonitorCameraConfigRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{44}
}

func (x *GetMonitorCameraConfigRsp) GetHeartbeatInterval() int32 {
	if x != nil {
		return x.HeartbeatInterval
	}
	return 0
}

func (x *GetMonitorCameraConfigRsp) GetReportInterval() int32 {
	if x != nil {
		return x.ReportInterval
	}
	return 0
}

func (x *GetMonitorCameraConfigRsp) GetAncdaUsername() string {
	if x != nil {
		return x.AncdaUsername
	}
	return ""
}

func (x *GetMonitorCameraConfigRsp) GetAncdaPassword() string {
	if x != nil {
		return x.AncdaPassword
	}
	return ""
}

func (x *GetMonitorCameraConfigRsp) GetAncdaServerUrl() string {
	if x != nil {
		return x.AncdaServerUrl
	}
	return ""
}

func (x *GetMonitorCameraConfigRsp) GetAncdaServerPort() int32 {
	if x != nil {
		return x.AncdaServerPort
	}
	return 0
}

func (x *GetMonitorCameraConfigRsp) GetYsAccessToken() string {
	if x != nil {
		return x.YsAccessToken
	}
	return ""
}

// 获取教职工授权摄像头信息请求结构
type FindStaffCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 教职工id
	StaffIds []int64 `protobuf:"varint,1,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，2：家长端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *FindStaffCameraReq) Reset() {
	*x = FindStaffCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStaffCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStaffCameraReq) ProtoMessage() {}

func (x *FindStaffCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStaffCameraReq.ProtoReflect.Descriptor instead.
func (*FindStaffCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{45}
}

func (x *FindStaffCameraReq) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *FindStaffCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *FindStaffCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *FindStaffCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *FindStaffCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *FindStaffCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 获取教职工授权摄像头信息返回参数
type FindStaffCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 教职工授权信息
	AuthMap map[int64]*StaffCameraAuthInfo `protobuf:"bytes,1,rep,name=auth_map,json=authMap,proto3" json:"auth_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FindStaffCameraRsp) Reset() {
	*x = FindStaffCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStaffCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStaffCameraRsp) ProtoMessage() {}

func (x *FindStaffCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStaffCameraRsp.ProtoReflect.Descriptor instead.
func (*FindStaffCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{46}
}

func (x *FindStaffCameraRsp) GetAuthMap() map[int64]*StaffCameraAuthInfo {
	if x != nil {
		return x.AuthMap
	}
	return nil
}

// 班级授权信息请求结构
type FindClassCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassIds []int64 `protobuf:"varint,1,rep,packed,name=class_ids,json=classIds,proto3" json:"class_ids,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，2：家长端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *FindClassCameraReq) Reset() {
	*x = FindClassCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindClassCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindClassCameraReq) ProtoMessage() {}

func (x *FindClassCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindClassCameraReq.ProtoReflect.Descriptor instead.
func (*FindClassCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{47}
}

func (x *FindClassCameraReq) GetClassIds() []int64 {
	if x != nil {
		return x.ClassIds
	}
	return nil
}

func (x *FindClassCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *FindClassCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *FindClassCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *FindClassCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *FindClassCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 班级授权信息返回参数
type FindClassCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级授权信息
	AuthMap map[int64]*ClassCameraAuthInfo `protobuf:"bytes,1,rep,name=auth_map,json=authMap,proto3" json:"auth_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FindClassCameraRsp) Reset() {
	*x = FindClassCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindClassCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindClassCameraRsp) ProtoMessage() {}

func (x *FindClassCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindClassCameraRsp.ProtoReflect.Descriptor instead.
func (*FindClassCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{48}
}

func (x *FindClassCameraRsp) GetAuthMap() map[int64]*ClassCameraAuthInfo {
	if x != nil {
		return x.AuthMap
	}
	return nil
}

type GetClassIdsByCameraNameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 摄像头名称
	CameraName string `protobuf:"bytes,2,opt,name=camera_name,json=cameraName,proto3" json:"camera_name,omitempty"`
}

func (x *GetClassIdsByCameraNameReq) Reset() {
	*x = GetClassIdsByCameraNameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClassIdsByCameraNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClassIdsByCameraNameReq) ProtoMessage() {}

func (x *GetClassIdsByCameraNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClassIdsByCameraNameReq.ProtoReflect.Descriptor instead.
func (*GetClassIdsByCameraNameReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{49}
}

func (x *GetClassIdsByCameraNameReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetClassIdsByCameraNameReq) GetCameraName() string {
	if x != nil {
		return x.CameraName
	}
	return ""
}

type GetClassIdsByCameraNameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id列表
	ClassIds []int64 `protobuf:"varint,1,rep,packed,name=class_ids,json=classIds,proto3" json:"class_ids,omitempty"`
}

func (x *GetClassIdsByCameraNameRsp) Reset() {
	*x = GetClassIdsByCameraNameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClassIdsByCameraNameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClassIdsByCameraNameRsp) ProtoMessage() {}

func (x *GetClassIdsByCameraNameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClassIdsByCameraNameRsp.ProtoReflect.Descriptor instead.
func (*GetClassIdsByCameraNameRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{50}
}

func (x *GetClassIdsByCameraNameRsp) GetClassIds() []int64 {
	if x != nil {
		return x.ClassIds
	}
	return nil
}

type CameraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头id
	CameraId int64 `protobuf:"varint,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// 摄像头名称
	CameraName string `protobuf:"bytes,2,opt,name=camera_name,json=cameraName,proto3" json:"camera_name,omitempty"`
}

func (x *CameraInfo) Reset() {
	*x = CameraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraInfo) ProtoMessage() {}

func (x *CameraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraInfo.ProtoReflect.Descriptor instead.
func (*CameraInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{51}
}

func (x *CameraInfo) GetCameraId() int64 {
	if x != nil {
		return x.CameraId
	}
	return 0
}

func (x *CameraInfo) GetCameraName() string {
	if x != nil {
		return x.CameraName
	}
	return ""
}

// 班级授权信息请求结构
type GetClassCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，2：家长端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *GetClassCameraReq) Reset() {
	*x = GetClassCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClassCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClassCameraReq) ProtoMessage() {}

func (x *GetClassCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClassCameraReq.ProtoReflect.Descriptor instead.
func (*GetClassCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{52}
}

func (x *GetClassCameraReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *GetClassCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetClassCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *GetClassCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GetClassCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *GetClassCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 班级授权信息返回结构
type ClassCameraAuthInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头列表信息
	CameraList []*AuthorizedCameraInfo `protobuf:"bytes,1,rep,name=camera_list,json=cameraList,proto3" json:"camera_list,omitempty"`
}

func (x *ClassCameraAuthInfo) Reset() {
	*x = ClassCameraAuthInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassCameraAuthInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassCameraAuthInfo) ProtoMessage() {}

func (x *ClassCameraAuthInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassCameraAuthInfo.ProtoReflect.Descriptor instead.
func (*ClassCameraAuthInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{53}
}

func (x *ClassCameraAuthInfo) GetCameraList() []*AuthorizedCameraInfo {
	if x != nil {
		return x.CameraList
	}
	return nil
}

// 班级摄像头信息
type AuthorizedCameraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头id
	CameraId string `protobuf:"bytes,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// 摄像头名称
	CameraName string `protobuf:"bytes,2,opt,name=camera_name,json=cameraName,proto3" json:"camera_name,omitempty"`
	// 是否已授权
	Authorized bool `protobuf:"varint,3,opt,name=authorized,proto3" json:"authorized,omitempty"`
	// 摄像头所属平台（1：其他品牌，2：掌心宝贝）
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *AuthorizedCameraInfo) Reset() {
	*x = AuthorizedCameraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthorizedCameraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthorizedCameraInfo) ProtoMessage() {}

func (x *AuthorizedCameraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthorizedCameraInfo.ProtoReflect.Descriptor instead.
func (*AuthorizedCameraInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{54}
}

func (x *AuthorizedCameraInfo) GetCameraId() string {
	if x != nil {
		return x.CameraId
	}
	return ""
}

func (x *AuthorizedCameraInfo) GetCameraName() string {
	if x != nil {
		return x.CameraName
	}
	return ""
}

func (x *AuthorizedCameraInfo) GetAuthorized() bool {
	if x != nil {
		return x.Authorized
	}
	return false
}

func (x *AuthorizedCameraInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

// 删除班级授权请求参数
type ClearClassCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，2：家长端）
	Channel uint32 `protobuf:"varint,2,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,4,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *ClearClassCameraReq) Reset() {
	*x = ClearClassCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearClassCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearClassCameraReq) ProtoMessage() {}

func (x *ClearClassCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearClassCameraReq.ProtoReflect.Descriptor instead.
func (*ClearClassCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{55}
}

func (x *ClearClassCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ClearClassCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ClearClassCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *ClearClassCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ClearClassCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 删除班级授权返回参数
type ClearClassCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClearClassCameraRsp) Reset() {
	*x = ClearClassCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearClassCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearClassCameraRsp) ProtoMessage() {}

func (x *ClearClassCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearClassCameraRsp.ProtoReflect.Descriptor instead.
func (*ClearClassCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{56}
}

// 班级授权设置请求结构
type UpdateClassCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,2,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,3,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
	// 授权其他品牌摄像头id列表
	CameraIds []int64 `protobuf:"varint,4,rep,packed,name=camera_ids,json=cameraIds,proto3" json:"camera_ids,omitempty"`
	// 授权掌心宝贝摄像头id列表
	ZxCameraIds []string `protobuf:"bytes,5,rep,name=zx_camera_ids,json=zxCameraIds,proto3" json:"zx_camera_ids,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,6,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，2：家长端）
	Channel uint32 `protobuf:"varint,7,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,8,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,9,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,10,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateClassCameraReq) Reset() {
	*x = UpdateClassCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClassCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClassCameraReq) ProtoMessage() {}

func (x *UpdateClassCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClassCameraReq.ProtoReflect.Descriptor instead.
func (*UpdateClassCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{57}
}

func (x *UpdateClassCameraReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *UpdateClassCameraReq) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *UpdateClassCameraReq) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *UpdateClassCameraReq) GetCameraIds() []int64 {
	if x != nil {
		return x.CameraIds
	}
	return nil
}

func (x *UpdateClassCameraReq) GetZxCameraIds() []string {
	if x != nil {
		return x.ZxCameraIds
	}
	return nil
}

func (x *UpdateClassCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateClassCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateClassCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateClassCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateClassCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 班级授权设置返回结构
type UpdateClassCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateClassCameraRsp) Reset() {
	*x = UpdateClassCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClassCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClassCameraRsp) ProtoMessage() {}

func (x *UpdateClassCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClassCameraRsp.ProtoReflect.Descriptor instead.
func (*UpdateClassCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{58}
}

type ClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,2,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
}

func (x *ClassInfo) Reset() {
	*x = ClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassInfo) ProtoMessage() {}

func (x *ClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassInfo.ProtoReflect.Descriptor instead.
func (*ClassInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{59}
}

func (x *ClassInfo) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *ClassInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

// 教职工授权信息请求结构
type GetStaffCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 教职工id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，2：家长端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *GetStaffCameraReq) Reset() {
	*x = GetStaffCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffCameraReq) ProtoMessage() {}

func (x *GetStaffCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffCameraReq.ProtoReflect.Descriptor instead.
func (*GetStaffCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{60}
}

func (x *GetStaffCameraReq) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GetStaffCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetStaffCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *GetStaffCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GetStaffCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *GetStaffCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type StaffCameraAuthInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头列表信息
	CameraList []*AuthorizedCameraInfo `protobuf:"bytes,1,rep,name=camera_list,json=cameraList,proto3" json:"camera_list,omitempty"`
}

func (x *StaffCameraAuthInfo) Reset() {
	*x = StaffCameraAuthInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffCameraAuthInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffCameraAuthInfo) ProtoMessage() {}

func (x *StaffCameraAuthInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffCameraAuthInfo.ProtoReflect.Descriptor instead.
func (*StaffCameraAuthInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{61}
}

func (x *StaffCameraAuthInfo) GetCameraList() []*AuthorizedCameraInfo {
	if x != nil {
		return x.CameraList
	}
	return nil
}

// 教职工授权设置请求结构
type UpdateStaffCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 教职工id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 教职工姓名
	StaffName string `protobuf:"bytes,2,opt,name=staff_name,json=staffName,proto3" json:"staff_name,omitempty"`
	// 授权其他品牌摄像头列表
	CameraIds []int64 `protobuf:"varint,3,rep,packed,name=camera_ids,json=cameraIds,proto3" json:"camera_ids,omitempty"`
	// 授权掌心宝贝品牌摄像头列表
	ZxCameraIds []string `protobuf:"bytes,4,rep,name=zx_camera_ids,json=zxCameraIds,proto3" json:"zx_camera_ids,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,5,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，2：家长端）
	Channel uint32 `protobuf:"varint,6,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,7,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,8,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,9,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateStaffCameraReq) Reset() {
	*x = UpdateStaffCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffCameraReq) ProtoMessage() {}

func (x *UpdateStaffCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffCameraReq.ProtoReflect.Descriptor instead.
func (*UpdateStaffCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{62}
}

func (x *UpdateStaffCameraReq) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateStaffCameraReq) GetStaffName() string {
	if x != nil {
		return x.StaffName
	}
	return ""
}

func (x *UpdateStaffCameraReq) GetCameraIds() []int64 {
	if x != nil {
		return x.CameraIds
	}
	return nil
}

func (x *UpdateStaffCameraReq) GetZxCameraIds() []string {
	if x != nil {
		return x.ZxCameraIds
	}
	return nil
}

func (x *UpdateStaffCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateStaffCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateStaffCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateStaffCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateStaffCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 教职工授权设置返回结构
type UpdateStaffCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateStaffCameraRsp) Reset() {
	*x = UpdateStaffCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffCameraRsp) ProtoMessage() {}

func (x *UpdateStaffCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffCameraRsp.ProtoReflect.Descriptor instead.
func (*UpdateStaffCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{63}
}

// 班级访问时间段请求参数
type GetClassAccessTimeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，2：家长端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *GetClassAccessTimeReq) Reset() {
	*x = GetClassAccessTimeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClassAccessTimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClassAccessTimeReq) ProtoMessage() {}

func (x *GetClassAccessTimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClassAccessTimeReq.ProtoReflect.Descriptor instead.
func (*GetClassAccessTimeReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{64}
}

func (x *GetClassAccessTimeReq) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *GetClassAccessTimeReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetClassAccessTimeReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *GetClassAccessTimeReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GetClassAccessTimeReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *GetClassAccessTimeReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 访问时间设置信息
type GetClassAccessTimeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CameraList []*CameraAccessTimeInfo `protobuf:"bytes,1,rep,name=camera_list,json=cameraList,proto3" json:"camera_list,omitempty"`
}

func (x *GetClassAccessTimeRsp) Reset() {
	*x = GetClassAccessTimeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClassAccessTimeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClassAccessTimeRsp) ProtoMessage() {}

func (x *GetClassAccessTimeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClassAccessTimeRsp.ProtoReflect.Descriptor instead.
func (*GetClassAccessTimeRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{65}
}

func (x *GetClassAccessTimeRsp) GetCameraList() []*CameraAccessTimeInfo {
	if x != nil {
		return x.CameraList
	}
	return nil
}

// 摄像头访问时间设置信息
type CameraAccessTimeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头id
	CameraId string `protobuf:"bytes,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// 摄像头名称
	CameraName string `protobuf:"bytes,2,opt,name=camera_name,json=cameraName,proto3" json:"camera_name,omitempty"`
	// 访问时间设置信息
	AccessInfo *AccessTimeInfo `protobuf:"bytes,3,opt,name=access_info,json=accessInfo,proto3" json:"access_info,omitempty"`
	// 摄像头所属平台（1：其他品牌，2：掌心宝贝）
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *CameraAccessTimeInfo) Reset() {
	*x = CameraAccessTimeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraAccessTimeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraAccessTimeInfo) ProtoMessage() {}

func (x *CameraAccessTimeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraAccessTimeInfo.ProtoReflect.Descriptor instead.
func (*CameraAccessTimeInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{66}
}

func (x *CameraAccessTimeInfo) GetCameraId() string {
	if x != nil {
		return x.CameraId
	}
	return ""
}

func (x *CameraAccessTimeInfo) GetCameraName() string {
	if x != nil {
		return x.CameraName
	}
	return ""
}

func (x *CameraAccessTimeInfo) GetAccessInfo() *AccessTimeInfo {
	if x != nil {
		return x.AccessInfo
	}
	return nil
}

func (x *CameraAccessTimeInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type AccessTimeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 开放类型（1：自定义，2：按工作日）
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// 是否跳过节假日
	SkipHolidays bool `protobuf:"varint,2,opt,name=skip_holidays,json=skipHolidays,proto3" json:"skip_holidays,omitempty"`
	// 访问时间段信息
	AccessTimes []*AccessTime `protobuf:"bytes,3,rep,name=access_times,json=accessTimes,proto3" json:"access_times,omitempty"`
}

func (x *AccessTimeInfo) Reset() {
	*x = AccessTimeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessTimeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessTimeInfo) ProtoMessage() {}

func (x *AccessTimeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessTimeInfo.ProtoReflect.Descriptor instead.
func (*AccessTimeInfo) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{67}
}

func (x *AccessTimeInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AccessTimeInfo) GetSkipHolidays() bool {
	if x != nil {
		return x.SkipHolidays
	}
	return false
}

func (x *AccessTimeInfo) GetAccessTimes() []*AccessTime {
	if x != nil {
		return x.AccessTimes
	}
	return nil
}

// 访问时间信息
type AccessTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 星期（0：代表工作日，1~7：代表星期一~星期日）
	Week int32 `protobuf:"varint,1,opt,name=week,proto3" json:"week,omitempty"`
	// 访问时间段列表
	List []*AccessTimePeriod `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AccessTime) Reset() {
	*x = AccessTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessTime) ProtoMessage() {}

func (x *AccessTime) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessTime.ProtoReflect.Descriptor instead.
func (*AccessTime) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{68}
}

func (x *AccessTime) GetWeek() int32 {
	if x != nil {
		return x.Week
	}
	return 0
}

func (x *AccessTime) GetList() []*AccessTimePeriod {
	if x != nil {
		return x.List
	}
	return nil
}

// 访问时间段信息
type AccessTimePeriod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 开始时间
	StartTime string `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime string `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *AccessTimePeriod) Reset() {
	*x = AccessTimePeriod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessTimePeriod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessTimePeriod) ProtoMessage() {}

func (x *AccessTimePeriod) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessTimePeriod.ProtoReflect.Descriptor instead.
func (*AccessTimePeriod) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{69}
}

func (x *AccessTimePeriod) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *AccessTimePeriod) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type ClassCamera struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,2,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,3,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
	// 摄像头id
	CameraId string `protobuf:"bytes,4,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// 摄像头所属平台（1：其他品牌，2：掌心宝贝）
	Type int32 `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *ClassCamera) Reset() {
	*x = ClassCamera{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassCamera) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassCamera) ProtoMessage() {}

func (x *ClassCamera) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassCamera.ProtoReflect.Descriptor instead.
func (*ClassCamera) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{70}
}

func (x *ClassCamera) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *ClassCamera) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *ClassCamera) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *ClassCamera) GetCameraId() string {
	if x != nil {
		return x.CameraId
	}
	return ""
}

func (x *ClassCamera) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

// 更新班级访问时间段请求参数
type UpdateClassAccessTimeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级摄像头信息
	ClassCameras []*ClassCamera `protobuf:"bytes,1,rep,name=class_cameras,json=classCameras,proto3" json:"class_cameras,omitempty"`
	// 访问时间信息
	AccessTimeInfo *AccessTimeInfo `protobuf:"bytes,2,opt,name=access_time_info,json=accessTimeInfo,proto3" json:"access_time_info,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，2：家长端）
	Channel uint32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,6,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateClassAccessTimeReq) Reset() {
	*x = UpdateClassAccessTimeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClassAccessTimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClassAccessTimeReq) ProtoMessage() {}

func (x *UpdateClassAccessTimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClassAccessTimeReq.ProtoReflect.Descriptor instead.
func (*UpdateClassAccessTimeReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{71}
}

func (x *UpdateClassAccessTimeReq) GetClassCameras() []*ClassCamera {
	if x != nil {
		return x.ClassCameras
	}
	return nil
}

func (x *UpdateClassAccessTimeReq) GetAccessTimeInfo() *AccessTimeInfo {
	if x != nil {
		return x.AccessTimeInfo
	}
	return nil
}

func (x *UpdateClassAccessTimeReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateClassAccessTimeReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateClassAccessTimeReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateClassAccessTimeReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateClassAccessTimeReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 班级访问时间段设置返回参数
type UpdateClassAccessTimeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateClassAccessTimeRsp) Reset() {
	*x = UpdateClassAccessTimeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClassAccessTimeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClassAccessTimeRsp) ProtoMessage() {}

func (x *UpdateClassAccessTimeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClassAccessTimeRsp.ProtoReflect.Descriptor instead.
func (*UpdateClassAccessTimeRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{72}
}

// 查询监控设备列表请求结构
type ListMonitorDeviceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备序列号
	SerialNo string `protobuf:"bytes,1,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 注册平台（1：EHome， 2：国标，4：萤石云，5：萤石国标，6：掌心魔方）
	Platform int32 `protobuf:"varint,2,opt,name=platform,proto3" json:"platform,omitempty"`
	// 设备类型（1：摄像头；2：4路NVR；3：8路NVR；4：16路NVR；5：32路NVR；6：64路NVR, 7:128路NVR）
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	// 摄像头/通道名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 机构id
	InstId int64 `protobuf:"varint,5,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 页数
	Page int32 `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,7,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 渠道 （1：园长后台，2：园丁端，3：家长端）
	Channel uint32 `protobuf:"varint,8,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,9,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,10,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,11,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *ListMonitorDeviceReq) Reset() {
	*x = ListMonitorDeviceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonitorDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonitorDeviceReq) ProtoMessage() {}

func (x *ListMonitorDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonitorDeviceReq.ProtoReflect.Descriptor instead.
func (*ListMonitorDeviceReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{73}
}

func (x *ListMonitorDeviceReq) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *ListMonitorDeviceReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *ListMonitorDeviceReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ListMonitorDeviceReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListMonitorDeviceReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListMonitorDeviceReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListMonitorDeviceReq) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListMonitorDeviceReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ListMonitorDeviceReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *ListMonitorDeviceReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ListMonitorDeviceReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 其他品牌摄像头查询返回结构
type ListMonitorDeviceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 页数
	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 总条数
	Total uint64 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	// 设备列表
	List []*MonitorDevice `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListMonitorDeviceRsp) Reset() {
	*x = ListMonitorDeviceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonitorDeviceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonitorDeviceRsp) ProtoMessage() {}

func (x *ListMonitorDeviceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonitorDeviceRsp.ProtoReflect.Descriptor instead.
func (*ListMonitorDeviceRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{74}
}

func (x *ListMonitorDeviceRsp) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListMonitorDeviceRsp) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListMonitorDeviceRsp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListMonitorDeviceRsp) GetList() []*MonitorDevice {
	if x != nil {
		return x.List
	}
	return nil
}

// 设备信息
type MonitorDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备id
	DeviceId int64 `protobuf:"varint,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 设备名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 设备类型（1：摄像头；2：4路NVR；3：8路NVR；4：16路NVR；5：32路NVR）
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	// 设备序列号
	SerialNo string `protobuf:"bytes,4,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 所属平台（1：EHome, 2：国标，4：萤石云，5：萤石国标，6：掌心魔方）
	Platform uint32 `protobuf:"varint,5,opt,name=platform,proto3" json:"platform,omitempty"`
	// 所属学校id
	InstId int64 `protobuf:"varint,6,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 绑定时间，时间戳
	CreateTime int64 `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 摄像头/通道列表
	CameraList []*Camera `protobuf:"bytes,8,rep,name=camera_list,json=cameraList,proto3" json:"camera_list,omitempty"`
	// 国标配置信息
	GbConfig *GBConfig `protobuf:"bytes,9,opt,name=gb_config,json=gbConfig,proto3" json:"gb_config,omitempty"`
	// 设备验证码
	ValidateCode string `protobuf:"bytes,10,opt,name=validate_code,json=validateCode,proto3" json:"validate_code,omitempty"`
}

func (x *MonitorDevice) Reset() {
	*x = MonitorDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorDevice) ProtoMessage() {}

func (x *MonitorDevice) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorDevice.ProtoReflect.Descriptor instead.
func (*MonitorDevice) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{75}
}

func (x *MonitorDevice) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *MonitorDevice) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MonitorDevice) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *MonitorDevice) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *MonitorDevice) GetPlatform() uint32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *MonitorDevice) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *MonitorDevice) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MonitorDevice) GetCameraList() []*Camera {
	if x != nil {
		return x.CameraList
	}
	return nil
}

func (x *MonitorDevice) GetGbConfig() *GBConfig {
	if x != nil {
		return x.GbConfig
	}
	return nil
}

func (x *MonitorDevice) GetValidateCode() string {
	if x != nil {
		return x.ValidateCode
	}
	return ""
}

// 国标配置信息
type GBConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 国标id
	GbId string `protobuf:"bytes,1,opt,name=gb_id,json=gbId,proto3" json:"gb_id,omitempty"`
	// sip服务器id
	SipId string `protobuf:"bytes,2,opt,name=sip_id,json=sipId,proto3" json:"sip_id,omitempty"`
	// SIP服务器域
	SipDomain string `protobuf:"bytes,3,opt,name=sip_domain,json=sipDomain,proto3" json:"sip_domain,omitempty"`
	// SIP服务ip
	SipIp string `protobuf:"bytes,4,opt,name=sip_ip,json=sipIp,proto3" json:"sip_ip,omitempty"`
	// SIP服务器端口
	SipPort uint32 `protobuf:"varint,5,opt,name=sip_port,json=sipPort,proto3" json:"sip_port,omitempty"`
	// 本地SIP端口
	SipLocalPort uint32 `protobuf:"varint,6,opt,name=sip_local_port,json=sipLocalPort,proto3" json:"sip_local_port,omitempty"`
	// SIP用户名
	SipUsername string `protobuf:"bytes,7,opt,name=sip_username,json=sipUsername,proto3" json:"sip_username,omitempty"`
	// SIP密码
	SipPassword string `protobuf:"bytes,8,opt,name=sip_password,json=sipPassword,proto3" json:"sip_password,omitempty"`
}

func (x *GBConfig) Reset() {
	*x = GBConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GBConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GBConfig) ProtoMessage() {}

func (x *GBConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GBConfig.ProtoReflect.Descriptor instead.
func (*GBConfig) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{76}
}

func (x *GBConfig) GetGbId() string {
	if x != nil {
		return x.GbId
	}
	return ""
}

func (x *GBConfig) GetSipId() string {
	if x != nil {
		return x.SipId
	}
	return ""
}

func (x *GBConfig) GetSipDomain() string {
	if x != nil {
		return x.SipDomain
	}
	return ""
}

func (x *GBConfig) GetSipIp() string {
	if x != nil {
		return x.SipIp
	}
	return ""
}

func (x *GBConfig) GetSipPort() uint32 {
	if x != nil {
		return x.SipPort
	}
	return 0
}

func (x *GBConfig) GetSipLocalPort() uint32 {
	if x != nil {
		return x.SipLocalPort
	}
	return 0
}

func (x *GBConfig) GetSipUsername() string {
	if x != nil {
		return x.SipUsername
	}
	return ""
}

func (x *GBConfig) GetSipPassword() string {
	if x != nil {
		return x.SipPassword
	}
	return ""
}

// 摄像头信息
type Camera struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 摄像头类型（1：摄像头，2：nvr通道）
	Type uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// 摄像头/通道名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 通道，如果是摄像头，则为1，nvr则为具体通道数
	Channel int32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel,omitempty"`
	// 直播间id
	LiveId string `protobuf:"bytes,5,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	// 视频清晰度(1：普清，2：高清)
	LiveMode uint32 `protobuf:"varint,6,opt,name=live_mode,json=liveMode,proto3" json:"live_mode,omitempty"`
	// 设备序列号
	SerialNo string `protobuf:"bytes,7,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 所属学校id
	InstId int64 `protobuf:"varint,8,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 创建时间，时间戳
	CreateTime int64 `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间，时间戳
	UpdateTime int64 `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 通道序列号，注册平台是国标时有值
	ChannelSerialNo string `protobuf:"bytes,11,opt,name=channel_serial_no,json=channelSerialNo,proto3" json:"channel_serial_no,omitempty"`
}

func (x *Camera) Reset() {
	*x = Camera{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Camera) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Camera) ProtoMessage() {}

func (x *Camera) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Camera.ProtoReflect.Descriptor instead.
func (*Camera) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{77}
}

func (x *Camera) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Camera) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Camera) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Camera) GetChannel() int32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *Camera) GetLiveId() string {
	if x != nil {
		return x.LiveId
	}
	return ""
}

func (x *Camera) GetLiveMode() uint32 {
	if x != nil {
		return x.LiveMode
	}
	return 0
}

func (x *Camera) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *Camera) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *Camera) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Camera) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Camera) GetChannelSerialNo() string {
	if x != nil {
		return x.ChannelSerialNo
	}
	return ""
}

// 添加监控设备请求结构
type CreateMonitorDeviceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备序列号
	SerialNo string `protobuf:"bytes,1,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 注册平台（1：EHome， 2：国标，4：萤石云，5：萤石国标，6：掌心魔方）
	Platform int32 `protobuf:"varint,2,opt,name=platform,proto3" json:"platform,omitempty"`
	// 设备类型（1：摄像头；2：4路NVR；3：8路NVR；4：16路NVR；5：32路NVR；6：64路NVR, 7: 128路NVR）
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	// 设备名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,5,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 操作渠道 （1：园长后台，2：园丁端）
	Channel uint32 `protobuf:"varint,6,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,7,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,8,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,9,opt,name=ip,proto3" json:"ip,omitempty"`
	// 设备验证码，萤石云平台需要提供
	ValidateCode string `protobuf:"bytes,10,opt,name=validate_code,json=validateCode,proto3" json:"validate_code,omitempty"`
}

func (x *CreateMonitorDeviceReq) Reset() {
	*x = CreateMonitorDeviceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMonitorDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMonitorDeviceReq) ProtoMessage() {}

func (x *CreateMonitorDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMonitorDeviceReq.ProtoReflect.Descriptor instead.
func (*CreateMonitorDeviceReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{78}
}

func (x *CreateMonitorDeviceReq) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *CreateMonitorDeviceReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *CreateMonitorDeviceReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CreateMonitorDeviceReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateMonitorDeviceReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *CreateMonitorDeviceReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *CreateMonitorDeviceReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateMonitorDeviceReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *CreateMonitorDeviceReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CreateMonitorDeviceReq) GetValidateCode() string {
	if x != nil {
		return x.ValidateCode
	}
	return ""
}

// 添加监控设备返回结构
type CreateMonitorDeviceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateMonitorDeviceRsp) Reset() {
	*x = CreateMonitorDeviceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMonitorDeviceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMonitorDeviceRsp) ProtoMessage() {}

func (x *CreateMonitorDeviceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMonitorDeviceRsp.ProtoReflect.Descriptor instead.
func (*CreateMonitorDeviceRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{79}
}

// 编辑监控设备请求结构
type UpdateMonitorDeviceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备id
	DeviceId int64 `protobuf:"varint,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 设备名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 操作渠道 （1：园长后台，2：园丁端）
	Channel uint32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,6,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateMonitorDeviceReq) Reset() {
	*x = UpdateMonitorDeviceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMonitorDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMonitorDeviceReq) ProtoMessage() {}

func (x *UpdateMonitorDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMonitorDeviceReq.ProtoReflect.Descriptor instead.
func (*UpdateMonitorDeviceReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{80}
}

func (x *UpdateMonitorDeviceReq) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *UpdateMonitorDeviceReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateMonitorDeviceReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateMonitorDeviceReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateMonitorDeviceReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateMonitorDeviceReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateMonitorDeviceReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 编辑监控设备返回结构
type UpdateMonitorDeviceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateMonitorDeviceRsp) Reset() {
	*x = UpdateMonitorDeviceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMonitorDeviceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMonitorDeviceRsp) ProtoMessage() {}

func (x *UpdateMonitorDeviceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMonitorDeviceRsp.ProtoReflect.Descriptor instead.
func (*UpdateMonitorDeviceRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{81}
}

// 删除监控设备请求结构
type DeleteMonitorDeviceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备id
	DeviceId int64 `protobuf:"varint,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 操作渠道 （1：园长后台，2：园丁端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *DeleteMonitorDeviceReq) Reset() {
	*x = DeleteMonitorDeviceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMonitorDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMonitorDeviceReq) ProtoMessage() {}

func (x *DeleteMonitorDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMonitorDeviceReq.ProtoReflect.Descriptor instead.
func (*DeleteMonitorDeviceReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{82}
}

func (x *DeleteMonitorDeviceReq) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *DeleteMonitorDeviceReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *DeleteMonitorDeviceReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *DeleteMonitorDeviceReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *DeleteMonitorDeviceReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *DeleteMonitorDeviceReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 删除监控设备返回结构
type DeleteMonitorDeviceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteMonitorDeviceRsp) Reset() {
	*x = DeleteMonitorDeviceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMonitorDeviceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMonitorDeviceRsp) ProtoMessage() {}

func (x *DeleteMonitorDeviceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMonitorDeviceRsp.ProtoReflect.Descriptor instead.
func (*DeleteMonitorDeviceRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{83}
}

// 获取设备状态请求结构
type GetMonitorDeviceStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备id
	DeviceId int64 `protobuf:"varint,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 操作渠道 （1：园长后台，2：园丁端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *GetMonitorDeviceStatusReq) Reset() {
	*x = GetMonitorDeviceStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonitorDeviceStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonitorDeviceStatusReq) ProtoMessage() {}

func (x *GetMonitorDeviceStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonitorDeviceStatusReq.ProtoReflect.Descriptor instead.
func (*GetMonitorDeviceStatusReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{84}
}

func (x *GetMonitorDeviceStatusReq) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *GetMonitorDeviceStatusReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetMonitorDeviceStatusReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *GetMonitorDeviceStatusReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GetMonitorDeviceStatusReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *GetMonitorDeviceStatusReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 获取设备状态返回结构
type GetMonitorDeviceStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头/通道状态信息
	List []*MonitorCameraStatus `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetMonitorDeviceStatusRsp) Reset() {
	*x = GetMonitorDeviceStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonitorDeviceStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonitorDeviceStatusRsp) ProtoMessage() {}

func (x *GetMonitorDeviceStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonitorDeviceStatusRsp.ProtoReflect.Descriptor instead.
func (*GetMonitorDeviceStatusRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{85}
}

func (x *GetMonitorDeviceStatusRsp) GetList() []*MonitorCameraStatus {
	if x != nil {
		return x.List
	}
	return nil
}

type MonitorCameraStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头id
	CameraId int64 `protobuf:"varint,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// 状态（0：未知，1：在线，2：离线）
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *MonitorCameraStatus) Reset() {
	*x = MonitorCameraStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorCameraStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorCameraStatus) ProtoMessage() {}

func (x *MonitorCameraStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorCameraStatus.ProtoReflect.Descriptor instead.
func (*MonitorCameraStatus) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{86}
}

func (x *MonitorCameraStatus) GetCameraId() int64 {
	if x != nil {
		return x.CameraId
	}
	return 0
}

func (x *MonitorCameraStatus) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 获取监控设备信息请求结构
type GetMonitorDeviceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备id
	DeviceId int64 `protobuf:"varint,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 操作渠道 （1：园长后台，2：园丁端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *GetMonitorDeviceReq) Reset() {
	*x = GetMonitorDeviceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonitorDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonitorDeviceReq) ProtoMessage() {}

func (x *GetMonitorDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonitorDeviceReq.ProtoReflect.Descriptor instead.
func (*GetMonitorDeviceReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{87}
}

func (x *GetMonitorDeviceReq) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *GetMonitorDeviceReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetMonitorDeviceReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *GetMonitorDeviceReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GetMonitorDeviceReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *GetMonitorDeviceReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 添加摄像头/通道请求结构
type CreateMonitorCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 所属设备id
	DeviceId int64 `protobuf:"varint,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 通道号
	ChannelNo uint32 `protobuf:"varint,2,opt,name=channel_no,json=channelNo,proto3" json:"channel_no,omitempty"`
	// 通道名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 授权班级id列表
	ClassIds []int64 `protobuf:"varint,4,rep,packed,name=class_ids,json=classIds,proto3" json:"class_ids,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,5,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 操作渠道 （1：园长后台，2：园丁端）
	Channel uint32 `protobuf:"varint,6,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,7,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,8,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,9,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *CreateMonitorCameraReq) Reset() {
	*x = CreateMonitorCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMonitorCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMonitorCameraReq) ProtoMessage() {}

func (x *CreateMonitorCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMonitorCameraReq.ProtoReflect.Descriptor instead.
func (*CreateMonitorCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{88}
}

func (x *CreateMonitorCameraReq) GetDeviceId() int64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *CreateMonitorCameraReq) GetChannelNo() uint32 {
	if x != nil {
		return x.ChannelNo
	}
	return 0
}

func (x *CreateMonitorCameraReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateMonitorCameraReq) GetClassIds() []int64 {
	if x != nil {
		return x.ClassIds
	}
	return nil
}

func (x *CreateMonitorCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *CreateMonitorCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *CreateMonitorCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateMonitorCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *CreateMonitorCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 添加摄像头/通道返回结构
type CreateMonitorCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateMonitorCameraRsp) Reset() {
	*x = CreateMonitorCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMonitorCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMonitorCameraRsp) ProtoMessage() {}

func (x *CreateMonitorCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMonitorCameraRsp.ProtoReflect.Descriptor instead.
func (*CreateMonitorCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{89}
}

// 编辑通道请求结构
type UpdateMonitorCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头id
	CameraId int64 `protobuf:"varint,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// 通道名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 操作渠道 （1：园长后台，2：园丁端）
	Channel uint32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,6,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateMonitorCameraReq) Reset() {
	*x = UpdateMonitorCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMonitorCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMonitorCameraReq) ProtoMessage() {}

func (x *UpdateMonitorCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMonitorCameraReq.ProtoReflect.Descriptor instead.
func (*UpdateMonitorCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{90}
}

func (x *UpdateMonitorCameraReq) GetCameraId() int64 {
	if x != nil {
		return x.CameraId
	}
	return 0
}

func (x *UpdateMonitorCameraReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateMonitorCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateMonitorCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateMonitorCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateMonitorCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateMonitorCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 编辑摄像头/通道返回结构
type UpdateMonitorCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateMonitorCameraRsp) Reset() {
	*x = UpdateMonitorCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMonitorCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMonitorCameraRsp) ProtoMessage() {}

func (x *UpdateMonitorCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMonitorCameraRsp.ProtoReflect.Descriptor instead.
func (*UpdateMonitorCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{91}
}

// 删除摄像头/通道请求结构
type DeleteMonitorCameraReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 摄像头id
	CameraId int64 `protobuf:"varint,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 操作渠道 （1：园长后台，2：园丁端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *DeleteMonitorCameraReq) Reset() {
	*x = DeleteMonitorCameraReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMonitorCameraReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMonitorCameraReq) ProtoMessage() {}

func (x *DeleteMonitorCameraReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMonitorCameraReq.ProtoReflect.Descriptor instead.
func (*DeleteMonitorCameraReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{92}
}

func (x *DeleteMonitorCameraReq) GetCameraId() int64 {
	if x != nil {
		return x.CameraId
	}
	return 0
}

func (x *DeleteMonitorCameraReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *DeleteMonitorCameraReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *DeleteMonitorCameraReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *DeleteMonitorCameraReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *DeleteMonitorCameraReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 删除通道返回结构
type DeleteMonitorCameraRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteMonitorCameraRsp) Reset() {
	*x = DeleteMonitorCameraRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMonitorCameraRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMonitorCameraRsp) ProtoMessage() {}

func (x *DeleteMonitorCameraRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMonitorCameraRsp.ProtoReflect.Descriptor instead.
func (*DeleteMonitorCameraRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{93}
}

type QiniuToYsyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 设备序列号
	SerialNo string `protobuf:"bytes,2,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,4,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *QiniuToYsyReq) Reset() {
	*x = QiniuToYsyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QiniuToYsyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QiniuToYsyReq) ProtoMessage() {}

func (x *QiniuToYsyReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QiniuToYsyReq.ProtoReflect.Descriptor instead.
func (*QiniuToYsyReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{94}
}

func (x *QiniuToYsyReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *QiniuToYsyReq) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *QiniuToYsyReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *QiniuToYsyReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *QiniuToYsyReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type QiniuToYsyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QiniuToYsyRsp) Reset() {
	*x = QiniuToYsyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QiniuToYsyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QiniuToYsyRsp) ProtoMessage() {}

func (x *QiniuToYsyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QiniuToYsyRsp.ProtoReflect.Descriptor instead.
func (*QiniuToYsyRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{95}
}

type YsyDeviceInfoExportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 设备序列号
	SerialNo string `protobuf:"bytes,2,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
}

func (x *YsyDeviceInfoExportReq) Reset() {
	*x = YsyDeviceInfoExportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YsyDeviceInfoExportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YsyDeviceInfoExportReq) ProtoMessage() {}

func (x *YsyDeviceInfoExportReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YsyDeviceInfoExportReq.ProtoReflect.Descriptor instead.
func (*YsyDeviceInfoExportReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{96}
}

func (x *YsyDeviceInfoExportReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *YsyDeviceInfoExportReq) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

type YsyDeviceInfoExportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 返回文件流
	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *YsyDeviceInfoExportRsp) Reset() {
	*x = YsyDeviceInfoExportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YsyDeviceInfoExportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YsyDeviceInfoExportRsp) ProtoMessage() {}

func (x *YsyDeviceInfoExportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YsyDeviceInfoExportRsp.ProtoReflect.Descriptor instead.
func (*YsyDeviceInfoExportRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{97}
}

func (x *YsyDeviceInfoExportRsp) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type SetLiveModelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 用户id
	UserId int64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 设备号
	SerialNo string `protobuf:"bytes,3,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// 索引号
	ChannelIndex int32 `protobuf:"varint,4,opt,name=channel_index,json=channelIndex,proto3" json:"channel_index,omitempty"`
	// 清晰度
	Model int32 `protobuf:"varint,5,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *SetLiveModelReq) Reset() {
	*x = SetLiveModelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLiveModelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLiveModelReq) ProtoMessage() {}

func (x *SetLiveModelReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLiveModelReq.ProtoReflect.Descriptor instead.
func (*SetLiveModelReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{98}
}

func (x *SetLiveModelReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *SetLiveModelReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SetLiveModelReq) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *SetLiveModelReq) GetChannelIndex() int32 {
	if x != nil {
		return x.ChannelIndex
	}
	return 0
}

func (x *SetLiveModelReq) GetModel() int32 {
	if x != nil {
		return x.Model
	}
	return 0
}

type SetLiveModelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetLiveModelRsp) Reset() {
	*x = SetLiveModelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLiveModelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLiveModelRsp) ProtoMessage() {}

func (x *SetLiveModelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLiveModelRsp.ProtoReflect.Descriptor instead.
func (*SetLiveModelRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{99}
}

type QiniuBackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机构id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 序列号
	SerialNo string `protobuf:"bytes,2,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
}

func (x *QiniuBackReq) Reset() {
	*x = QiniuBackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QiniuBackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QiniuBackReq) ProtoMessage() {}

func (x *QiniuBackReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QiniuBackReq.ProtoReflect.Descriptor instead.
func (*QiniuBackReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{100}
}

func (x *QiniuBackReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *QiniuBackReq) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

type QiniuBackRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QiniuBackRsp) Reset() {
	*x = QiniuBackRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QiniuBackRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QiniuBackRsp) ProtoMessage() {}

func (x *QiniuBackRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QiniuBackRsp.ProtoReflect.Descriptor instead.
func (*QiniuBackRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{101}
}

type CronCloseChannelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CronCloseChannelReq) Reset() {
	*x = CronCloseChannelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CronCloseChannelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CronCloseChannelReq) ProtoMessage() {}

func (x *CronCloseChannelReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CronCloseChannelReq.ProtoReflect.Descriptor instead.
func (*CronCloseChannelReq) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{102}
}

type CronCloseChannelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CronCloseChannelRsp) Reset() {
	*x = CronCloseChannelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_monitor_v1_monitor_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CronCloseChannelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CronCloseChannelRsp) ProtoMessage() {}

func (x *CronCloseChannelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_api_monitor_v1_monitor_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CronCloseChannelRsp.ProtoReflect.Descriptor instead.
func (*CronCloseChannelRsp) Descriptor() ([]byte, []int) {
	return file_api_monitor_v1_monitor_proto_rawDescGZIP(), []int{103}
}

var File_api_monitor_v1_monitor_proto protoreflect.FileDescriptor

var file_api_monitor_v1_monitor_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x43, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0xd8, 0x01, 0x0a, 0x1f, 0x47, 0x65,
	0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x12, 0x2a, 0x0a,
	0x11, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x6e, 0x65, 0x65, 0x64, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61,
	0x62, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5a, 0x0a, 0x16, 0x61, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x7a, 0x65, 0x64, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x14,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x90, 0x02, 0x0a, 0x19, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5a,
	0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x26, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07, 0x71, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x1a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x1b, 0x0a, 0x19, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x73, 0x70, 0x22, 0xe4, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5a,
	0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x0a, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69,
	0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30,
	0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x14, 0x0a, 0x12, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x73,
	0x70, 0x22, 0x8b, 0x02, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5a, 0x78, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a,
	0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22,
	0x14, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x52, 0x73, 0x70, 0x22, 0xbf, 0x01, 0x0a, 0x15, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x5a,
	0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x17, 0x0a, 0x15, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x73, 0x70,
	0x22, 0xfd, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x22, 0x57, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xef, 0x01, 0x0a, 0x09, 0x5a, 0x78,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0b, 0x6f, 0x6c, 0x64, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b,
	0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x22, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0x33, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x52, 0x73, 0x70,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x74, 0x6d, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x72, 0x74, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x22, 0xae, 0x01, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x50, 0x6c, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07,
	0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x50, 0x6c, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x62, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x63,
	0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x74, 0x6d, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x74, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xa1, 0x02, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x74, 0x73, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x22, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x22, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x07, 0x70, 0x65,
	0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0x65, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x74, 0x73,
	0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x38, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x86,
	0x02, 0x0a, 0x14, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x83, 0x02, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20,
	0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69,
	0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0x67, 0x0a,
	0x14, 0x53, 0x74, 0x61, 0x74, 0x73, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xc9, 0x01, 0x0a, 0x15, 0x53, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xb7, 0x02, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a,
	0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67,
	0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x53, 0x6f, 0x72, 0x74, 0x22, 0x5b, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8a, 0x03, 0x0a, 0x0b, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75,
	0x64, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x69,
	0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x69, 0x76,
	0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x7d, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x74, 0x73, 0x54,
	0x6f, 0x64, 0x61, 0x79, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x66, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x74, 0x73, 0x54, 0x6f,
	0x64, 0x61, 0x79, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x77,
	0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x77, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x75,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x22, 0xf4, 0x01,
	0x0a, 0x14, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x30, 0x01, 0x30, 0x03, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x49, 0x64, 0x22, 0x33, 0x0a, 0x14, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x20, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x20,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x03, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x20, 0x4c, 0x69, 0x73,
	0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a,
	0x0a, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x22, 0x84, 0x01, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x2d,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x11, 0xfa, 0x42, 0x0e, 0x1a, 0x0c, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x30, 0x04, 0x30,
	0x05, 0x30, 0x06, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x20, 0x0a,
	0x07, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x22, 0x46, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0xaf, 0x01, 0x0a, 0x0d, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5d, 0x0a, 0x1c, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69,
	0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x1c, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x69,
	0x73, 0x5f, 0x68, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x69, 0x73, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x73, 0x12, 0x41, 0x0a, 0x0b,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0xc9, 0x01, 0x0a, 0x10, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c,
	0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x48, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa1, 0x01, 0x0a, 0x14,
	0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72,
	0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04,
	0x30, 0x01, 0x30, 0x03, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x20,
	0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x93, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x07, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x68, 0x6f, 0x6c, 0x69, 0x64, 0x61,
	0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x48, 0x6f, 0x6c, 0x69,
	0x64, 0x61, 0x79, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xc1, 0x01, 0x0a, 0x0f, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a,
	0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x22, 0x9e, 0x03, 0x0a, 0x10, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c,
	0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6c, 0x69, 0x76, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x77, 0x61,
	0x74, 0x63, 0x68, 0x4e, 0x75, 0x6d, 0x12, 0x48, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f, 0x70,
	0x6c, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68,
	0x61, 0x73, 0x50, 0x6c, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x22, 0xcd, 0x01, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x03, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x75, 0x64, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30,
	0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0xbf, 0x02, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x12, 0x68, 0x65, 0x61, 0x72,
	0x74, 0x62, 0x65, 0x61, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x6e, 0x63, 0x64, 0x61, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x6e, 0x63, 0x64, 0x61, 0x55,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x6e, 0x63, 0x64, 0x61,
	0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x6e, 0x63, 0x64, 0x61, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x28,
	0x0a, 0x10, 0x61, 0x6e, 0x63, 0x64, 0x61, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x6e, 0x63, 0x64, 0x61, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x6e, 0x63, 0x64,
	0x61, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x61, 0x6e, 0x63, 0x64, 0x61, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x50, 0x6f, 0x72, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x79, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x79,
	0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xda, 0x01, 0x0a,
	0x12, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01,
	0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0xc1, 0x01, 0x0a, 0x12, 0x46, 0x69,
	0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70,
	0x12, 0x4a, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x1a, 0x5f, 0x0a, 0x0c,
	0x41, 0x75, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xda, 0x01,
	0x0a, 0x12, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08,
	0x01, 0x52, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x69,
	0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b,
	0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0xc1, 0x01, 0x0a, 0x12, 0x46,
	0x69, 0x6e, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73,
	0x70, 0x12, 0x4a, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x1a, 0x5f, 0x0a,
	0x0c, 0x41, 0x75, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x75, 0x74, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x56,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69,
	0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x39, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64,
	0x73, 0x22, 0x4a, 0x0a, 0x0a, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd6, 0x01,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a,
	0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x5c, 0x0a, 0x13, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45, 0x0a,
	0x0b, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x88, 0x01, 0x0a, 0x14, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x7a, 0x65, 0x64, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22,
	0xbd, 0x01, 0x0a, 0x13, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a,
	0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22,
	0x15, 0x0a, 0x13, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x22, 0x80, 0x03, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x12,
	0x22, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x7a, 0x78, 0x5f,
	0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x7a, 0x78, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x16, 0x0a, 0x14, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73,
	0x70, 0x22, 0x45, 0x0a, 0x09, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19,
	0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf3, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x12, 0x22,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02,
	0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x5c,
	0x0a, 0x13, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x75, 0x74,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xe1, 0x02, 0x0a,
	0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x73,
	0x12, 0x22, 0x0a, 0x0d, 0x7a, 0x78, 0x5f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x7a, 0x78, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01,
	0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a,
	0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70,
	0x22, 0x16, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x22, 0xda, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06,
	0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12,
	0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x5e, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x12, 0x45,
	0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xa9, 0x01, 0x0a, 0x14, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0b,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x22, 0x92, 0x01, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70,
	0x5f, 0x68, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x73, 0x6b, 0x69, 0x70, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x73, 0x12, 0x47, 0x0a,
	0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x07, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22, 0x79, 0x0a, 0x0a, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x1a, 0x10, 0x30, 0x00, 0x30, 0x01, 0x30, 0x02, 0x30,
	0x03, 0x30, 0x04, 0x30, 0x05, 0x30, 0x06, 0x30, 0x07, 0x52, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x12,
	0x40, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x06, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xa2, 0x01, 0x0a, 0x10, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x48, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x29, 0xfa, 0x42, 0x26, 0x72,
	0x24, 0x32, 0x22, 0x5e, 0x28, 0x5b, 0x30, 0x2d, 0x31, 0x5d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7c,
	0x32, 0x5b, 0x30, 0x2d, 0x33, 0x5d, 0x29, 0x3a, 0x28, 0x5b, 0x30, 0x2d, 0x35, 0x5d, 0x5b, 0x30,
	0x2d, 0x39, 0x5d, 0x29, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x44, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x29, 0xfa, 0x42, 0x26, 0x72, 0x24, 0x32, 0x22, 0x5e, 0x28, 0x5b, 0x30, 0x2d,
	0x31, 0x5d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7c, 0x32, 0x5b, 0x30, 0x2d, 0x33, 0x5d, 0x29, 0x3a,
	0x28, 0x5b, 0x30, 0x2d, 0x35, 0x5d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x24, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x0b, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0xe2, 0x02, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x40, 0x0a,
	0x0d, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x52, 0x0c, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x12,
	0x48, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x1a, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x73,
	0x70, 0x22, 0xf8, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x2d, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x1a, 0x0c,
	0x30, 0x00, 0x30, 0x01, 0x30, 0x02, 0x30, 0x04, 0x30, 0x05, 0x30, 0x06, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x1a, 0x10, 0x30, 0x00, 0x30, 0x01, 0x30,
	0x02, 0x30, 0x03, 0x30, 0x04, 0x30, 0x05, 0x30, 0x06, 0x30, 0x07, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70,
	0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01,
	0x30, 0x02, 0x30, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a,
	0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x8e, 0x01, 0x0a,
	0x14, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72,
	0x50, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xdc, 0x02,
	0x0a, 0x0d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x35, 0x0a, 0x09, 0x67, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x67,
	0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xf3, 0x01, 0x0a,
	0x08, 0x47, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x13, 0x0a, 0x05, 0x67, 0x62, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x62, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x73, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x69, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x70, 0x5f, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x70, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x69, 0x70, 0x5f, 0x69, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x69, 0x70, 0x49, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x69, 0x70, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73,
	0x69, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x69, 0x70, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x73, 0x69, 0x70, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x69, 0x70, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x70, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x69, 0x70, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x70, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x22, 0xb4, 0x02, 0x0a, 0x06, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12,
	0x17, 0x0a, 0x07, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6c, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x69, 0x76, 0x65,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6c, 0x69, 0x76,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x6e, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x4e, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a,
	0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x6e, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x22, 0x94, 0x03, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x32, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x2b, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0f,
	0xfa, 0x42, 0x0c, 0x1a, 0x0a, 0x30, 0x01, 0x30, 0x02, 0x30, 0x04, 0x30, 0x05, 0x30, 0x06, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x1a, 0x0e, 0x30, 0x01,
	0x30, 0x02, 0x30, 0x03, 0x30, 0x04, 0x30, 0x05, 0x30, 0x06, 0x30, 0x07, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x12, 0x23, 0x0a, 0x0d, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0x18, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0x97, 0x02, 0x0a, 0x16, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14,
	0x52, 0x02, 0x69, 0x70, 0x22, 0x18, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0xf8,
	0x01, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x73, 0x70, 0x22, 0xfb, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x24, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a,
	0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28,
	0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69,
	0x70, 0x22, 0x54, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x37,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x4a, 0x0a, 0x13, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xf5, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xd1, 0x02, 0x0a, 0x16,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30,
	0x01, 0x30, 0x02, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22,
	0x18, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x22, 0x8c, 0x02, 0x0a, 0x16, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x18, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52,
	0x73, 0x70, 0x22, 0xf8, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69,
	0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30,
	0x02, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x18, 0x0a,
	0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x22, 0xad, 0x01, 0x0a, 0x0d, 0x51, 0x69, 0x6e, 0x69,
	0x75, 0x54, 0x6f, 0x59, 0x73, 0x79, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x0f, 0x0a, 0x0d, 0x51, 0x69, 0x6e, 0x69, 0x75,
	0x54, 0x6f, 0x59, 0x73, 0x79, 0x52, 0x73, 0x70, 0x22, 0x57, 0x0a, 0x16, 0x59, 0x73, 0x79, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e,
	0x6f, 0x22, 0x2c, 0x0a, 0x16, 0x59, 0x73, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0xca, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69,
	0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x2c, 0x0a,
	0x0d, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1f, 0x0a, 0x05, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a,
	0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x11, 0x0a, 0x0f,
	0x53, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x22,
	0x56, 0x0a, 0x0c, 0x51, 0x69, 0x6e, 0x69, 0x75, 0x42, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x22, 0x0e, 0x0a, 0x0c, 0x51, 0x69, 0x6e, 0x69, 0x75,
	0x42, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x72, 0x6f, 0x6e, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x22, 0x15,
	0x0a, 0x13, 0x43, 0x72, 0x6f, 0x6e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x52, 0x73, 0x70, 0x32, 0xa9, 0x21, 0x0a, 0x0c, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x55, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x5a, 0x78,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x5a, 0x78, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x5a,
	0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x64, 0x0a,
	0x12, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x4e,
	0x6f, 0x64, 0x65, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5a, 0x78, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5a, 0x78,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x5b, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5a, 0x78, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5a,
	0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a,
	0x16, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5a,
	0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5a, 0x78, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x61, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x58, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65,
	0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00,
	0x12, 0x61, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x5e, 0x0a, 0x10, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c,
	0x65, 0x61, 0x72, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00, 0x12,
	0x61, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x64, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x42,
	0x79, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x0f,
	0x46, 0x69, 0x6e, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x0f, 0x46, 0x69, 0x6e,
	0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69,
	0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71,
	0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x85, 0x01, 0x0a, 0x1d,
	0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x2c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x52,
	0x0a, 0x0c, 0x47, 0x65, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x61, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x74, 0x73, 0x54, 0x6f,
	0x64, 0x61, 0x79, 0x57, 0x61, 0x74, 0x63, 0x68, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x54,
	0x6f, 0x64, 0x61, 0x79, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x5b, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x61, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x74, 0x73, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x53, 0x74, 0x75, 0x64, 0x65,
	0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x53, 0x74, 0x75, 0x64, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x5e, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x74, 0x73, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x79, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x12,
	0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x50, 0x6c, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x67, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x62, 0x61, 0x63,
	0x6b, 0x55, 0x72, 0x6c, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x50,
	0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72,
	0x6c, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x0a, 0x51, 0x69, 0x6e, 0x69, 0x75, 0x54,
	0x6f, 0x59, 0x73, 0x79, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x69, 0x6e, 0x69, 0x75, 0x54, 0x6f, 0x59, 0x73, 0x79,
	0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x69, 0x6e, 0x69, 0x75, 0x54, 0x6f, 0x59, 0x73, 0x79, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x13, 0x59, 0x73, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x59, 0x73, 0x79,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x59, 0x73, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x82, 0x01,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x49,
	0x6e, 0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x2f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e, 0x73,
	0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x71, 0x1a,
	0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e,
	0x73, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x52, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x09, 0x51, 0x69, 0x6e, 0x69, 0x75, 0x42,
	0x61, 0x63, 0x6b, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x69, 0x6e, 0x69, 0x75, 0x42, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x69, 0x6e, 0x69, 0x75, 0x42, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x5e, 0x0a, 0x10, 0x43, 0x72, 0x6f, 0x6e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x43, 0x6c, 0x6f, 0x73, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x6f, 0x6e,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x42, 0x13, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_monitor_v1_monitor_proto_rawDescOnce sync.Once
	file_api_monitor_v1_monitor_proto_rawDescData = file_api_monitor_v1_monitor_proto_rawDesc
)

func file_api_monitor_v1_monitor_proto_rawDescGZIP() []byte {
	file_api_monitor_v1_monitor_proto_rawDescOnce.Do(func() {
		file_api_monitor_v1_monitor_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_monitor_v1_monitor_proto_rawDescData)
	})
	return file_api_monitor_v1_monitor_proto_rawDescData
}

var file_api_monitor_v1_monitor_proto_msgTypes = make([]protoimpl.MessageInfo, 106)
var file_api_monitor_v1_monitor_proto_goTypes = []any{
	(*GetAuthorizedInstClassCameraReq)(nil),  // 0: api.monitor.v1.GetAuthorizedInstClassCameraReq
	(*GetAuthorizedInstClassCameraRsp)(nil),  // 1: api.monitor.v1.GetAuthorizedInstClassCameraRsp
	(*SwitchZxMonitorQualityReq)(nil),        // 2: api.monitor.v1.SwitchZxMonitorQualityReq
	(*SwitchZxMonitorQualityRsp)(nil),        // 3: api.monitor.v1.SwitchZxMonitorQualityRsp
	(*DeleteZxMonitorReq)(nil),               // 4: api.monitor.v1.DeleteZxMonitorReq
	(*DeleteZxMonitorRsp)(nil),               // 5: api.monitor.v1.DeleteZxMonitorRsp
	(*UpdateZxMonitorReq)(nil),               // 6: api.monitor.v1.UpdateZxMonitorReq
	(*UpdateZxMonitorRsp)(nil),               // 7: api.monitor.v1.UpdateZxMonitorRsp
	(*MergeZxMonitorNodeReq)(nil),            // 8: api.monitor.v1.MergeZxMonitorNodeReq
	(*MergeZxMonitorNodeRsp)(nil),            // 9: api.monitor.v1.MergeZxMonitorNodeRsp
	(*ListZxMonitorReq)(nil),                 // 10: api.monitor.v1.ListZxMonitorReq
	(*ListZxMonitorRsp)(nil),                 // 11: api.monitor.v1.ListZxMonitorRsp
	(*ZxMonitor)(nil),                        // 12: api.monitor.v1.ZxMonitor
	(*GetParentPaybackUrlReq)(nil),           // 13: api.monitor.v1.GetParentPaybackUrlReq
	(*GetParentPaybackUrlRsp)(nil),           // 14: api.monitor.v1.GetParentPaybackUrlRsp
	(*GetChannelHistoryPlaybackReq)(nil),     // 15: api.monitor.v1.GetChannelHistoryPlaybackReq
	(*GetChannelHistoryPlaybackRsp)(nil),     // 16: api.monitor.v1.GetChannelHistoryPlaybackRsp
	(*PlaybackInfo)(nil),                     // 17: api.monitor.v1.PlaybackInfo
	(*StatsParentWatchReq)(nil),              // 18: api.monitor.v1.StatsParentWatchReq
	(*StatsParentWatchRsp)(nil),              // 19: api.monitor.v1.StatsParentWatchRsp
	(*ParentWatchStatsInfo)(nil),             // 20: api.monitor.v1.ParentWatchStatsInfo
	(*StatsStudentWatchReq)(nil),             // 21: api.monitor.v1.StatsStudentWatchReq
	(*StatsStudentWatchRsp)(nil),             // 22: api.monitor.v1.StatsStudentWatchRsp
	(*StudentWatchStatsInfo)(nil),            // 23: api.monitor.v1.StudentWatchStatsInfo
	(*ListWatchRecordReq)(nil),               // 24: api.monitor.v1.ListWatchRecordReq
	(*ListWatchRecordRsp)(nil),               // 25: api.monitor.v1.ListWatchRecordRsp
	(*WatchRecord)(nil),                      // 26: api.monitor.v1.WatchRecord
	(*StatsTodayWatchReq)(nil),               // 27: api.monitor.v1.StatsTodayWatchReq
	(*StatsTodayWatchRsp)(nil),               // 28: api.monitor.v1.StatsTodayWatchRsp
	(*ReportWatchRecordReq)(nil),             // 29: api.monitor.v1.ReportWatchRecordReq
	(*ReportWatchRecordRsp)(nil),             // 30: api.monitor.v1.ReportWatchRecordRsp
	(*ListMonitorCameraOnlineStatusReq)(nil), // 31: api.monitor.v1.ListMonitorCameraOnlineStatusReq
	(*ListMonitorCameraOnlineStatusRsp)(nil), // 32: api.monitor.v1.ListMonitorCameraOnlineStatusRsp
	(*GetWatchUserReq)(nil),                  // 33: api.monitor.v1.GetWatchUserReq
	(*GetWatchUserRsp)(nil),                  // 34: api.monitor.v1.GetWatchUserRsp
	(*WatchUserInfo)(nil),                    // 35: api.monitor.v1.WatchUserInfo
	(*ListClassCameraAccessTimeReq)(nil),     // 36: api.monitor.v1.ListClassCameraAccessTimeReq
	(*ListClassCameraAccessTimeRsp)(nil),     // 37: api.monitor.v1.ListClassCameraAccessTimeRsp
	(*CameraAccessInfo)(nil),                 // 38: api.monitor.v1.CameraAccessInfo
	(*ListMonitorCameraReq)(nil),             // 39: api.monitor.v1.ListMonitorCameraReq
	(*ListMonitorCameraRsp)(nil),             // 40: api.monitor.v1.ListMonitorCameraRsp
	(*ClassCameraInfo)(nil),                  // 41: api.monitor.v1.ClassCameraInfo
	(*CameraDetailInfo)(nil),                 // 42: api.monitor.v1.CameraDetailInfo
	(*GetMonitorCameraConfigReq)(nil),        // 43: api.monitor.v1.GetMonitorCameraConfigReq
	(*GetMonitorCameraConfigRsp)(nil),        // 44: api.monitor.v1.GetMonitorCameraConfigRsp
	(*FindStaffCameraReq)(nil),               // 45: api.monitor.v1.FindStaffCameraReq
	(*FindStaffCameraRsp)(nil),               // 46: api.monitor.v1.FindStaffCameraRsp
	(*FindClassCameraReq)(nil),               // 47: api.monitor.v1.FindClassCameraReq
	(*FindClassCameraRsp)(nil),               // 48: api.monitor.v1.FindClassCameraRsp
	(*GetClassIdsByCameraNameReq)(nil),       // 49: api.monitor.v1.GetClassIdsByCameraNameReq
	(*GetClassIdsByCameraNameRsp)(nil),       // 50: api.monitor.v1.GetClassIdsByCameraNameRsp
	(*CameraInfo)(nil),                       // 51: api.monitor.v1.CameraInfo
	(*GetClassCameraReq)(nil),                // 52: api.monitor.v1.GetClassCameraReq
	(*ClassCameraAuthInfo)(nil),              // 53: api.monitor.v1.ClassCameraAuthInfo
	(*AuthorizedCameraInfo)(nil),             // 54: api.monitor.v1.AuthorizedCameraInfo
	(*ClearClassCameraReq)(nil),              // 55: api.monitor.v1.ClearClassCameraReq
	(*ClearClassCameraRsp)(nil),              // 56: api.monitor.v1.ClearClassCameraRsp
	(*UpdateClassCameraReq)(nil),             // 57: api.monitor.v1.UpdateClassCameraReq
	(*UpdateClassCameraRsp)(nil),             // 58: api.monitor.v1.UpdateClassCameraRsp
	(*ClassInfo)(nil),                        // 59: api.monitor.v1.ClassInfo
	(*GetStaffCameraReq)(nil),                // 60: api.monitor.v1.GetStaffCameraReq
	(*StaffCameraAuthInfo)(nil),              // 61: api.monitor.v1.StaffCameraAuthInfo
	(*UpdateStaffCameraReq)(nil),             // 62: api.monitor.v1.UpdateStaffCameraReq
	(*UpdateStaffCameraRsp)(nil),             // 63: api.monitor.v1.UpdateStaffCameraRsp
	(*GetClassAccessTimeReq)(nil),            // 64: api.monitor.v1.GetClassAccessTimeReq
	(*GetClassAccessTimeRsp)(nil),            // 65: api.monitor.v1.GetClassAccessTimeRsp
	(*CameraAccessTimeInfo)(nil),             // 66: api.monitor.v1.CameraAccessTimeInfo
	(*AccessTimeInfo)(nil),                   // 67: api.monitor.v1.AccessTimeInfo
	(*AccessTime)(nil),                       // 68: api.monitor.v1.AccessTime
	(*AccessTimePeriod)(nil),                 // 69: api.monitor.v1.AccessTimePeriod
	(*ClassCamera)(nil),                      // 70: api.monitor.v1.ClassCamera
	(*UpdateClassAccessTimeReq)(nil),         // 71: api.monitor.v1.UpdateClassAccessTimeReq
	(*UpdateClassAccessTimeRsp)(nil),         // 72: api.monitor.v1.UpdateClassAccessTimeRsp
	(*ListMonitorDeviceReq)(nil),             // 73: api.monitor.v1.ListMonitorDeviceReq
	(*ListMonitorDeviceRsp)(nil),             // 74: api.monitor.v1.ListMonitorDeviceRsp
	(*MonitorDevice)(nil),                    // 75: api.monitor.v1.MonitorDevice
	(*GBConfig)(nil),                         // 76: api.monitor.v1.GBConfig
	(*Camera)(nil),                           // 77: api.monitor.v1.Camera
	(*CreateMonitorDeviceReq)(nil),           // 78: api.monitor.v1.CreateMonitorDeviceReq
	(*CreateMonitorDeviceRsp)(nil),           // 79: api.monitor.v1.CreateMonitorDeviceRsp
	(*UpdateMonitorDeviceReq)(nil),           // 80: api.monitor.v1.UpdateMonitorDeviceReq
	(*UpdateMonitorDeviceRsp)(nil),           // 81: api.monitor.v1.UpdateMonitorDeviceRsp
	(*DeleteMonitorDeviceReq)(nil),           // 82: api.monitor.v1.DeleteMonitorDeviceReq
	(*DeleteMonitorDeviceRsp)(nil),           // 83: api.monitor.v1.DeleteMonitorDeviceRsp
	(*GetMonitorDeviceStatusReq)(nil),        // 84: api.monitor.v1.GetMonitorDeviceStatusReq
	(*GetMonitorDeviceStatusRsp)(nil),        // 85: api.monitor.v1.GetMonitorDeviceStatusRsp
	(*MonitorCameraStatus)(nil),              // 86: api.monitor.v1.MonitorCameraStatus
	(*GetMonitorDeviceReq)(nil),              // 87: api.monitor.v1.GetMonitorDeviceReq
	(*CreateMonitorCameraReq)(nil),           // 88: api.monitor.v1.CreateMonitorCameraReq
	(*CreateMonitorCameraRsp)(nil),           // 89: api.monitor.v1.CreateMonitorCameraRsp
	(*UpdateMonitorCameraReq)(nil),           // 90: api.monitor.v1.UpdateMonitorCameraReq
	(*UpdateMonitorCameraRsp)(nil),           // 91: api.monitor.v1.UpdateMonitorCameraRsp
	(*DeleteMonitorCameraReq)(nil),           // 92: api.monitor.v1.DeleteMonitorCameraReq
	(*DeleteMonitorCameraRsp)(nil),           // 93: api.monitor.v1.DeleteMonitorCameraRsp
	(*QiniuToYsyReq)(nil),                    // 94: api.monitor.v1.QiniuToYsyReq
	(*QiniuToYsyRsp)(nil),                    // 95: api.monitor.v1.QiniuToYsyRsp
	(*YsyDeviceInfoExportReq)(nil),           // 96: api.monitor.v1.YsyDeviceInfoExportReq
	(*YsyDeviceInfoExportRsp)(nil),           // 97: api.monitor.v1.YsyDeviceInfoExportRsp
	(*SetLiveModelReq)(nil),                  // 98: api.monitor.v1.SetLiveModelReq
	(*SetLiveModelRsp)(nil),                  // 99: api.monitor.v1.SetLiveModelRsp
	(*QiniuBackReq)(nil),                     // 100: api.monitor.v1.QiniuBackReq
	(*QiniuBackRsp)(nil),                     // 101: api.monitor.v1.QiniuBackRsp
	(*CronCloseChannelReq)(nil),              // 102: api.monitor.v1.CronCloseChannelReq
	(*CronCloseChannelRsp)(nil),              // 103: api.monitor.v1.CronCloseChannelRsp
	nil,                                      // 104: api.monitor.v1.FindStaffCameraRsp.AuthMapEntry
	nil,                                      // 105: api.monitor.v1.FindClassCameraRsp.AuthMapEntry
}
var file_api_monitor_v1_monitor_proto_depIdxs = []int32{
	54,  // 0: api.monitor.v1.GetAuthorizedInstClassCameraRsp.authorized_camera_list:type_name -> api.monitor.v1.AuthorizedCameraInfo
	12,  // 1: api.monitor.v1.ListZxMonitorRsp.list:type_name -> api.monitor.v1.ZxMonitor
	17,  // 2: api.monitor.v1.GetChannelHistoryPlaybackRsp.list:type_name -> api.monitor.v1.PlaybackInfo
	20,  // 3: api.monitor.v1.StatsParentWatchRsp.list:type_name -> api.monitor.v1.ParentWatchStatsInfo
	23,  // 4: api.monitor.v1.StatsStudentWatchRsp.list:type_name -> api.monitor.v1.StudentWatchStatsInfo
	26,  // 5: api.monitor.v1.ListWatchRecordRsp.list:type_name -> api.monitor.v1.WatchRecord
	42,  // 6: api.monitor.v1.ListMonitorCameraOnlineStatusRsp.cameras:type_name -> api.monitor.v1.CameraDetailInfo
	35,  // 7: api.monitor.v1.GetWatchUserRsp.users:type_name -> api.monitor.v1.WatchUserInfo
	38,  // 8: api.monitor.v1.ListClassCameraAccessTimeRsp.access_list:type_name -> api.monitor.v1.CameraAccessInfo
	67,  // 9: api.monitor.v1.CameraAccessInfo.access_time_info:type_name -> api.monitor.v1.AccessTimeInfo
	41,  // 10: api.monitor.v1.ListMonitorCameraRsp.cameras:type_name -> api.monitor.v1.ClassCameraInfo
	42,  // 11: api.monitor.v1.ClassCameraInfo.cameras:type_name -> api.monitor.v1.CameraDetailInfo
	67,  // 12: api.monitor.v1.CameraDetailInfo.access_time_info:type_name -> api.monitor.v1.AccessTimeInfo
	104, // 13: api.monitor.v1.FindStaffCameraRsp.auth_map:type_name -> api.monitor.v1.FindStaffCameraRsp.AuthMapEntry
	105, // 14: api.monitor.v1.FindClassCameraRsp.auth_map:type_name -> api.monitor.v1.FindClassCameraRsp.AuthMapEntry
	54,  // 15: api.monitor.v1.ClassCameraAuthInfo.camera_list:type_name -> api.monitor.v1.AuthorizedCameraInfo
	54,  // 16: api.monitor.v1.StaffCameraAuthInfo.camera_list:type_name -> api.monitor.v1.AuthorizedCameraInfo
	66,  // 17: api.monitor.v1.GetClassAccessTimeRsp.camera_list:type_name -> api.monitor.v1.CameraAccessTimeInfo
	67,  // 18: api.monitor.v1.CameraAccessTimeInfo.access_info:type_name -> api.monitor.v1.AccessTimeInfo
	68,  // 19: api.monitor.v1.AccessTimeInfo.access_times:type_name -> api.monitor.v1.AccessTime
	69,  // 20: api.monitor.v1.AccessTime.list:type_name -> api.monitor.v1.AccessTimePeriod
	70,  // 21: api.monitor.v1.UpdateClassAccessTimeReq.class_cameras:type_name -> api.monitor.v1.ClassCamera
	67,  // 22: api.monitor.v1.UpdateClassAccessTimeReq.access_time_info:type_name -> api.monitor.v1.AccessTimeInfo
	75,  // 23: api.monitor.v1.ListMonitorDeviceRsp.list:type_name -> api.monitor.v1.MonitorDevice
	77,  // 24: api.monitor.v1.MonitorDevice.camera_list:type_name -> api.monitor.v1.Camera
	76,  // 25: api.monitor.v1.MonitorDevice.gb_config:type_name -> api.monitor.v1.GBConfig
	86,  // 26: api.monitor.v1.GetMonitorDeviceStatusRsp.list:type_name -> api.monitor.v1.MonitorCameraStatus
	61,  // 27: api.monitor.v1.FindStaffCameraRsp.AuthMapEntry.value:type_name -> api.monitor.v1.StaffCameraAuthInfo
	53,  // 28: api.monitor.v1.FindClassCameraRsp.AuthMapEntry.value:type_name -> api.monitor.v1.ClassCameraAuthInfo
	10,  // 29: api.monitor.v1.VideoMonitor.ListZxMonitor:input_type -> api.monitor.v1.ListZxMonitorReq
	8,   // 30: api.monitor.v1.VideoMonitor.MergeZxMonitorNode:input_type -> api.monitor.v1.MergeZxMonitorNodeReq
	6,   // 31: api.monitor.v1.VideoMonitor.UpdateZxMonitor:input_type -> api.monitor.v1.UpdateZxMonitorReq
	4,   // 32: api.monitor.v1.VideoMonitor.DeleteZxMonitor:input_type -> api.monitor.v1.DeleteZxMonitorReq
	2,   // 33: api.monitor.v1.VideoMonitor.SwitchZxMonitorQuality:input_type -> api.monitor.v1.SwitchZxMonitorQualityReq
	73,  // 34: api.monitor.v1.VideoMonitor.ListMonitorDevice:input_type -> api.monitor.v1.ListMonitorDeviceReq
	78,  // 35: api.monitor.v1.VideoMonitor.CreateMonitorDevice:input_type -> api.monitor.v1.CreateMonitorDeviceReq
	80,  // 36: api.monitor.v1.VideoMonitor.UpdateMonitorDevice:input_type -> api.monitor.v1.UpdateMonitorDeviceReq
	82,  // 37: api.monitor.v1.VideoMonitor.DeleteMonitorDevice:input_type -> api.monitor.v1.DeleteMonitorDeviceReq
	84,  // 38: api.monitor.v1.VideoMonitor.GetMonitorDeviceStatus:input_type -> api.monitor.v1.GetMonitorDeviceStatusReq
	87,  // 39: api.monitor.v1.VideoMonitor.GetMonitorDevice:input_type -> api.monitor.v1.GetMonitorDeviceReq
	88,  // 40: api.monitor.v1.VideoMonitor.CreateMonitorCamera:input_type -> api.monitor.v1.CreateMonitorCameraReq
	90,  // 41: api.monitor.v1.VideoMonitor.UpdateMonitorCamera:input_type -> api.monitor.v1.UpdateMonitorCameraReq
	92,  // 42: api.monitor.v1.VideoMonitor.DeleteMonitorCamera:input_type -> api.monitor.v1.DeleteMonitorCameraReq
	52,  // 43: api.monitor.v1.VideoMonitor.GetClassCamera:input_type -> api.monitor.v1.GetClassCameraReq
	57,  // 44: api.monitor.v1.VideoMonitor.UpdateClassCamera:input_type -> api.monitor.v1.UpdateClassCameraReq
	55,  // 45: api.monitor.v1.VideoMonitor.ClearClassCamera:input_type -> api.monitor.v1.ClearClassCameraReq
	60,  // 46: api.monitor.v1.VideoMonitor.GetStaffCamera:input_type -> api.monitor.v1.GetStaffCameraReq
	62,  // 47: api.monitor.v1.VideoMonitor.UpdateStaffCamera:input_type -> api.monitor.v1.UpdateStaffCameraReq
	64,  // 48: api.monitor.v1.VideoMonitor.GetClassAccessTime:input_type -> api.monitor.v1.GetClassAccessTimeReq
	71,  // 49: api.monitor.v1.VideoMonitor.UpdateClassAccessTime:input_type -> api.monitor.v1.UpdateClassAccessTimeReq
	49,  // 50: api.monitor.v1.VideoMonitor.GetClassIdsByCameraName:input_type -> api.monitor.v1.GetClassIdsByCameraNameReq
	47,  // 51: api.monitor.v1.VideoMonitor.FindClassCamera:input_type -> api.monitor.v1.FindClassCameraReq
	45,  // 52: api.monitor.v1.VideoMonitor.FindStaffCamera:input_type -> api.monitor.v1.FindStaffCameraReq
	43,  // 53: api.monitor.v1.VideoMonitor.GetMonitorCameraConfig:input_type -> api.monitor.v1.GetMonitorCameraConfigReq
	39,  // 54: api.monitor.v1.VideoMonitor.ListMonitorCamera:input_type -> api.monitor.v1.ListMonitorCameraReq
	31,  // 55: api.monitor.v1.VideoMonitor.ListMonitorCameraOnlineStatus:input_type -> api.monitor.v1.ListMonitorCameraOnlineStatusReq
	36,  // 56: api.monitor.v1.VideoMonitor.ListClassCameraAccessTime:input_type -> api.monitor.v1.ListClassCameraAccessTimeReq
	33,  // 57: api.monitor.v1.VideoMonitor.GetWatchUser:input_type -> api.monitor.v1.GetWatchUserReq
	29,  // 58: api.monitor.v1.VideoMonitor.ReportWatchRecord:input_type -> api.monitor.v1.ReportWatchRecordReq
	27,  // 59: api.monitor.v1.VideoMonitor.StatsTodayWatch:input_type -> api.monitor.v1.StatsTodayWatchReq
	24,  // 60: api.monitor.v1.VideoMonitor.ListWatchRecord:input_type -> api.monitor.v1.ListWatchRecordReq
	21,  // 61: api.monitor.v1.VideoMonitor.StatsStudentWatch:input_type -> api.monitor.v1.StatsStudentWatchReq
	18,  // 62: api.monitor.v1.VideoMonitor.StatsParentWatch:input_type -> api.monitor.v1.StatsParentWatchReq
	15,  // 63: api.monitor.v1.VideoMonitor.GetChannelHistoryPlayback:input_type -> api.monitor.v1.GetChannelHistoryPlaybackReq
	13,  // 64: api.monitor.v1.VideoMonitor.GetParentPaybackUrl:input_type -> api.monitor.v1.GetParentPaybackUrlReq
	94,  // 65: api.monitor.v1.VideoMonitor.QiniuToYsy:input_type -> api.monitor.v1.QiniuToYsyReq
	96,  // 66: api.monitor.v1.VideoMonitor.YsyDeviceInfoExport:input_type -> api.monitor.v1.YsyDeviceInfoExportReq
	0,   // 67: api.monitor.v1.VideoMonitor.GetAuthorizedInstClassCamera:input_type -> api.monitor.v1.GetAuthorizedInstClassCameraReq
	98,  // 68: api.monitor.v1.VideoMonitor.SetLiveModel:input_type -> api.monitor.v1.SetLiveModelReq
	100, // 69: api.monitor.v1.VideoMonitor.QiniuBack:input_type -> api.monitor.v1.QiniuBackReq
	102, // 70: api.monitor.v1.VideoMonitor.CronCloseChannel:input_type -> api.monitor.v1.CronCloseChannelReq
	11,  // 71: api.monitor.v1.VideoMonitor.ListZxMonitor:output_type -> api.monitor.v1.ListZxMonitorRsp
	9,   // 72: api.monitor.v1.VideoMonitor.MergeZxMonitorNode:output_type -> api.monitor.v1.MergeZxMonitorNodeRsp
	7,   // 73: api.monitor.v1.VideoMonitor.UpdateZxMonitor:output_type -> api.monitor.v1.UpdateZxMonitorRsp
	5,   // 74: api.monitor.v1.VideoMonitor.DeleteZxMonitor:output_type -> api.monitor.v1.DeleteZxMonitorRsp
	3,   // 75: api.monitor.v1.VideoMonitor.SwitchZxMonitorQuality:output_type -> api.monitor.v1.SwitchZxMonitorQualityRsp
	74,  // 76: api.monitor.v1.VideoMonitor.ListMonitorDevice:output_type -> api.monitor.v1.ListMonitorDeviceRsp
	79,  // 77: api.monitor.v1.VideoMonitor.CreateMonitorDevice:output_type -> api.monitor.v1.CreateMonitorDeviceRsp
	81,  // 78: api.monitor.v1.VideoMonitor.UpdateMonitorDevice:output_type -> api.monitor.v1.UpdateMonitorDeviceRsp
	83,  // 79: api.monitor.v1.VideoMonitor.DeleteMonitorDevice:output_type -> api.monitor.v1.DeleteMonitorDeviceRsp
	85,  // 80: api.monitor.v1.VideoMonitor.GetMonitorDeviceStatus:output_type -> api.monitor.v1.GetMonitorDeviceStatusRsp
	75,  // 81: api.monitor.v1.VideoMonitor.GetMonitorDevice:output_type -> api.monitor.v1.MonitorDevice
	89,  // 82: api.monitor.v1.VideoMonitor.CreateMonitorCamera:output_type -> api.monitor.v1.CreateMonitorCameraRsp
	91,  // 83: api.monitor.v1.VideoMonitor.UpdateMonitorCamera:output_type -> api.monitor.v1.UpdateMonitorCameraRsp
	93,  // 84: api.monitor.v1.VideoMonitor.DeleteMonitorCamera:output_type -> api.monitor.v1.DeleteMonitorCameraRsp
	53,  // 85: api.monitor.v1.VideoMonitor.GetClassCamera:output_type -> api.monitor.v1.ClassCameraAuthInfo
	58,  // 86: api.monitor.v1.VideoMonitor.UpdateClassCamera:output_type -> api.monitor.v1.UpdateClassCameraRsp
	56,  // 87: api.monitor.v1.VideoMonitor.ClearClassCamera:output_type -> api.monitor.v1.ClearClassCameraRsp
	61,  // 88: api.monitor.v1.VideoMonitor.GetStaffCamera:output_type -> api.monitor.v1.StaffCameraAuthInfo
	63,  // 89: api.monitor.v1.VideoMonitor.UpdateStaffCamera:output_type -> api.monitor.v1.UpdateStaffCameraRsp
	65,  // 90: api.monitor.v1.VideoMonitor.GetClassAccessTime:output_type -> api.monitor.v1.GetClassAccessTimeRsp
	72,  // 91: api.monitor.v1.VideoMonitor.UpdateClassAccessTime:output_type -> api.monitor.v1.UpdateClassAccessTimeRsp
	50,  // 92: api.monitor.v1.VideoMonitor.GetClassIdsByCameraName:output_type -> api.monitor.v1.GetClassIdsByCameraNameRsp
	48,  // 93: api.monitor.v1.VideoMonitor.FindClassCamera:output_type -> api.monitor.v1.FindClassCameraRsp
	46,  // 94: api.monitor.v1.VideoMonitor.FindStaffCamera:output_type -> api.monitor.v1.FindStaffCameraRsp
	44,  // 95: api.monitor.v1.VideoMonitor.GetMonitorCameraConfig:output_type -> api.monitor.v1.GetMonitorCameraConfigRsp
	40,  // 96: api.monitor.v1.VideoMonitor.ListMonitorCamera:output_type -> api.monitor.v1.ListMonitorCameraRsp
	32,  // 97: api.monitor.v1.VideoMonitor.ListMonitorCameraOnlineStatus:output_type -> api.monitor.v1.ListMonitorCameraOnlineStatusRsp
	37,  // 98: api.monitor.v1.VideoMonitor.ListClassCameraAccessTime:output_type -> api.monitor.v1.ListClassCameraAccessTimeRsp
	34,  // 99: api.monitor.v1.VideoMonitor.GetWatchUser:output_type -> api.monitor.v1.GetWatchUserRsp
	30,  // 100: api.monitor.v1.VideoMonitor.ReportWatchRecord:output_type -> api.monitor.v1.ReportWatchRecordRsp
	28,  // 101: api.monitor.v1.VideoMonitor.StatsTodayWatch:output_type -> api.monitor.v1.StatsTodayWatchRsp
	25,  // 102: api.monitor.v1.VideoMonitor.ListWatchRecord:output_type -> api.monitor.v1.ListWatchRecordRsp
	22,  // 103: api.monitor.v1.VideoMonitor.StatsStudentWatch:output_type -> api.monitor.v1.StatsStudentWatchRsp
	19,  // 104: api.monitor.v1.VideoMonitor.StatsParentWatch:output_type -> api.monitor.v1.StatsParentWatchRsp
	16,  // 105: api.monitor.v1.VideoMonitor.GetChannelHistoryPlayback:output_type -> api.monitor.v1.GetChannelHistoryPlaybackRsp
	14,  // 106: api.monitor.v1.VideoMonitor.GetParentPaybackUrl:output_type -> api.monitor.v1.GetParentPaybackUrlRsp
	95,  // 107: api.monitor.v1.VideoMonitor.QiniuToYsy:output_type -> api.monitor.v1.QiniuToYsyRsp
	97,  // 108: api.monitor.v1.VideoMonitor.YsyDeviceInfoExport:output_type -> api.monitor.v1.YsyDeviceInfoExportRsp
	1,   // 109: api.monitor.v1.VideoMonitor.GetAuthorizedInstClassCamera:output_type -> api.monitor.v1.GetAuthorizedInstClassCameraRsp
	99,  // 110: api.monitor.v1.VideoMonitor.SetLiveModel:output_type -> api.monitor.v1.SetLiveModelRsp
	101, // 111: api.monitor.v1.VideoMonitor.QiniuBack:output_type -> api.monitor.v1.QiniuBackRsp
	103, // 112: api.monitor.v1.VideoMonitor.CronCloseChannel:output_type -> api.monitor.v1.CronCloseChannelRsp
	71,  // [71:113] is the sub-list for method output_type
	29,  // [29:71] is the sub-list for method input_type
	29,  // [29:29] is the sub-list for extension type_name
	29,  // [29:29] is the sub-list for extension extendee
	0,   // [0:29] is the sub-list for field type_name
}

func init() { file_api_monitor_v1_monitor_proto_init() }
func file_api_monitor_v1_monitor_proto_init() {
	if File_api_monitor_v1_monitor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_monitor_v1_monitor_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetAuthorizedInstClassCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetAuthorizedInstClassCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SwitchZxMonitorQualityReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SwitchZxMonitorQualityRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteZxMonitorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteZxMonitorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateZxMonitorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateZxMonitorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*MergeZxMonitorNodeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*MergeZxMonitorNodeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*ListZxMonitorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ListZxMonitorRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ZxMonitor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetParentPaybackUrlReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GetParentPaybackUrlRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*GetChannelHistoryPlaybackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*GetChannelHistoryPlaybackRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*PlaybackInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*StatsParentWatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*StatsParentWatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*ParentWatchStatsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*StatsStudentWatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*StatsStudentWatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*StudentWatchStatsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*ListWatchRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*ListWatchRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*WatchRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*StatsTodayWatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*StatsTodayWatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*ReportWatchRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*ReportWatchRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*ListMonitorCameraOnlineStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*ListMonitorCameraOnlineStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*GetWatchUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*GetWatchUserRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*WatchUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*ListClassCameraAccessTimeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*ListClassCameraAccessTimeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*CameraAccessInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*ListMonitorCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*ListMonitorCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*ClassCameraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*CameraDetailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*GetMonitorCameraConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*GetMonitorCameraConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*FindStaffCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*FindStaffCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[47].Exporter = func(v any, i int) any {
			switch v := v.(*FindClassCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[48].Exporter = func(v any, i int) any {
			switch v := v.(*FindClassCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[49].Exporter = func(v any, i int) any {
			switch v := v.(*GetClassIdsByCameraNameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[50].Exporter = func(v any, i int) any {
			switch v := v.(*GetClassIdsByCameraNameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[51].Exporter = func(v any, i int) any {
			switch v := v.(*CameraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[52].Exporter = func(v any, i int) any {
			switch v := v.(*GetClassCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[53].Exporter = func(v any, i int) any {
			switch v := v.(*ClassCameraAuthInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[54].Exporter = func(v any, i int) any {
			switch v := v.(*AuthorizedCameraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[55].Exporter = func(v any, i int) any {
			switch v := v.(*ClearClassCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[56].Exporter = func(v any, i int) any {
			switch v := v.(*ClearClassCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[57].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateClassCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[58].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateClassCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[59].Exporter = func(v any, i int) any {
			switch v := v.(*ClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[60].Exporter = func(v any, i int) any {
			switch v := v.(*GetStaffCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[61].Exporter = func(v any, i int) any {
			switch v := v.(*StaffCameraAuthInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[62].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateStaffCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[63].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateStaffCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[64].Exporter = func(v any, i int) any {
			switch v := v.(*GetClassAccessTimeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[65].Exporter = func(v any, i int) any {
			switch v := v.(*GetClassAccessTimeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[66].Exporter = func(v any, i int) any {
			switch v := v.(*CameraAccessTimeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[67].Exporter = func(v any, i int) any {
			switch v := v.(*AccessTimeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[68].Exporter = func(v any, i int) any {
			switch v := v.(*AccessTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[69].Exporter = func(v any, i int) any {
			switch v := v.(*AccessTimePeriod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[70].Exporter = func(v any, i int) any {
			switch v := v.(*ClassCamera); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[71].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateClassAccessTimeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[72].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateClassAccessTimeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[73].Exporter = func(v any, i int) any {
			switch v := v.(*ListMonitorDeviceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[74].Exporter = func(v any, i int) any {
			switch v := v.(*ListMonitorDeviceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[75].Exporter = func(v any, i int) any {
			switch v := v.(*MonitorDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[76].Exporter = func(v any, i int) any {
			switch v := v.(*GBConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[77].Exporter = func(v any, i int) any {
			switch v := v.(*Camera); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[78].Exporter = func(v any, i int) any {
			switch v := v.(*CreateMonitorDeviceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[79].Exporter = func(v any, i int) any {
			switch v := v.(*CreateMonitorDeviceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[80].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateMonitorDeviceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[81].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateMonitorDeviceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[82].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteMonitorDeviceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[83].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteMonitorDeviceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[84].Exporter = func(v any, i int) any {
			switch v := v.(*GetMonitorDeviceStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[85].Exporter = func(v any, i int) any {
			switch v := v.(*GetMonitorDeviceStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[86].Exporter = func(v any, i int) any {
			switch v := v.(*MonitorCameraStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[87].Exporter = func(v any, i int) any {
			switch v := v.(*GetMonitorDeviceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[88].Exporter = func(v any, i int) any {
			switch v := v.(*CreateMonitorCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[89].Exporter = func(v any, i int) any {
			switch v := v.(*CreateMonitorCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[90].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateMonitorCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[91].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateMonitorCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[92].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteMonitorCameraReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[93].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteMonitorCameraRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[94].Exporter = func(v any, i int) any {
			switch v := v.(*QiniuToYsyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[95].Exporter = func(v any, i int) any {
			switch v := v.(*QiniuToYsyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[96].Exporter = func(v any, i int) any {
			switch v := v.(*YsyDeviceInfoExportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[97].Exporter = func(v any, i int) any {
			switch v := v.(*YsyDeviceInfoExportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[98].Exporter = func(v any, i int) any {
			switch v := v.(*SetLiveModelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[99].Exporter = func(v any, i int) any {
			switch v := v.(*SetLiveModelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[100].Exporter = func(v any, i int) any {
			switch v := v.(*QiniuBackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[101].Exporter = func(v any, i int) any {
			switch v := v.(*QiniuBackRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[102].Exporter = func(v any, i int) any {
			switch v := v.(*CronCloseChannelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_monitor_v1_monitor_proto_msgTypes[103].Exporter = func(v any, i int) any {
			switch v := v.(*CronCloseChannelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_monitor_v1_monitor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   106,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_monitor_v1_monitor_proto_goTypes,
		DependencyIndexes: file_api_monitor_v1_monitor_proto_depIdxs,
		MessageInfos:      file_api_monitor_v1_monitor_proto_msgTypes,
	}.Build()
	File_api_monitor_v1_monitor_proto = out.File
	file_api_monitor_v1_monitor_proto_rawDesc = nil
	file_api_monitor_v1_monitor_proto_goTypes = nil
	file_api_monitor_v1_monitor_proto_depIdxs = nil
}
