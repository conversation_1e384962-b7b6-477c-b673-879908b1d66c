// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.17.3
// source: monitor/v1/monitor_error_reason.proto

package v1

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	// 设备已被其他学校绑定
	ErrorReason_LIVE_MONITOR_DEVICE_IS_BOUND_BY_OTHER_SCHOOL ErrorReason = 0
	// 设备已绑定
	ErrorReason_LIVE_MONITOR_DEVICE_IS_BOUND ErrorReason = 1
	// 监控设备不存在
	ErrorReason_LIVE_MONITOR_DEVICE_NOT_EXIST ErrorReason = 2
	// 摄像头已绑定
	ErrorReason_LIVE_MONITOR_CAMERA_IS_BOUND ErrorReason = 3
	// 摄像头不存在
	ErrorReason_LIVE_MONITOR_CAMERA_NOT_EXIST ErrorReason = 4
	// 注册设备失败
	ErrorReason_LIVE_MONITOR_DEVICE_REGISTER_FAIL ErrorReason = 5
	// 设备未注册
	ErrorReason_LIVE_MONITOR_DEVICE_NOT_REGISTER ErrorReason = 6
	// 设备通道离线
	ErrorReason_LIVE_MONITOR_DEVICE_OFFLINE ErrorReason = 7
	// 推流失败
	ErrorReason_LIVE_MONITOR_PUSH_STREAM_FAIL ErrorReason = 8
	// 获取播放地址失败
	ErrorReason_LIVE_MONITOR_GET_STREAM_URL_FAIL ErrorReason = 9
	// 没有购买视频服务套餐
	ErrorReason_LIVE_MONITOR_NO_BUY_VIDEO_SERVICE ErrorReason = 10
	// 绑定摄像头数量超过购买的数量
	ErrorReason_LIVE_MONITOR_BIND_CAMERA_NUM_OVER_LIMIT ErrorReason = 11
	// 掌心宝贝摄像头不存在
	ErrorReason_LIVE_ZX_MONITOR_NOT_EXIST ErrorReason = 12
	// 掌心宝贝学校信息不存在
	ErrorReason_LIVE_ZX_COMP_INFO_NOT_EXIST ErrorReason = 13
	// 掌心宝贝云监控系统管理员账号不存在
	ErrorReason_LIVE_ZX_ADMIN_ACCOUNT_NOT_EXIST ErrorReason = 14
	// 掌心宝贝摄像头已离线
	ErrorReason_LIVE_ZX_MONITOR_OFFLINE ErrorReason = 15
	// 设备验证码为空
	ErrorReason_LIVE_MONITOR_DEVICE_VALIDATE_CODE_EMPTY ErrorReason = 16
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0:  "LIVE_MONITOR_DEVICE_IS_BOUND_BY_OTHER_SCHOOL",
		1:  "LIVE_MONITOR_DEVICE_IS_BOUND",
		2:  "LIVE_MONITOR_DEVICE_NOT_EXIST",
		3:  "LIVE_MONITOR_CAMERA_IS_BOUND",
		4:  "LIVE_MONITOR_CAMERA_NOT_EXIST",
		5:  "LIVE_MONITOR_DEVICE_REGISTER_FAIL",
		6:  "LIVE_MONITOR_DEVICE_NOT_REGISTER",
		7:  "LIVE_MONITOR_DEVICE_OFFLINE",
		8:  "LIVE_MONITOR_PUSH_STREAM_FAIL",
		9:  "LIVE_MONITOR_GET_STREAM_URL_FAIL",
		10: "LIVE_MONITOR_NO_BUY_VIDEO_SERVICE",
		11: "LIVE_MONITOR_BIND_CAMERA_NUM_OVER_LIMIT",
		12: "LIVE_ZX_MONITOR_NOT_EXIST",
		13: "LIVE_ZX_COMP_INFO_NOT_EXIST",
		14: "LIVE_ZX_ADMIN_ACCOUNT_NOT_EXIST",
		15: "LIVE_ZX_MONITOR_OFFLINE",
		16: "LIVE_MONITOR_DEVICE_VALIDATE_CODE_EMPTY",
	}
	ErrorReason_value = map[string]int32{
		"LIVE_MONITOR_DEVICE_IS_BOUND_BY_OTHER_SCHOOL": 0,
		"LIVE_MONITOR_DEVICE_IS_BOUND":                 1,
		"LIVE_MONITOR_DEVICE_NOT_EXIST":                2,
		"LIVE_MONITOR_CAMERA_IS_BOUND":                 3,
		"LIVE_MONITOR_CAMERA_NOT_EXIST":                4,
		"LIVE_MONITOR_DEVICE_REGISTER_FAIL":            5,
		"LIVE_MONITOR_DEVICE_NOT_REGISTER":             6,
		"LIVE_MONITOR_DEVICE_OFFLINE":                  7,
		"LIVE_MONITOR_PUSH_STREAM_FAIL":                8,
		"LIVE_MONITOR_GET_STREAM_URL_FAIL":             9,
		"LIVE_MONITOR_NO_BUY_VIDEO_SERVICE":            10,
		"LIVE_MONITOR_BIND_CAMERA_NUM_OVER_LIMIT":      11,
		"LIVE_ZX_MONITOR_NOT_EXIST":                    12,
		"LIVE_ZX_COMP_INFO_NOT_EXIST":                  13,
		"LIVE_ZX_ADMIN_ACCOUNT_NOT_EXIST":              14,
		"LIVE_ZX_MONITOR_OFFLINE":                      15,
		"LIVE_MONITOR_DEVICE_VALIDATE_CODE_EMPTY":      16,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_monitor_v1_monitor_error_reason_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_monitor_v1_monitor_error_reason_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_monitor_v1_monitor_error_reason_proto_rawDescGZIP(), []int{0}
}

var File_monitor_v1_monitor_error_reason_proto protoreflect.FileDescriptor

var file_monitor_v1_monitor_error_reason_proto_rawDesc = []byte{
	0x0a, 0x25, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x13, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0xef, 0x05, 0x0a,
	0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x2c,
	0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x42, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x42, 0x59, 0x5f,
	0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x48, 0x4f, 0x4f, 0x4c, 0x10, 0x00, 0x1a, 0x04,
	0xa8, 0x45, 0x90, 0x03, 0x12, 0x26, 0x0a, 0x1c, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f, 0x4e,
	0x49, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x42,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x27, 0x0a, 0x1d,
	0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x02, 0x1a,
	0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x26, 0x0a, 0x1c, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f,
	0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4d, 0x45, 0x52, 0x41, 0x5f, 0x49, 0x53, 0x5f,
	0x42, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x03, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x27, 0x0a,
	0x1d, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x41,
	0x4d, 0x45, 0x52, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x04,
	0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x2b, 0x0a, 0x21, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d,
	0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x52, 0x45,
	0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x05, 0x1a, 0x04, 0xa8,
	0x45, 0x90, 0x03, 0x12, 0x2a, 0x0a, 0x20, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x49,
	0x54, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52,
	0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x10, 0x06, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12,
	0x25, 0x0a, 0x1b, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f,
	0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x07,
	0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x27, 0x0a, 0x1d, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d,
	0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x50, 0x55, 0x53, 0x48, 0x5f, 0x53, 0x54, 0x52, 0x45,
	0x41, 0x4d, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x08, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12,
	0x2a, 0x0a, 0x20, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f,
	0x47, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x10, 0x09, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x2b, 0x0a, 0x21, 0x4c,
	0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x5f, 0x42,
	0x55, 0x59, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45,
	0x10, 0x0a, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x31, 0x0a, 0x27, 0x4c, 0x49, 0x56, 0x45,
	0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x5f, 0x43, 0x41,
	0x4d, 0x45, 0x52, 0x41, 0x5f, 0x4e, 0x55, 0x4d, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x10, 0x0b, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x23, 0x0a, 0x19, 0x4c,
	0x49, 0x56, 0x45, 0x5f, 0x5a, 0x58, 0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x0c, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03,
	0x12, 0x25, 0x0a, 0x1b, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x5a, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10,
	0x0d, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x29, 0x0a, 0x1f, 0x4c, 0x49, 0x56, 0x45, 0x5f,
	0x5a, 0x58, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x0e, 0x1a, 0x04, 0xa8, 0x45,
	0x90, 0x03, 0x12, 0x21, 0x0a, 0x17, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x5a, 0x58, 0x5f, 0x4d, 0x4f,
	0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x0f, 0x1a,
	0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x31, 0x0a, 0x27, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x4f,
	0x4e, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59,
	0x10, 0x10, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x1a, 0x04, 0xa0, 0x45, 0xf4, 0x03, 0x42, 0x13,
	0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_monitor_v1_monitor_error_reason_proto_rawDescOnce sync.Once
	file_monitor_v1_monitor_error_reason_proto_rawDescData = file_monitor_v1_monitor_error_reason_proto_rawDesc
)

func file_monitor_v1_monitor_error_reason_proto_rawDescGZIP() []byte {
	file_monitor_v1_monitor_error_reason_proto_rawDescOnce.Do(func() {
		file_monitor_v1_monitor_error_reason_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_v1_monitor_error_reason_proto_rawDescData)
	})
	return file_monitor_v1_monitor_error_reason_proto_rawDescData
}

var file_monitor_v1_monitor_error_reason_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_monitor_v1_monitor_error_reason_proto_goTypes = []interface{}{
	(ErrorReason)(0), // 0: api.monitor.v1.ErrorReason
}
var file_monitor_v1_monitor_error_reason_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_monitor_v1_monitor_error_reason_proto_init() }
func file_monitor_v1_monitor_error_reason_proto_init() {
	if File_monitor_v1_monitor_error_reason_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_v1_monitor_error_reason_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_monitor_v1_monitor_error_reason_proto_goTypes,
		DependencyIndexes: file_monitor_v1_monitor_error_reason_proto_depIdxs,
		EnumInfos:         file_monitor_v1_monitor_error_reason_proto_enumTypes,
	}.Build()
	File_monitor_v1_monitor_error_reason_proto = out.File
	file_monitor_v1_monitor_error_reason_proto_rawDesc = nil
	file_monitor_v1_monitor_error_reason_proto_goTypes = nil
	file_monitor_v1_monitor_error_reason_proto_depIdxs = nil
}
