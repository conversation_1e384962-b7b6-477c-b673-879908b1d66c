syntax = "proto3";

package api.monitor.v1;

import "errors/errors.proto";

option go_package = "api/monitor/v1;v1";

// kratos proto client ./api/monitor/v1/monitor_error_reason.proto

enum ErrorReason {
  // 设置缺省错误码
  option (errors.default_code) = 500;

  // 设备已被其他学校绑定
  LIVE_MONITOR_DEVICE_IS_BOUND_BY_OTHER_SCHOOL = 0 [(errors.code) = 400];

  // 设备已绑定
  LIVE_MONITOR_DEVICE_IS_BOUND = 1 [(errors.code) = 400];

  // 监控设备不存在
  LIVE_MONITOR_DEVICE_NOT_EXIST = 2 [(errors.code) = 400];

  // 摄像头已绑定
  LIVE_MONITOR_CAMERA_IS_BOUND = 3 [(errors.code) = 400];

  // 摄像头不存在
  LIVE_MONITOR_CAMERA_NOT_EXIST = 4 [(errors.code) = 400];

  // 注册设备失败
  LIVE_MONITOR_DEVICE_REGISTER_FAIL = 5 [(errors.code) = 400];

  // 设备未注册
  LIVE_MONITOR_DEVICE_NOT_REGISTER = 6 [(errors.code) = 400];

  // 设备通道离线
  LIVE_MONITOR_DEVICE_OFFLINE = 7 [(errors.code) = 400];

  // 推流失败
  LIVE_MONITOR_PUSH_STREAM_FAIL = 8 [(errors.code) = 400];

  // 获取播放地址失败
  LIVE_MONITOR_GET_STREAM_URL_FAIL = 9 [(errors.code) = 400];

  // 没有购买视频服务套餐
  LIVE_MONITOR_NO_BUY_VIDEO_SERVICE = 10 [(errors.code) = 400];

  // 绑定摄像头数量超过购买的数量
  LIVE_MONITOR_BIND_CAMERA_NUM_OVER_LIMIT = 11 [(errors.code) = 400];

  // 掌心宝贝摄像头不存在
  LIVE_ZX_MONITOR_NOT_EXIST = 12 [(errors.code) = 400];

  // 掌心宝贝学校信息不存在
  LIVE_ZX_COMP_INFO_NOT_EXIST = 13 [(errors.code) = 400];

  // 掌心宝贝云监控系统管理员账号不存在
  LIVE_ZX_ADMIN_ACCOUNT_NOT_EXIST = 14 [(errors.code) = 400];

  // 掌心宝贝摄像头已离线
  LIVE_ZX_MONITOR_OFFLINE = 15 [(errors.code) = 400];

  // 设备验证码为空
  LIVE_MONITOR_DEVICE_VALIDATE_CODE_EMPTY = 16 [(errors.code) = 400];
}
