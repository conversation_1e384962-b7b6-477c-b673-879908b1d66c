syntax = "proto3";

package api.monitor.v1;
option go_package = "api/monitor/v1;v1";

import "google/protobuf/empty.proto";
import "validate/validate.proto";

// kratos proto client ./api/monitor/v1/video_integration.proto
// kratos proto server ./api/monitor/v1/video_integration.proto

// 监控设备平台对接服务
service VideoIntegration {
    // 注册设备
    rpc RegisterDevice(RegisterDeviceReq) returns (RegisterDeviceRsp) {}

    // 解绑设备
    rpc UnRegisterDevice(UnRegisterDeviceReq) returns (UnRegisterDeviceRsp) {}

    // 播放地址
    rpc Play(PlayReq) returns (PlayRsp) {}
    // 上报信息
    rpc Report(ReportReq) returns (google.protobuf.Empty) {}

    // 七牛回调
    rpc QiniuQvsCallBack(QnCallBackReq) returns (google.protobuf.Empty) {}

    // 同步学校信息到掌心宝贝云监控平台
    rpc SyncToAncda(SyncToAncdaReq) returns (SyncToAncdaRsp) {}
}

// 同步学校信息到掌心宝贝云监控平台请求参数
message SyncToAncdaReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gte = 1];
}

// 同步学校信息到掌心宝贝云监控平台返回参数
message SyncToAncdaRsp {
}

// 注册设备请求参数
message RegisterDeviceReq {
  // 设备名称
  string name = 1;
  // 设备序列号
  string serial_no = 2;
  // 设备类型 (1：IPC摄像头，2：NVR)
  int32 type = 3;
  // 通道数
  int32 channel_num = 4;
  // 接入平台（1：Ehome（占位，EHome不需要注册）， 2：国标）
  int32 platform = 5;
}

// 注册设备返回参数
message RegisterDeviceRsp {
}

// 解绑设备请求参数
message UnRegisterDeviceReq {
  // 设备序列号
  string serial_no = 1;
  // 接入平台（1：Ehome（占位，EHome不需要解绑）， 2：国标）
  int32 platform = 2;
}

// 解绑设备返回参数
message UnRegisterDeviceRsp {
}


//------->>>>>>>------播放地址------<<<<<<<&&&&>>>>>>---MaWei@2023-07-28 08:49----<<<<<<----//
message PlayReq {
    // 机构Id
    int64 inst_id = 1 [(validate.rules).int64.gte = 1];
    // 用户ID
    int64 user_id = 2 [(validate.rules).int64.gte = 1];
    // 用户角色 1老师  3家长
    int32 user_role = 3 [(validate.rules).int32 = {in: [1, 2, 3]}];
    // 学生
    int64 student_id = 4 ;
    // liveid
    string live_id = 5 [(validate.rules).string.min_len = 1];
    // ip
    string ip = 6 [(validate.rules).string.min_len = 1];
}
message PlayRsp {
    // 播放地址
    string rtmp_url = 1;
    // liveid
    string live_id = 2;
    // 在线状态
    int32 online_status = 3;
    // 服务时间
    int64 service_time = 4;
}


//------->>>>>>>------进入播放------<<<<<<<&&&&>>>>>>---MaWei@2023-07-28 20:36----<<<<<<----//
message ReportReq {
    // 机构Id
    int64 inst_id = 1 [(validate.rules).int64.gte = 1];
    // 用户ID
    int64 user_id = 2 [(validate.rules).int64.gte = 1];
    // 用户角色 1老师  3家长
    int32 user_role = 3 [(validate.rules).int32 = {in: [1, 2, 3]}];
    // 学生
    int64 student_id = 4 ;
    // liveid
    string live_id = 5 [(validate.rules).string.min_len = 1];
    // 上报类型（1：进入房间，2：退出房间）
    int32 report_type = 6;
    // 注册平台
    int32 platform = 7;
    // 视频直播快照
    string snapshot_url = 8;
}

//------->>>>>>>------七牛回调------<<<<<<<&&&&>>>>>>---MaWei@2023-07-28 08:49----<<<<<<----//
message QnCallBackReq {
    // 类型
    string type = 1;
    // 空间id
    string ns_id = 2;
    // 国标id
    string gb_id = 3;
    // 频道id
    string ch_gb_id = 4;
    // 设备状态
    string device_state = 5;
    // 时间
    int64 time_sec = 6;
    // 请求id
    string req_id = 7;
    // 流id
    string stream_id = 8;
    // 流状态
    int32 stream_status = 9;
    // code
    int32 code = 10;
    // 截图
    repeated SnapItems snap_items = 11;
    // 错误
    string errmsg = 12;
    // 描述
    string desc = 13;
}
message SnapItems {
    // code 
    int32 code = 1;
    // 错误
    string error = 2;
    // 截图
    string fname = 3;
}
