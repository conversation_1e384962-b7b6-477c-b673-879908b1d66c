// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/monitor/v1/video_integration.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SyncToAncdaReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SyncToAncdaReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncToAncdaReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SyncToAncdaReqMultiError,
// or nil if none found.
func (m *SyncToAncdaReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncToAncdaReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetInstId() < 1 {
		err := SyncToAncdaReqValidationError{
			field:  "InstId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SyncToAncdaReqMultiError(errors)
	}

	return nil
}

// SyncToAncdaReqMultiError is an error wrapping multiple validation errors
// returned by SyncToAncdaReq.ValidateAll() if the designated constraints
// aren't met.
type SyncToAncdaReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncToAncdaReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncToAncdaReqMultiError) AllErrors() []error { return m }

// SyncToAncdaReqValidationError is the validation error returned by
// SyncToAncdaReq.Validate if the designated constraints aren't met.
type SyncToAncdaReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncToAncdaReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncToAncdaReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncToAncdaReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncToAncdaReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncToAncdaReqValidationError) ErrorName() string { return "SyncToAncdaReqValidationError" }

// Error satisfies the builtin error interface
func (e SyncToAncdaReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncToAncdaReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncToAncdaReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncToAncdaReqValidationError{}

// Validate checks the field values on SyncToAncdaRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SyncToAncdaRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncToAncdaRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SyncToAncdaRspMultiError,
// or nil if none found.
func (m *SyncToAncdaRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncToAncdaRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SyncToAncdaRspMultiError(errors)
	}

	return nil
}

// SyncToAncdaRspMultiError is an error wrapping multiple validation errors
// returned by SyncToAncdaRsp.ValidateAll() if the designated constraints
// aren't met.
type SyncToAncdaRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncToAncdaRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncToAncdaRspMultiError) AllErrors() []error { return m }

// SyncToAncdaRspValidationError is the validation error returned by
// SyncToAncdaRsp.Validate if the designated constraints aren't met.
type SyncToAncdaRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncToAncdaRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncToAncdaRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncToAncdaRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncToAncdaRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncToAncdaRspValidationError) ErrorName() string { return "SyncToAncdaRspValidationError" }

// Error satisfies the builtin error interface
func (e SyncToAncdaRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncToAncdaRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncToAncdaRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncToAncdaRspValidationError{}

// Validate checks the field values on RegisterDeviceReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RegisterDeviceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterDeviceReqMultiError, or nil if none found.
func (m *RegisterDeviceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterDeviceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for SerialNo

	// no validation rules for Type

	// no validation rules for ChannelNum

	// no validation rules for Platform

	if len(errors) > 0 {
		return RegisterDeviceReqMultiError(errors)
	}

	return nil
}

// RegisterDeviceReqMultiError is an error wrapping multiple validation errors
// returned by RegisterDeviceReq.ValidateAll() if the designated constraints
// aren't met.
type RegisterDeviceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterDeviceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterDeviceReqMultiError) AllErrors() []error { return m }

// RegisterDeviceReqValidationError is the validation error returned by
// RegisterDeviceReq.Validate if the designated constraints aren't met.
type RegisterDeviceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterDeviceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterDeviceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterDeviceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterDeviceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterDeviceReqValidationError) ErrorName() string {
	return "RegisterDeviceReqValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterDeviceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterDeviceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterDeviceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterDeviceReqValidationError{}

// Validate checks the field values on RegisterDeviceRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RegisterDeviceRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterDeviceRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterDeviceRspMultiError, or nil if none found.
func (m *RegisterDeviceRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterDeviceRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RegisterDeviceRspMultiError(errors)
	}

	return nil
}

// RegisterDeviceRspMultiError is an error wrapping multiple validation errors
// returned by RegisterDeviceRsp.ValidateAll() if the designated constraints
// aren't met.
type RegisterDeviceRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterDeviceRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterDeviceRspMultiError) AllErrors() []error { return m }

// RegisterDeviceRspValidationError is the validation error returned by
// RegisterDeviceRsp.Validate if the designated constraints aren't met.
type RegisterDeviceRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterDeviceRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterDeviceRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterDeviceRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterDeviceRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterDeviceRspValidationError) ErrorName() string {
	return "RegisterDeviceRspValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterDeviceRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterDeviceRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterDeviceRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterDeviceRspValidationError{}

// Validate checks the field values on UnRegisterDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnRegisterDeviceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnRegisterDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnRegisterDeviceReqMultiError, or nil if none found.
func (m *UnRegisterDeviceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnRegisterDeviceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SerialNo

	// no validation rules for Platform

	if len(errors) > 0 {
		return UnRegisterDeviceReqMultiError(errors)
	}

	return nil
}

// UnRegisterDeviceReqMultiError is an error wrapping multiple validation
// errors returned by UnRegisterDeviceReq.ValidateAll() if the designated
// constraints aren't met.
type UnRegisterDeviceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnRegisterDeviceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnRegisterDeviceReqMultiError) AllErrors() []error { return m }

// UnRegisterDeviceReqValidationError is the validation error returned by
// UnRegisterDeviceReq.Validate if the designated constraints aren't met.
type UnRegisterDeviceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnRegisterDeviceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnRegisterDeviceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnRegisterDeviceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnRegisterDeviceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnRegisterDeviceReqValidationError) ErrorName() string {
	return "UnRegisterDeviceReqValidationError"
}

// Error satisfies the builtin error interface
func (e UnRegisterDeviceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnRegisterDeviceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnRegisterDeviceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnRegisterDeviceReqValidationError{}

// Validate checks the field values on UnRegisterDeviceRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnRegisterDeviceRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnRegisterDeviceRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnRegisterDeviceRspMultiError, or nil if none found.
func (m *UnRegisterDeviceRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UnRegisterDeviceRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UnRegisterDeviceRspMultiError(errors)
	}

	return nil
}

// UnRegisterDeviceRspMultiError is an error wrapping multiple validation
// errors returned by UnRegisterDeviceRsp.ValidateAll() if the designated
// constraints aren't met.
type UnRegisterDeviceRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnRegisterDeviceRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnRegisterDeviceRspMultiError) AllErrors() []error { return m }

// UnRegisterDeviceRspValidationError is the validation error returned by
// UnRegisterDeviceRsp.Validate if the designated constraints aren't met.
type UnRegisterDeviceRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnRegisterDeviceRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnRegisterDeviceRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnRegisterDeviceRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnRegisterDeviceRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnRegisterDeviceRspValidationError) ErrorName() string {
	return "UnRegisterDeviceRspValidationError"
}

// Error satisfies the builtin error interface
func (e UnRegisterDeviceRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnRegisterDeviceRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnRegisterDeviceRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnRegisterDeviceRspValidationError{}

// Validate checks the field values on PlayReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PlayReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlayReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PlayReqMultiError, or nil if none found.
func (m *PlayReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PlayReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetInstId() < 1 {
		err := PlayReqValidationError{
			field:  "InstId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetUserId() < 1 {
		err := PlayReqValidationError{
			field:  "UserId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _PlayReq_UserRole_InLookup[m.GetUserRole()]; !ok {
		err := PlayReqValidationError{
			field:  "UserRole",
			reason: "value must be in list [1 2 3]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for StudentId

	if utf8.RuneCountInString(m.GetLiveId()) < 1 {
		err := PlayReqValidationError{
			field:  "LiveId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIp()) < 1 {
		err := PlayReqValidationError{
			field:  "Ip",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PlayReqMultiError(errors)
	}

	return nil
}

// PlayReqMultiError is an error wrapping multiple validation errors returned
// by PlayReq.ValidateAll() if the designated constraints aren't met.
type PlayReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlayReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlayReqMultiError) AllErrors() []error { return m }

// PlayReqValidationError is the validation error returned by PlayReq.Validate
// if the designated constraints aren't met.
type PlayReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlayReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlayReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlayReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlayReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlayReqValidationError) ErrorName() string { return "PlayReqValidationError" }

// Error satisfies the builtin error interface
func (e PlayReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlayReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlayReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlayReqValidationError{}

var _PlayReq_UserRole_InLookup = map[int32]struct{}{
	1: {},
	2: {},
	3: {},
}

// Validate checks the field values on PlayRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PlayRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlayRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PlayRspMultiError, or nil if none found.
func (m *PlayRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *PlayRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RtmpUrl

	// no validation rules for LiveId

	// no validation rules for OnlineStatus

	// no validation rules for ServiceTime

	if len(errors) > 0 {
		return PlayRspMultiError(errors)
	}

	return nil
}

// PlayRspMultiError is an error wrapping multiple validation errors returned
// by PlayRsp.ValidateAll() if the designated constraints aren't met.
type PlayRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlayRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlayRspMultiError) AllErrors() []error { return m }

// PlayRspValidationError is the validation error returned by PlayRsp.Validate
// if the designated constraints aren't met.
type PlayRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlayRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlayRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlayRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlayRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlayRspValidationError) ErrorName() string { return "PlayRspValidationError" }

// Error satisfies the builtin error interface
func (e PlayRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlayRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlayRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlayRspValidationError{}

// Validate checks the field values on ReportReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReportReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReportReqMultiError, or nil
// if none found.
func (m *ReportReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetInstId() < 1 {
		err := ReportReqValidationError{
			field:  "InstId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetUserId() < 1 {
		err := ReportReqValidationError{
			field:  "UserId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ReportReq_UserRole_InLookup[m.GetUserRole()]; !ok {
		err := ReportReqValidationError{
			field:  "UserRole",
			reason: "value must be in list [1 2 3]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for StudentId

	if utf8.RuneCountInString(m.GetLiveId()) < 1 {
		err := ReportReqValidationError{
			field:  "LiveId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ReportType

	// no validation rules for Platform

	// no validation rules for SnapshotUrl

	if len(errors) > 0 {
		return ReportReqMultiError(errors)
	}

	return nil
}

// ReportReqMultiError is an error wrapping multiple validation errors returned
// by ReportReq.ValidateAll() if the designated constraints aren't met.
type ReportReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportReqMultiError) AllErrors() []error { return m }

// ReportReqValidationError is the validation error returned by
// ReportReq.Validate if the designated constraints aren't met.
type ReportReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportReqValidationError) ErrorName() string { return "ReportReqValidationError" }

// Error satisfies the builtin error interface
func (e ReportReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportReqValidationError{}

var _ReportReq_UserRole_InLookup = map[int32]struct{}{
	1: {},
	2: {},
	3: {},
}

// Validate checks the field values on QnCallBackReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QnCallBackReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QnCallBackReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QnCallBackReqMultiError, or
// nil if none found.
func (m *QnCallBackReq) ValidateAll() error {
	return m.validate(true)
}

func (m *QnCallBackReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for NsId

	// no validation rules for GbId

	// no validation rules for ChGbId

	// no validation rules for DeviceState

	// no validation rules for TimeSec

	// no validation rules for ReqId

	// no validation rules for StreamId

	// no validation rules for StreamStatus

	// no validation rules for Code

	for idx, item := range m.GetSnapItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QnCallBackReqValidationError{
						field:  fmt.Sprintf("SnapItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QnCallBackReqValidationError{
						field:  fmt.Sprintf("SnapItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QnCallBackReqValidationError{
					field:  fmt.Sprintf("SnapItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Errmsg

	// no validation rules for Desc

	if len(errors) > 0 {
		return QnCallBackReqMultiError(errors)
	}

	return nil
}

// QnCallBackReqMultiError is an error wrapping multiple validation errors
// returned by QnCallBackReq.ValidateAll() if the designated constraints
// aren't met.
type QnCallBackReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QnCallBackReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QnCallBackReqMultiError) AllErrors() []error { return m }

// QnCallBackReqValidationError is the validation error returned by
// QnCallBackReq.Validate if the designated constraints aren't met.
type QnCallBackReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QnCallBackReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QnCallBackReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QnCallBackReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QnCallBackReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QnCallBackReqValidationError) ErrorName() string { return "QnCallBackReqValidationError" }

// Error satisfies the builtin error interface
func (e QnCallBackReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQnCallBackReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QnCallBackReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QnCallBackReqValidationError{}

// Validate checks the field values on SnapItems with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SnapItems) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SnapItems with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SnapItemsMultiError, or nil
// if none found.
func (m *SnapItems) ValidateAll() error {
	return m.validate(true)
}

func (m *SnapItems) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Error

	// no validation rules for Fname

	if len(errors) > 0 {
		return SnapItemsMultiError(errors)
	}

	return nil
}

// SnapItemsMultiError is an error wrapping multiple validation errors returned
// by SnapItems.ValidateAll() if the designated constraints aren't met.
type SnapItemsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SnapItemsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SnapItemsMultiError) AllErrors() []error { return m }

// SnapItemsValidationError is the validation error returned by
// SnapItems.Validate if the designated constraints aren't met.
type SnapItemsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SnapItemsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SnapItemsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SnapItemsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SnapItemsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SnapItemsValidationError) ErrorName() string { return "SnapItemsValidationError" }

// Error satisfies the builtin error interface
func (e SnapItemsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSnapItems.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SnapItemsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SnapItemsValidationError{}
