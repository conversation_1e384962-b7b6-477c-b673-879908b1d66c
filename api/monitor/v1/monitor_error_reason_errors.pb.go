// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package v1

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 设备已被其他学校绑定
func IsLiveMonitorDeviceIsBoundByOtherSchool(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_DEVICE_IS_BOUND_BY_OTHER_SCHOOL.String() && e.Code == 400
}

// 设备已被其他学校绑定
func ErrorLiveMonitorDeviceIsBoundByOtherSchool(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_DEVICE_IS_BOUND_BY_OTHER_SCHOOL.String(), fmt.Sprintf(format, args...))
}

// 设备已绑定
func IsLiveMonitorDeviceIsBound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_DEVICE_IS_BOUND.String() && e.Code == 400
}

// 设备已绑定
func ErrorLiveMonitorDeviceIsBound(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_DEVICE_IS_BOUND.String(), fmt.Sprintf(format, args...))
}

// 监控设备不存在
func IsLiveMonitorDeviceNotExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_DEVICE_NOT_EXIST.String() && e.Code == 400
}

// 监控设备不存在
func ErrorLiveMonitorDeviceNotExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_DEVICE_NOT_EXIST.String(), fmt.Sprintf(format, args...))
}

// 摄像头已绑定
func IsLiveMonitorCameraIsBound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_CAMERA_IS_BOUND.String() && e.Code == 400
}

// 摄像头已绑定
func ErrorLiveMonitorCameraIsBound(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_CAMERA_IS_BOUND.String(), fmt.Sprintf(format, args...))
}

// 摄像头不存在
func IsLiveMonitorCameraNotExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_CAMERA_NOT_EXIST.String() && e.Code == 400
}

// 摄像头不存在
func ErrorLiveMonitorCameraNotExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_CAMERA_NOT_EXIST.String(), fmt.Sprintf(format, args...))
}

// 注册设备失败
func IsLiveMonitorDeviceRegisterFail(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_DEVICE_REGISTER_FAIL.String() && e.Code == 400
}

// 注册设备失败
func ErrorLiveMonitorDeviceRegisterFail(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_DEVICE_REGISTER_FAIL.String(), fmt.Sprintf(format, args...))
}

// 设备未注册
func IsLiveMonitorDeviceNotRegister(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_DEVICE_NOT_REGISTER.String() && e.Code == 400
}

// 设备未注册
func ErrorLiveMonitorDeviceNotRegister(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_DEVICE_NOT_REGISTER.String(), fmt.Sprintf(format, args...))
}

// 设备通道离线
func IsLiveMonitorDeviceOffline(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_DEVICE_OFFLINE.String() && e.Code == 400
}

// 设备通道离线
func ErrorLiveMonitorDeviceOffline(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_DEVICE_OFFLINE.String(), fmt.Sprintf(format, args...))
}

// 推流失败
func IsLiveMonitorPushStreamFail(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_PUSH_STREAM_FAIL.String() && e.Code == 400
}

// 推流失败
func ErrorLiveMonitorPushStreamFail(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_PUSH_STREAM_FAIL.String(), fmt.Sprintf(format, args...))
}

// 获取播放地址失败
func IsLiveMonitorGetStreamUrlFail(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_GET_STREAM_URL_FAIL.String() && e.Code == 400
}

// 获取播放地址失败
func ErrorLiveMonitorGetStreamUrlFail(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_GET_STREAM_URL_FAIL.String(), fmt.Sprintf(format, args...))
}

// 没有购买视频服务套餐
func IsLiveMonitorNoBuyVideoService(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_NO_BUY_VIDEO_SERVICE.String() && e.Code == 400
}

// 没有购买视频服务套餐
func ErrorLiveMonitorNoBuyVideoService(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_NO_BUY_VIDEO_SERVICE.String(), fmt.Sprintf(format, args...))
}

// 绑定摄像头数量超过购买的数量
func IsLiveMonitorBindCameraNumOverLimit(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_BIND_CAMERA_NUM_OVER_LIMIT.String() && e.Code == 400
}

// 绑定摄像头数量超过购买的数量
func ErrorLiveMonitorBindCameraNumOverLimit(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_BIND_CAMERA_NUM_OVER_LIMIT.String(), fmt.Sprintf(format, args...))
}

// 掌心宝贝摄像头不存在
func IsLiveZxMonitorNotExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_ZX_MONITOR_NOT_EXIST.String() && e.Code == 400
}

// 掌心宝贝摄像头不存在
func ErrorLiveZxMonitorNotExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_ZX_MONITOR_NOT_EXIST.String(), fmt.Sprintf(format, args...))
}

// 掌心宝贝学校信息不存在
func IsLiveZxCompInfoNotExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_ZX_COMP_INFO_NOT_EXIST.String() && e.Code == 400
}

// 掌心宝贝学校信息不存在
func ErrorLiveZxCompInfoNotExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_ZX_COMP_INFO_NOT_EXIST.String(), fmt.Sprintf(format, args...))
}

// 掌心宝贝云监控系统管理员账号不存在
func IsLiveZxAdminAccountNotExist(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_ZX_ADMIN_ACCOUNT_NOT_EXIST.String() && e.Code == 400
}

// 掌心宝贝云监控系统管理员账号不存在
func ErrorLiveZxAdminAccountNotExist(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_ZX_ADMIN_ACCOUNT_NOT_EXIST.String(), fmt.Sprintf(format, args...))
}

// 掌心宝贝摄像头已离线
func IsLiveZxMonitorOffline(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_ZX_MONITOR_OFFLINE.String() && e.Code == 400
}

// 掌心宝贝摄像头已离线
func ErrorLiveZxMonitorOffline(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_ZX_MONITOR_OFFLINE.String(), fmt.Sprintf(format, args...))
}

// 设备验证码为空
func IsLiveMonitorDeviceValidateCodeEmpty(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LIVE_MONITOR_DEVICE_VALIDATE_CODE_EMPTY.String() && e.Code == 400
}

// 设备验证码为空
func ErrorLiveMonitorDeviceValidateCodeEmpty(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_LIVE_MONITOR_DEVICE_VALIDATE_CODE_EMPTY.String(), fmt.Sprintf(format, args...))
}
