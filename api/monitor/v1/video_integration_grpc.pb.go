// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: api/monitor/v1/video_integration.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	VideoIntegration_RegisterDevice_FullMethodName   = "/api.monitor.v1.VideoIntegration/RegisterDevice"
	VideoIntegration_UnRegisterDevice_FullMethodName = "/api.monitor.v1.VideoIntegration/UnRegisterDevice"
	VideoIntegration_Play_FullMethodName             = "/api.monitor.v1.VideoIntegration/Play"
	VideoIntegration_Report_FullMethodName           = "/api.monitor.v1.VideoIntegration/Report"
	VideoIntegration_QiniuQvsCallBack_FullMethodName = "/api.monitor.v1.VideoIntegration/QiniuQvsCallBack"
	VideoIntegration_SyncToAncda_FullMethodName      = "/api.monitor.v1.VideoIntegration/SyncToAncda"
)

// VideoIntegrationClient is the client API for VideoIntegration service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VideoIntegrationClient interface {
	// 注册设备
	RegisterDevice(ctx context.Context, in *RegisterDeviceReq, opts ...grpc.CallOption) (*RegisterDeviceRsp, error)
	// 解绑设备
	UnRegisterDevice(ctx context.Context, in *UnRegisterDeviceReq, opts ...grpc.CallOption) (*UnRegisterDeviceRsp, error)
	// 播放地址
	Play(ctx context.Context, in *PlayReq, opts ...grpc.CallOption) (*PlayRsp, error)
	// 上报信息
	Report(ctx context.Context, in *ReportReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 七牛回调
	QiniuQvsCallBack(ctx context.Context, in *QnCallBackReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 同步学校信息到掌心宝贝云监控平台
	SyncToAncda(ctx context.Context, in *SyncToAncdaReq, opts ...grpc.CallOption) (*SyncToAncdaRsp, error)
}

type videoIntegrationClient struct {
	cc grpc.ClientConnInterface
}

func NewVideoIntegrationClient(cc grpc.ClientConnInterface) VideoIntegrationClient {
	return &videoIntegrationClient{cc}
}

func (c *videoIntegrationClient) RegisterDevice(ctx context.Context, in *RegisterDeviceReq, opts ...grpc.CallOption) (*RegisterDeviceRsp, error) {
	out := new(RegisterDeviceRsp)
	err := c.cc.Invoke(ctx, VideoIntegration_RegisterDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoIntegrationClient) UnRegisterDevice(ctx context.Context, in *UnRegisterDeviceReq, opts ...grpc.CallOption) (*UnRegisterDeviceRsp, error) {
	out := new(UnRegisterDeviceRsp)
	err := c.cc.Invoke(ctx, VideoIntegration_UnRegisterDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoIntegrationClient) Play(ctx context.Context, in *PlayReq, opts ...grpc.CallOption) (*PlayRsp, error) {
	out := new(PlayRsp)
	err := c.cc.Invoke(ctx, VideoIntegration_Play_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoIntegrationClient) Report(ctx context.Context, in *ReportReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, VideoIntegration_Report_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoIntegrationClient) QiniuQvsCallBack(ctx context.Context, in *QnCallBackReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, VideoIntegration_QiniuQvsCallBack_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoIntegrationClient) SyncToAncda(ctx context.Context, in *SyncToAncdaReq, opts ...grpc.CallOption) (*SyncToAncdaRsp, error) {
	out := new(SyncToAncdaRsp)
	err := c.cc.Invoke(ctx, VideoIntegration_SyncToAncda_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VideoIntegrationServer is the server API for VideoIntegration service.
// All implementations must embed UnimplementedVideoIntegrationServer
// for forward compatibility
type VideoIntegrationServer interface {
	// 注册设备
	RegisterDevice(context.Context, *RegisterDeviceReq) (*RegisterDeviceRsp, error)
	// 解绑设备
	UnRegisterDevice(context.Context, *UnRegisterDeviceReq) (*UnRegisterDeviceRsp, error)
	// 播放地址
	Play(context.Context, *PlayReq) (*PlayRsp, error)
	// 上报信息
	Report(context.Context, *ReportReq) (*emptypb.Empty, error)
	// 七牛回调
	QiniuQvsCallBack(context.Context, *QnCallBackReq) (*emptypb.Empty, error)
	// 同步学校信息到掌心宝贝云监控平台
	SyncToAncda(context.Context, *SyncToAncdaReq) (*SyncToAncdaRsp, error)
	mustEmbedUnimplementedVideoIntegrationServer()
}

// UnimplementedVideoIntegrationServer must be embedded to have forward compatible implementations.
type UnimplementedVideoIntegrationServer struct {
}

func (UnimplementedVideoIntegrationServer) RegisterDevice(context.Context, *RegisterDeviceReq) (*RegisterDeviceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterDevice not implemented")
}
func (UnimplementedVideoIntegrationServer) UnRegisterDevice(context.Context, *UnRegisterDeviceReq) (*UnRegisterDeviceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnRegisterDevice not implemented")
}
func (UnimplementedVideoIntegrationServer) Play(context.Context, *PlayReq) (*PlayRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Play not implemented")
}
func (UnimplementedVideoIntegrationServer) Report(context.Context, *ReportReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Report not implemented")
}
func (UnimplementedVideoIntegrationServer) QiniuQvsCallBack(context.Context, *QnCallBackReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QiniuQvsCallBack not implemented")
}
func (UnimplementedVideoIntegrationServer) SyncToAncda(context.Context, *SyncToAncdaReq) (*SyncToAncdaRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncToAncda not implemented")
}
func (UnimplementedVideoIntegrationServer) mustEmbedUnimplementedVideoIntegrationServer() {}

// UnsafeVideoIntegrationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VideoIntegrationServer will
// result in compilation errors.
type UnsafeVideoIntegrationServer interface {
	mustEmbedUnimplementedVideoIntegrationServer()
}

func RegisterVideoIntegrationServer(s grpc.ServiceRegistrar, srv VideoIntegrationServer) {
	s.RegisterService(&VideoIntegration_ServiceDesc, srv)
}

func _VideoIntegration_RegisterDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoIntegrationServer).RegisterDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoIntegration_RegisterDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoIntegrationServer).RegisterDevice(ctx, req.(*RegisterDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoIntegration_UnRegisterDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnRegisterDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoIntegrationServer).UnRegisterDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoIntegration_UnRegisterDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoIntegrationServer).UnRegisterDevice(ctx, req.(*UnRegisterDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoIntegration_Play_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoIntegrationServer).Play(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoIntegration_Play_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoIntegrationServer).Play(ctx, req.(*PlayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoIntegration_Report_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoIntegrationServer).Report(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoIntegration_Report_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoIntegrationServer).Report(ctx, req.(*ReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoIntegration_QiniuQvsCallBack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QnCallBackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoIntegrationServer).QiniuQvsCallBack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoIntegration_QiniuQvsCallBack_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoIntegrationServer).QiniuQvsCallBack(ctx, req.(*QnCallBackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoIntegration_SyncToAncda_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncToAncdaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoIntegrationServer).SyncToAncda(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoIntegration_SyncToAncda_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoIntegrationServer).SyncToAncda(ctx, req.(*SyncToAncdaReq))
	}
	return interceptor(ctx, in, info, handler)
}

// VideoIntegration_ServiceDesc is the grpc.ServiceDesc for VideoIntegration service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VideoIntegration_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.monitor.v1.VideoIntegration",
	HandlerType: (*VideoIntegrationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RegisterDevice",
			Handler:    _VideoIntegration_RegisterDevice_Handler,
		},
		{
			MethodName: "UnRegisterDevice",
			Handler:    _VideoIntegration_UnRegisterDevice_Handler,
		},
		{
			MethodName: "Play",
			Handler:    _VideoIntegration_Play_Handler,
		},
		{
			MethodName: "Report",
			Handler:    _VideoIntegration_Report_Handler,
		},
		{
			MethodName: "QiniuQvsCallBack",
			Handler:    _VideoIntegration_QiniuQvsCallBack_Handler,
		},
		{
			MethodName: "SyncToAncda",
			Handler:    _VideoIntegration_SyncToAncda_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/monitor/v1/video_integration.proto",
}
