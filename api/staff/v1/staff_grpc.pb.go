// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: staff/v1/staff.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Staff_CreateStaff_FullMethodName          = "/api.staff.v1.Staff/CreateStaff"
	Staff_UpdateBasicStaff_FullMethodName     = "/api.staff.v1.Staff/UpdateBasicStaff"
	Staff_UpdateSimpleStaff_FullMethodName    = "/api.staff.v1.Staff/UpdateSimpleStaff"
	Staff_ChangeStaffMobile_FullMethodName    = "/api.staff.v1.Staff/ChangeStaffMobile"
	Staff_LeaveStaff_FullMethodName           = "/api.staff.v1.Staff/LeaveStaff"
	Staff_ReEntryStaff_FullMethodName         = "/api.staff.v1.Staff/ReEntryStaff"
	Staff_DeleteStaff_FullMethodName          = "/api.staff.v1.Staff/DeleteStaff"
	Staff_ListStaff_FullMethodName            = "/api.staff.v1.Staff/ListStaff"
	Staff_ResetPassword_FullMethodName        = "/api.staff.v1.Staff/ResetPassword"
	Staff_FindStaff_FullMethodName            = "/api.staff.v1.Staff/FindStaff"
	Staff_GetStaff_FullMethodName             = "/api.staff.v1.Staff/GetStaff"
	Staff_GetStaffActivateInfo_FullMethodName = "/api.staff.v1.Staff/GetStaffActivateInfo"
	Staff_ListUnActivatedStaff_FullMethodName = "/api.staff.v1.Staff/ListUnActivatedStaff"
	Staff_SearchStaff_FullMethodName          = "/api.staff.v1.Staff/SearchStaff"
	Staff_VerifyStaffMobile_FullMethodName    = "/api.staff.v1.Staff/VerifyStaffMobile"
)

// StaffClient is the client API for Staff service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StaffClient interface {
	// 添加老师信息
	CreateStaff(ctx context.Context, in *CreateStaffReq, opts ...grpc.CallOption) (*StaffBasicInfo, error)
	// 更新老师基本信息
	UpdateBasicStaff(ctx context.Context, in *UpdateBasicStaffReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新老师个人信息
	UpdateSimpleStaff(ctx context.Context, in *UpdateSimpleStaffReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更换老师手机号
	ChangeStaffMobile(ctx context.Context, in *ChangeStaffMobileReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 离职老师
	LeaveStaff(ctx context.Context, in *LeaveStaffReq, opts ...grpc.CallOption) (*LeaveStaffRsp, error)
	// 重新入职
	ReEntryStaff(ctx context.Context, in *ReEntryStaffReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除老师
	DeleteStaff(ctx context.Context, in *DeleteStaffReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取老师列表
	ListStaff(ctx context.Context, in *ListStaffReq, opts ...grpc.CallOption) (*ListStaffRsp, error)
	// 重置密码
	ResetPassword(ctx context.Context, in *ResetStaffPasswordReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取老师信息（批量）
	FindStaff(ctx context.Context, in *FindStaffReq, opts ...grpc.CallOption) (*FindStaffRsp, error)
	// 获取老师信息（单个）
	GetStaff(ctx context.Context, in *GetStaffReq, opts ...grpc.CallOption) (*StaffInfo, error)
	// 获取老师的激活数和未激活数
	GetStaffActivateInfo(ctx context.Context, in *GetStaffActivateInfoReq, opts ...grpc.CallOption) (*GetStaffActivateInfoRsp, error)
	// 获取未激活的员工列表
	ListUnActivatedStaff(ctx context.Context, in *ListUnActivatedStaffReq, opts ...grpc.CallOption) (*ListUnActivatedStaffRsp, error)
	// 搜索员工
	SearchStaff(ctx context.Context, in *SearchStaffReq, opts ...grpc.CallOption) (*SearchStaffRsp, error)
	// 校验员工手机号是否在系统中(同一类用户下)
	VerifyStaffMobile(ctx context.Context, in *VerifyStaffMobileReq, opts ...grpc.CallOption) (*VerifyStaffMobileRsp, error)
}

type staffClient struct {
	cc grpc.ClientConnInterface
}

func NewStaffClient(cc grpc.ClientConnInterface) StaffClient {
	return &staffClient{cc}
}

func (c *staffClient) CreateStaff(ctx context.Context, in *CreateStaffReq, opts ...grpc.CallOption) (*StaffBasicInfo, error) {
	out := new(StaffBasicInfo)
	err := c.cc.Invoke(ctx, Staff_CreateStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) UpdateBasicStaff(ctx context.Context, in *UpdateBasicStaffReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Staff_UpdateBasicStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) UpdateSimpleStaff(ctx context.Context, in *UpdateSimpleStaffReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Staff_UpdateSimpleStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) ChangeStaffMobile(ctx context.Context, in *ChangeStaffMobileReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Staff_ChangeStaffMobile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) LeaveStaff(ctx context.Context, in *LeaveStaffReq, opts ...grpc.CallOption) (*LeaveStaffRsp, error) {
	out := new(LeaveStaffRsp)
	err := c.cc.Invoke(ctx, Staff_LeaveStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) ReEntryStaff(ctx context.Context, in *ReEntryStaffReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Staff_ReEntryStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) DeleteStaff(ctx context.Context, in *DeleteStaffReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Staff_DeleteStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) ListStaff(ctx context.Context, in *ListStaffReq, opts ...grpc.CallOption) (*ListStaffRsp, error) {
	out := new(ListStaffRsp)
	err := c.cc.Invoke(ctx, Staff_ListStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) ResetPassword(ctx context.Context, in *ResetStaffPasswordReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Staff_ResetPassword_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) FindStaff(ctx context.Context, in *FindStaffReq, opts ...grpc.CallOption) (*FindStaffRsp, error) {
	out := new(FindStaffRsp)
	err := c.cc.Invoke(ctx, Staff_FindStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) GetStaff(ctx context.Context, in *GetStaffReq, opts ...grpc.CallOption) (*StaffInfo, error) {
	out := new(StaffInfo)
	err := c.cc.Invoke(ctx, Staff_GetStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) GetStaffActivateInfo(ctx context.Context, in *GetStaffActivateInfoReq, opts ...grpc.CallOption) (*GetStaffActivateInfoRsp, error) {
	out := new(GetStaffActivateInfoRsp)
	err := c.cc.Invoke(ctx, Staff_GetStaffActivateInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) ListUnActivatedStaff(ctx context.Context, in *ListUnActivatedStaffReq, opts ...grpc.CallOption) (*ListUnActivatedStaffRsp, error) {
	out := new(ListUnActivatedStaffRsp)
	err := c.cc.Invoke(ctx, Staff_ListUnActivatedStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) SearchStaff(ctx context.Context, in *SearchStaffReq, opts ...grpc.CallOption) (*SearchStaffRsp, error) {
	out := new(SearchStaffRsp)
	err := c.cc.Invoke(ctx, Staff_SearchStaff_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffClient) VerifyStaffMobile(ctx context.Context, in *VerifyStaffMobileReq, opts ...grpc.CallOption) (*VerifyStaffMobileRsp, error) {
	out := new(VerifyStaffMobileRsp)
	err := c.cc.Invoke(ctx, Staff_VerifyStaffMobile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StaffServer is the server API for Staff service.
// All implementations must embed UnimplementedStaffServer
// for forward compatibility
type StaffServer interface {
	// 添加老师信息
	CreateStaff(context.Context, *CreateStaffReq) (*StaffBasicInfo, error)
	// 更新老师基本信息
	UpdateBasicStaff(context.Context, *UpdateBasicStaffReq) (*emptypb.Empty, error)
	// 更新老师个人信息
	UpdateSimpleStaff(context.Context, *UpdateSimpleStaffReq) (*emptypb.Empty, error)
	// 更换老师手机号
	ChangeStaffMobile(context.Context, *ChangeStaffMobileReq) (*emptypb.Empty, error)
	// 离职老师
	LeaveStaff(context.Context, *LeaveStaffReq) (*LeaveStaffRsp, error)
	// 重新入职
	ReEntryStaff(context.Context, *ReEntryStaffReq) (*emptypb.Empty, error)
	// 删除老师
	DeleteStaff(context.Context, *DeleteStaffReq) (*emptypb.Empty, error)
	// 获取老师列表
	ListStaff(context.Context, *ListStaffReq) (*ListStaffRsp, error)
	// 重置密码
	ResetPassword(context.Context, *ResetStaffPasswordReq) (*emptypb.Empty, error)
	// 获取老师信息（批量）
	FindStaff(context.Context, *FindStaffReq) (*FindStaffRsp, error)
	// 获取老师信息（单个）
	GetStaff(context.Context, *GetStaffReq) (*StaffInfo, error)
	// 获取老师的激活数和未激活数
	GetStaffActivateInfo(context.Context, *GetStaffActivateInfoReq) (*GetStaffActivateInfoRsp, error)
	// 获取未激活的员工列表
	ListUnActivatedStaff(context.Context, *ListUnActivatedStaffReq) (*ListUnActivatedStaffRsp, error)
	// 搜索员工
	SearchStaff(context.Context, *SearchStaffReq) (*SearchStaffRsp, error)
	// 校验员工手机号是否在系统中(同一类用户下)
	VerifyStaffMobile(context.Context, *VerifyStaffMobileReq) (*VerifyStaffMobileRsp, error)
	mustEmbedUnimplementedStaffServer()
}

// UnimplementedStaffServer must be embedded to have forward compatible implementations.
type UnimplementedStaffServer struct {
}

func (UnimplementedStaffServer) CreateStaff(context.Context, *CreateStaffReq) (*StaffBasicInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStaff not implemented")
}
func (UnimplementedStaffServer) UpdateBasicStaff(context.Context, *UpdateBasicStaffReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBasicStaff not implemented")
}
func (UnimplementedStaffServer) UpdateSimpleStaff(context.Context, *UpdateSimpleStaffReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSimpleStaff not implemented")
}
func (UnimplementedStaffServer) ChangeStaffMobile(context.Context, *ChangeStaffMobileReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeStaffMobile not implemented")
}
func (UnimplementedStaffServer) LeaveStaff(context.Context, *LeaveStaffReq) (*LeaveStaffRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LeaveStaff not implemented")
}
func (UnimplementedStaffServer) ReEntryStaff(context.Context, *ReEntryStaffReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReEntryStaff not implemented")
}
func (UnimplementedStaffServer) DeleteStaff(context.Context, *DeleteStaffReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStaff not implemented")
}
func (UnimplementedStaffServer) ListStaff(context.Context, *ListStaffReq) (*ListStaffRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStaff not implemented")
}
func (UnimplementedStaffServer) ResetPassword(context.Context, *ResetStaffPasswordReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetPassword not implemented")
}
func (UnimplementedStaffServer) FindStaff(context.Context, *FindStaffReq) (*FindStaffRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindStaff not implemented")
}
func (UnimplementedStaffServer) GetStaff(context.Context, *GetStaffReq) (*StaffInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaff not implemented")
}
func (UnimplementedStaffServer) GetStaffActivateInfo(context.Context, *GetStaffActivateInfoReq) (*GetStaffActivateInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffActivateInfo not implemented")
}
func (UnimplementedStaffServer) ListUnActivatedStaff(context.Context, *ListUnActivatedStaffReq) (*ListUnActivatedStaffRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUnActivatedStaff not implemented")
}
func (UnimplementedStaffServer) SearchStaff(context.Context, *SearchStaffReq) (*SearchStaffRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchStaff not implemented")
}
func (UnimplementedStaffServer) VerifyStaffMobile(context.Context, *VerifyStaffMobileReq) (*VerifyStaffMobileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyStaffMobile not implemented")
}
func (UnimplementedStaffServer) mustEmbedUnimplementedStaffServer() {}

// UnsafeStaffServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StaffServer will
// result in compilation errors.
type UnsafeStaffServer interface {
	mustEmbedUnimplementedStaffServer()
}

func RegisterStaffServer(s grpc.ServiceRegistrar, srv StaffServer) {
	s.RegisterService(&Staff_ServiceDesc, srv)
}

func _Staff_CreateStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).CreateStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_CreateStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).CreateStaff(ctx, req.(*CreateStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_UpdateBasicStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBasicStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).UpdateBasicStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_UpdateBasicStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).UpdateBasicStaff(ctx, req.(*UpdateBasicStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_UpdateSimpleStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSimpleStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).UpdateSimpleStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_UpdateSimpleStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).UpdateSimpleStaff(ctx, req.(*UpdateSimpleStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_ChangeStaffMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeStaffMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).ChangeStaffMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_ChangeStaffMobile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).ChangeStaffMobile(ctx, req.(*ChangeStaffMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_LeaveStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaveStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).LeaveStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_LeaveStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).LeaveStaff(ctx, req.(*LeaveStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_ReEntryStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReEntryStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).ReEntryStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_ReEntryStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).ReEntryStaff(ctx, req.(*ReEntryStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_DeleteStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).DeleteStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_DeleteStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).DeleteStaff(ctx, req.(*DeleteStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_ListStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).ListStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_ListStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).ListStaff(ctx, req.(*ListStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_ResetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetStaffPasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).ResetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_ResetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).ResetPassword(ctx, req.(*ResetStaffPasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_FindStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).FindStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_FindStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).FindStaff(ctx, req.(*FindStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_GetStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).GetStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_GetStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).GetStaff(ctx, req.(*GetStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_GetStaffActivateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffActivateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).GetStaffActivateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_GetStaffActivateInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).GetStaffActivateInfo(ctx, req.(*GetStaffActivateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_ListUnActivatedStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUnActivatedStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).ListUnActivatedStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_ListUnActivatedStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).ListUnActivatedStaff(ctx, req.(*ListUnActivatedStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_SearchStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchStaffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).SearchStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_SearchStaff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).SearchStaff(ctx, req.(*SearchStaffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Staff_VerifyStaffMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyStaffMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffServer).VerifyStaffMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Staff_VerifyStaffMobile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffServer).VerifyStaffMobile(ctx, req.(*VerifyStaffMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Staff_ServiceDesc is the grpc.ServiceDesc for Staff service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Staff_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.staff.v1.Staff",
	HandlerType: (*StaffServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateStaff",
			Handler:    _Staff_CreateStaff_Handler,
		},
		{
			MethodName: "UpdateBasicStaff",
			Handler:    _Staff_UpdateBasicStaff_Handler,
		},
		{
			MethodName: "UpdateSimpleStaff",
			Handler:    _Staff_UpdateSimpleStaff_Handler,
		},
		{
			MethodName: "ChangeStaffMobile",
			Handler:    _Staff_ChangeStaffMobile_Handler,
		},
		{
			MethodName: "LeaveStaff",
			Handler:    _Staff_LeaveStaff_Handler,
		},
		{
			MethodName: "ReEntryStaff",
			Handler:    _Staff_ReEntryStaff_Handler,
		},
		{
			MethodName: "DeleteStaff",
			Handler:    _Staff_DeleteStaff_Handler,
		},
		{
			MethodName: "ListStaff",
			Handler:    _Staff_ListStaff_Handler,
		},
		{
			MethodName: "ResetPassword",
			Handler:    _Staff_ResetPassword_Handler,
		},
		{
			MethodName: "FindStaff",
			Handler:    _Staff_FindStaff_Handler,
		},
		{
			MethodName: "GetStaff",
			Handler:    _Staff_GetStaff_Handler,
		},
		{
			MethodName: "GetStaffActivateInfo",
			Handler:    _Staff_GetStaffActivateInfo_Handler,
		},
		{
			MethodName: "ListUnActivatedStaff",
			Handler:    _Staff_ListUnActivatedStaff_Handler,
		},
		{
			MethodName: "SearchStaff",
			Handler:    _Staff_SearchStaff_Handler,
		},
		{
			MethodName: "VerifyStaffMobile",
			Handler:    _Staff_VerifyStaffMobile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "staff/v1/staff.proto",
}
