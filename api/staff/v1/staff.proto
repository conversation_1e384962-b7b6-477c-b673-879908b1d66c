syntax = "proto3";

package api.staff.v1;


import "google/protobuf/empty.proto";
import "validate/validate.proto";

option go_package = "api/staff/v1;v1";

//生成proto代码
//kratos proto client api/staff/v1/staff.proto

//生成service代码
//kratos proto server api/staff/v1/staff.proto

// 教职工API
service Staff {
  // 添加老师信息
  rpc CreateStaff (CreateStaffReq) returns (StaffBasicInfo) {}

  // 更新老师基本信息
  rpc UpdateBasicStaff(UpdateBasicStaffReq) returns (google.protobuf.Empty) {}

  // 更新老师个人信息
  rpc UpdateSimpleStaff(UpdateSimpleStaffReq) returns (google.protobuf.Empty) {}

  // 更换老师手机号
  rpc ChangeStaffMobile(ChangeStaffMobileReq) returns (google.protobuf.Empty) {}

  // 离职老师
  rpc LeaveStaff(LeaveStaffReq) returns (LeaveStaffRsp) {}

  // 重新入职
  rpc ReEntryStaff(ReEntryStaffReq) returns (google.protobuf.Empty) {}

  // 删除老师
  rpc DeleteStaff(DeleteStaffReq) returns (google.protobuf.Empty) {}

  // 导入老师信息
  // rpc ImportStaff(ImportStaffReq) returns (ImportStaffRsp) {}

  // 获取老师列表
  rpc ListStaff(ListStaffReq) returns (ListStaffRsp) {}

  // 重置密码
  rpc ResetPassword(ResetStaffPasswordReq) returns (google.protobuf.Empty) {}

  // 获取老师信息（批量）
  rpc FindStaff(FindStaffReq) returns (FindStaffRsp) {}

  // 获取老师信息（单个）
  rpc GetStaff(GetStaffReq) returns (StaffInfo) {}

  // 获取老师的激活数和未激活数
  rpc GetStaffActivateInfo(GetStaffActivateInfoReq) returns (GetStaffActivateInfoRsp) {}

  // 获取未激活的员工列表
  rpc ListUnActivatedStaff(ListUnActivatedStaffReq) returns (ListUnActivatedStaffRsp) {}

  // 搜索员工
  rpc SearchStaff(SearchStaffReq) returns (SearchStaffRsp) {}

  // 校验员工手机号是否在系统中(同一类用户下)
  rpc VerifyStaffMobile(VerifyStaffMobileReq) returns (VerifyStaffMobileRsp) {}

}

message VerifyStaffMobileReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 手机区号
  string re_code = 2 [(validate.rules).string = {min_len: 1, max_len: 4}];
  // 手机号
  string mobile = 3 [(validate.rules).string.pattern = "^1\\d{10}$"];
}
message VerifyStaffMobileRsp {
  // 是否在系统中
  bool exist = 1;
}

message SearchStaffReq {
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 搜索关键词
  string keyword = 2 [(validate.rules).string.min_len = 1];
}

message SearchStaffRsp {
  // 搜索结果列表
  repeated SimpleStaffInfo list = 1;
}

message SimpleStaffInfo {
  // id
  int64 id = 1;
  // 姓名
  string name = 2;
  // 角色名称
  string role_name = 3;
}

message ListUnActivatedStaffReq {
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
}

message ListUnActivatedStaffRsp {
  // 老师信息列表
  repeated UnActivatedStaffInfo list = 1;
}

message UnActivatedStaffInfo {
  // id
  int64 id = 1;
  // 姓名
  string name = 2;
  // 角色名称
  string role_name = 3;
  // 电话区号
  string re_code = 4;
  // 手机号
  string mobile = 5;
  // 头像
  string avatar = 6;
}

message GetStaffActivateInfoReq {
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
}

message GetStaffActivateInfoRsp {
  // 激活数
  uint32 activated_count = 1;
  // 未激活数
  uint32 unactivate_count = 2;
}

// 创建老师请求参数
message CreateStaffReq {
  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
  // 老师姓名，限30字符
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 手机区号
  string re_code = 3 [(validate.rules).string = {min_len: 1, max_len: 4}];
  // 手机号
  string mobile = 4 [(validate.rules).string.pattern = "^1\\d{10}$"];
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 5 [(validate.rules).uint32 = {in: [0, 1, 2]}];
  // 多音字
  string polyphone = 6 [(validate.rules).string = { max_len: 50}];
  // 称谓，默认为老师
  string title = 7 [(validate.rules).string = {min_len: 1, max_len: 10}];
  // 角色id
  int64 role_id = 8 [(validate.rules).int64.gt = 0];
  // 归属部门id
  repeated int64 dept_ids = 9 [(validate.rules).repeated.min_items = 1];
  // 入职时间，时间戳，单位：秒
  int64 entry_date = 10;
  // 任教班级id
  repeated int64 class_ids = 11;
  // 考勤卡，最多绑10张卡
  repeated string card_list = 12;
  // 注册渠道 （1：园长后台，2：app，3：扫码注册）
  uint32 source = 13 [(validate.rules).uint32 = {in: [1, 2, 3]}];
  // 操作渠道 （1：园长后台，2：app端）
  uint32 channel = 14 [(validate.rules).uint32 = {in: [1, 2]}];
  // 创建人id
  int64 operator_id = 15 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 16 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 17 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 老师基本信息
message StaffBasicInfo {
  // id
  int64 id = 1;
  // 学校id
  int64 inst_id = 2;
  // 老师姓名，限30字符
  string name = 3;
  // 手机区号
  string re_code = 4;
  // 手机号
  string mobile = 5;
  // 多音字
  string polyphone = 6;
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 7;
  // 称谓，默认为老师
  string title = 8;
  // 角色id
  int64 role_id = 9;
  // 归属部门id
  repeated int64 dept_ids = 10;
  // 入职时间，时间戳，单位：秒
  int64 entry_date = 11;
  // 任教班级id
  repeated int64 class_ids = 12;
  // 考勤卡，最多绑10张卡
  repeated string card_list = 13;
  // 头像
  string avatar = 14;
  // 最后一次登陆时间
  int64 last_login_time = 15;
  // 称谓
  string alias = 16;
}

// 更新老师基础信息请求参数
message UpdateBasicStaffReq {
  // 老师id
  int64 id = 1 [(validate.rules).int64.gte = 0];
  // 老师姓名，限30字符
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 手机区号
  string re_code = 3 [(validate.rules).string = {min_len: 1, max_len: 4}];
  // 手机号
  string mobile = 4 [(validate.rules).string.pattern = "^1\\d{10}$"];
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 5 [(validate.rules).uint32 = {in: [0, 1, 2]}];
  // 称谓，默认为老师
  string title = 6 [(validate.rules).string = {min_len: 1, max_len: 10}];
  // 多音字
  string polyphone = 7 [(validate.rules).string = { max_len: 50}];
  // 角色id
  int64 role_id = 8 [(validate.rules).int64.gte = 0];
  // 归属部门id
  repeated int64 dept_ids = 9 [(validate.rules).repeated.min_items = 1];
  // 入职时间，时间戳，单位：秒
  int64 entry_date = 10;
  // 任教班级id
  repeated int64 class_ids = 11;
  // 学校id
  int64 inst_id = 12 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 13 [(validate.rules).uint32 = {in: [1, 2]}];
  // 创建人id
  int64 operator_id = 14 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 15 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 16 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message UpdateSimpleStaffReq {
  // 老师id
  int64 id = 1 [(validate.rules).int64.gte = 0];
  // 老师姓名，限30字符
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 30}];
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 3 [(validate.rules).uint32 = {in: [0, 1, 2]}];
  // 入职时间，时间戳，单位：秒
  int64 entry_date = 4;
  // 头像
  string avatar = 5;
  // 学校id
  int64 inst_id = 6 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 7 [(validate.rules).uint32 = {in: [1, 2]}];
  // 创建人id
  int64 operator_id = 8 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 9 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 10 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

message ChangeStaffMobileReq {
  // 老师id
  int64 id = 1 [(validate.rules).int64.gte = 0];
  // 原手机号
  string old_mobile = 2 [(validate.rules).string.pattern = "^1\\d{10}$"];
  // 新手机区号
  string re_code = 3 [(validate.rules).string = {min_len: 1, max_len: 4}];
  // 新手机号
  string mobile = 4 [(validate.rules).string.pattern = "^1\\d{10}$"];
  // 学校id
  int64 inst_id = 5 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 6 [(validate.rules).uint32 = {in: [1, 2]}];
  // 创建人id
  int64 operator_id = 7 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 8 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 9 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 离职老师请求参数
message LeaveStaffReq {
  // 老师id
  repeated int64 ids = 1 [(validate.rules).repeated.min_items = 1];
  // 离职时间，时间戳：秒
  int64 leave_time = 2 [(validate.rules).int64.gt = 0];
  // 离职原因
  string reason = 3 [(validate.rules).string = {min_len: 1, max_len: 50}];
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 5 [(validate.rules).uint32 = {in: [1, 2]}];
  // 创建人id
  int64 operator_id = 6 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 7 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 离职老师返回参数
message LeaveStaffRsp {
  // 失败列表，若为空则表示全部成功
  repeated StaffFailure failure_list = 1;
}

message StaffFailure {
  // 老师id
  int64 id = 1;
  // 老师姓名
  string name = 2;
  // 失败原因
  string reason = 3;
}

// 删除老师请求参数
message DeleteStaffReq {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 3 [(validate.rules).uint32 = {in: [1, 2]}];
  // 创建人id
  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 5 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// excel导入老师请求参数
//message ImportStaffReq {
//  int64 inst_id = 1 [(validate.rules).int64.gt = 0];
//  // 文件url
//  string file_url = 2;
//  // 渠道 （1：园长后台，2：app端）
//  uint32 channel = 3 [(validate.rules).uint32 = {in: [1, 2]}];
//  // 创建人id
//  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
//  // 创建人姓名
//  string operator_name = 5 [(validate.rules).string.min_len = 1];
//  // ip地址
//  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
//}

// excel导入老师返回参数
//message ImportStaffRsp {
//  // 导入失败的错误信息记录文件地址
//  string fail_info_file_url = 1;
//}

// 获取老师列表请求参数
message ListStaffReq {
  // 当前页
  int32 page = 1;
  // 每页条数
  uint32 per_page = 2[(validate.rules).uint32.gte = 0];
  // 学校id
  int64 inst_id = 3 [(validate.rules).int64.gt = 0];
  // 状态（1：在职，2：离职） 不传默认为1
  uint32 status = 4 [(validate.rules).uint32 = {in: [0, 1, 2]}];
  // 老师姓名
  string name = 5 [(validate.rules).string = {min_len: 0, max_len: 30}];
  // 老师手机号
  string mobile = 6;
  // 入职起始时间
  int64 entry_start_date = 7[(validate.rules).int64.gte = 0];
  // 入职结束时间
  int64 entry_end_date = 8[(validate.rules).int64.gte = 0];
  // 部门id
  repeated int64 dept_ids = 10;
  // 角色id
  repeated int64 role_ids = 11;
  // 考勤卡
  string card_no = 12;
  // 离职起始时间，时间戳 单位：秒
  int64 leave_start_date = 13[(validate.rules).int64.gte = 0];
  // 离职结束时间，时间戳 单位：秒
  int64 leave_end_date = 14[(validate.rules).int64.gte = 0];
}

// 查询老师返回参数
message ListStaffRsp {
  // 总条数
  int64 total = 1;
  // 当前页
  int32 page = 2;
  // 每页条数
  uint32 per_page = 3;
  // 老师信息列表
  repeated StaffInfo list = 4;
}

message StaffInfo {
  message RoleInfo {
    // 角色id
    int64 id = 1;
    // 角色名称
    string name = 2;
    // 角色编号
    string code = 3;
    // 是否是管理员
    bool is_admin = 4;
    // 是否是内置角色
    bool is_builtin = 5;
    // 数据范围(1：全园，2：关联班级）
    int32 data_scope = 6;
  }
  message DeptInfo {
    // 部门id
    int64 id = 1;
    // 部门名称
    string name = 2;
  }
  message TeachInfo {
    // 班级id
    int64 class_id = 1;
    // 班级名称
    string class_name = 2;
    // 年级名称
    string grade_name = 3;
  }
  // id
  int64 id = 1;
  // 姓名
  string name = 2;
  // 称谓
  string title = 3;
  // 性别，0表示未知，1表示男性，2表示女性
  uint32 gender = 4;
  // 电话区号
  string re_code = 5;
  // 手机号
  string mobile = 6;
  // 多音字
  string polyphone = 7;
  // 角色
  RoleInfo role = 8;
  // 部门
  repeated DeptInfo dept_list = 9;
  // 任教班级
  repeated TeachInfo teach_list = 10;
  // 来源
  int32 source = 11;
  // 入职日期
  int64 entry_date = 12;
  // 离职日期
  int64 leave_date = 13;
  // 离职原因
  string leave_reason = 14;
  // 头像
  string avatar = 15;
  // 绑定考勤卡列表
  repeated string card_list = 16;
  // 最后一次登陆时间
  int64 last_login_time = 17;
  // 称谓
  string alias = 18;
  // 是否已离职
  bool is_leave = 19;
}

// 老师重新入职请求参数
message ReEntryStaffReq {
  // 老师id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 部门id
  repeated int64 dept_ids = 2 [(validate.rules).repeated.min_items = 1];
  // 角色id
  int64 role_id = 3 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 4 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 5 [(validate.rules).uint32 = {in: [1, 2]}];
  // 创建人id
  int64 operator_id = 6 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 7 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 8 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 重置密码
message ResetStaffPasswordReq {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // 学校id
  int64 inst_id = 2 [(validate.rules).int64.gt = 0];
  // 渠道 （1：园长后台，2：app端）
  uint32 channel = 3 [(validate.rules).uint32 = {in: [1, 2]}];
  // 创建人id
  int64 operator_id = 4 [(validate.rules).int64.gt = 0];
  // 创建人姓名
  string operator_name = 5 [(validate.rules).string.min_len = 1];
  // ip地址
  string ip = 6 [(validate.rules).string = {min_len: 1, max_len: 20}];
}

// 批量获取老师信息
message FindStaffReq{
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gte = 0];
  // 老师ID
  repeated int64 staff_id = 2 [(validate.rules).repeated.min_items = 1];
}

message FindStaffRsp{
  // 老师信息列表
  repeated StaffBasicInfo data = 1;
}

// 获取单个老师信息
message GetStaffReq {
  // 学校id
  int64 inst_id = 1 [(validate.rules).int64.gte = 0];
  // 老师ID
  int64 staff_id = 2 [(validate.rules).int64.gte = 0];
}