// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.17.3
// source: staff/v1/staff.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VerifyStaffMobileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 手机区号
	ReCode string `protobuf:"bytes,2,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile,omitempty"`
}

func (x *VerifyStaffMobileReq) Reset() {
	*x = VerifyStaffMobileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyStaffMobileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyStaffMobileReq) ProtoMessage() {}

func (x *VerifyStaffMobileReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyStaffMobileReq.ProtoReflect.Descriptor instead.
func (*VerifyStaffMobileReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{0}
}

func (x *VerifyStaffMobileReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *VerifyStaffMobileReq) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *VerifyStaffMobileReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

type VerifyStaffMobileRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否在系统中
	Exist bool `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
}

func (x *VerifyStaffMobileRsp) Reset() {
	*x = VerifyStaffMobileRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyStaffMobileRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyStaffMobileRsp) ProtoMessage() {}

func (x *VerifyStaffMobileRsp) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyStaffMobileRsp.ProtoReflect.Descriptor instead.
func (*VerifyStaffMobileRsp) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{1}
}

func (x *VerifyStaffMobileRsp) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

type SearchStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 搜索关键词
	Keyword string `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
}

func (x *SearchStaffReq) Reset() {
	*x = SearchStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchStaffReq) ProtoMessage() {}

func (x *SearchStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchStaffReq.ProtoReflect.Descriptor instead.
func (*SearchStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{2}
}

func (x *SearchStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *SearchStaffReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type SearchStaffRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索结果列表
	List []*SimpleStaffInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SearchStaffRsp) Reset() {
	*x = SearchStaffRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchStaffRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchStaffRsp) ProtoMessage() {}

func (x *SearchStaffRsp) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchStaffRsp.ProtoReflect.Descriptor instead.
func (*SearchStaffRsp) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{3}
}

func (x *SearchStaffRsp) GetList() []*SimpleStaffInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type SimpleStaffInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 角色名称
	RoleName string `protobuf:"bytes,3,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
}

func (x *SimpleStaffInfo) Reset() {
	*x = SimpleStaffInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimpleStaffInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleStaffInfo) ProtoMessage() {}

func (x *SimpleStaffInfo) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleStaffInfo.ProtoReflect.Descriptor instead.
func (*SimpleStaffInfo) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{4}
}

func (x *SimpleStaffInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SimpleStaffInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SimpleStaffInfo) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

type ListUnActivatedStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *ListUnActivatedStaffReq) Reset() {
	*x = ListUnActivatedStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnActivatedStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnActivatedStaffReq) ProtoMessage() {}

func (x *ListUnActivatedStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnActivatedStaffReq.ProtoReflect.Descriptor instead.
func (*ListUnActivatedStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{5}
}

func (x *ListUnActivatedStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

type ListUnActivatedStaffRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 老师信息列表
	List []*UnActivatedStaffInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListUnActivatedStaffRsp) Reset() {
	*x = ListUnActivatedStaffRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnActivatedStaffRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnActivatedStaffRsp) ProtoMessage() {}

func (x *ListUnActivatedStaffRsp) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnActivatedStaffRsp.ProtoReflect.Descriptor instead.
func (*ListUnActivatedStaffRsp) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{6}
}

func (x *ListUnActivatedStaffRsp) GetList() []*UnActivatedStaffInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type UnActivatedStaffInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 角色名称
	RoleName string `protobuf:"bytes,3,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	// 电话区号
	ReCode string `protobuf:"bytes,4,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`
}

func (x *UnActivatedStaffInfo) Reset() {
	*x = UnActivatedStaffInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnActivatedStaffInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnActivatedStaffInfo) ProtoMessage() {}

func (x *UnActivatedStaffInfo) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnActivatedStaffInfo.ProtoReflect.Descriptor instead.
func (*UnActivatedStaffInfo) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{7}
}

func (x *UnActivatedStaffInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UnActivatedStaffInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UnActivatedStaffInfo) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

func (x *UnActivatedStaffInfo) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *UnActivatedStaffInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UnActivatedStaffInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type GetStaffActivateInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
}

func (x *GetStaffActivateInfoReq) Reset() {
	*x = GetStaffActivateInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffActivateInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffActivateInfoReq) ProtoMessage() {}

func (x *GetStaffActivateInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffActivateInfoReq.ProtoReflect.Descriptor instead.
func (*GetStaffActivateInfoReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{8}
}

func (x *GetStaffActivateInfoReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

type GetStaffActivateInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 激活数
	ActivatedCount uint32 `protobuf:"varint,1,opt,name=activated_count,json=activatedCount,proto3" json:"activated_count,omitempty"`
	// 未激活数
	UnactivateCount uint32 `protobuf:"varint,2,opt,name=unactivate_count,json=unactivateCount,proto3" json:"unactivate_count,omitempty"`
}

func (x *GetStaffActivateInfoRsp) Reset() {
	*x = GetStaffActivateInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffActivateInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffActivateInfoRsp) ProtoMessage() {}

func (x *GetStaffActivateInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffActivateInfoRsp.ProtoReflect.Descriptor instead.
func (*GetStaffActivateInfoRsp) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{9}
}

func (x *GetStaffActivateInfoRsp) GetActivatedCount() uint32 {
	if x != nil {
		return x.ActivatedCount
	}
	return 0
}

func (x *GetStaffActivateInfoRsp) GetUnactivateCount() uint32 {
	if x != nil {
		return x.UnactivateCount
	}
	return 0
}

// 创建老师请求参数
type CreateStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 老师姓名，限30字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 手机区号
	ReCode string `protobuf:"bytes,3,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`
	// 多音字
	Polyphone string `protobuf:"bytes,6,opt,name=polyphone,proto3" json:"polyphone,omitempty"`
	// 称谓，默认为老师
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	// 角色id
	RoleId int64 `protobuf:"varint,8,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 归属部门id
	DeptIds []int64 `protobuf:"varint,9,rep,packed,name=dept_ids,json=deptIds,proto3" json:"dept_ids,omitempty"`
	// 入职时间，时间戳，单位：秒
	EntryDate int64 `protobuf:"varint,10,opt,name=entry_date,json=entryDate,proto3" json:"entry_date,omitempty"`
	// 任教班级id
	ClassIds []int64 `protobuf:"varint,11,rep,packed,name=class_ids,json=classIds,proto3" json:"class_ids,omitempty"`
	// 考勤卡，最多绑10张卡
	CardList []string `protobuf:"bytes,12,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
	// 注册渠道 （1：园长后台，2：app，3：扫码注册）
	Source uint32 `protobuf:"varint,13,opt,name=source,proto3" json:"source,omitempty"`
	// 操作渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,14,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,15,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,16,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,17,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *CreateStaffReq) Reset() {
	*x = CreateStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffReq) ProtoMessage() {}

func (x *CreateStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffReq.ProtoReflect.Descriptor instead.
func (*CreateStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{10}
}

func (x *CreateStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *CreateStaffReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateStaffReq) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *CreateStaffReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *CreateStaffReq) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *CreateStaffReq) GetPolyphone() string {
	if x != nil {
		return x.Polyphone
	}
	return ""
}

func (x *CreateStaffReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateStaffReq) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *CreateStaffReq) GetDeptIds() []int64 {
	if x != nil {
		return x.DeptIds
	}
	return nil
}

func (x *CreateStaffReq) GetEntryDate() int64 {
	if x != nil {
		return x.EntryDate
	}
	return 0
}

func (x *CreateStaffReq) GetClassIds() []int64 {
	if x != nil {
		return x.ClassIds
	}
	return nil
}

func (x *CreateStaffReq) GetCardList() []string {
	if x != nil {
		return x.CardList
	}
	return nil
}

func (x *CreateStaffReq) GetSource() uint32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *CreateStaffReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *CreateStaffReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateStaffReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *CreateStaffReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 老师基本信息
type StaffBasicInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 老师姓名，限30字符
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 手机区号
	ReCode string `protobuf:"bytes,4,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 多音字
	Polyphone string `protobuf:"bytes,6,opt,name=polyphone,proto3" json:"polyphone,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,7,opt,name=gender,proto3" json:"gender,omitempty"`
	// 称谓，默认为老师
	Title string `protobuf:"bytes,8,opt,name=title,proto3" json:"title,omitempty"`
	// 角色id
	RoleId int64 `protobuf:"varint,9,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 归属部门id
	DeptIds []int64 `protobuf:"varint,10,rep,packed,name=dept_ids,json=deptIds,proto3" json:"dept_ids,omitempty"`
	// 入职时间，时间戳，单位：秒
	EntryDate int64 `protobuf:"varint,11,opt,name=entry_date,json=entryDate,proto3" json:"entry_date,omitempty"`
	// 任教班级id
	ClassIds []int64 `protobuf:"varint,12,rep,packed,name=class_ids,json=classIds,proto3" json:"class_ids,omitempty"`
	// 考勤卡，最多绑10张卡
	CardList []string `protobuf:"bytes,13,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,14,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 最后一次登陆时间
	LastLoginTime int64 `protobuf:"varint,15,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time,omitempty"`
	// 称谓
	Alias string `protobuf:"bytes,16,opt,name=alias,proto3" json:"alias,omitempty"`
}

func (x *StaffBasicInfo) Reset() {
	*x = StaffBasicInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffBasicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffBasicInfo) ProtoMessage() {}

func (x *StaffBasicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffBasicInfo.ProtoReflect.Descriptor instead.
func (*StaffBasicInfo) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{11}
}

func (x *StaffBasicInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffBasicInfo) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *StaffBasicInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StaffBasicInfo) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *StaffBasicInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *StaffBasicInfo) GetPolyphone() string {
	if x != nil {
		return x.Polyphone
	}
	return ""
}

func (x *StaffBasicInfo) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *StaffBasicInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *StaffBasicInfo) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *StaffBasicInfo) GetDeptIds() []int64 {
	if x != nil {
		return x.DeptIds
	}
	return nil
}

func (x *StaffBasicInfo) GetEntryDate() int64 {
	if x != nil {
		return x.EntryDate
	}
	return 0
}

func (x *StaffBasicInfo) GetClassIds() []int64 {
	if x != nil {
		return x.ClassIds
	}
	return nil
}

func (x *StaffBasicInfo) GetCardList() []string {
	if x != nil {
		return x.CardList
	}
	return nil
}

func (x *StaffBasicInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *StaffBasicInfo) GetLastLoginTime() int64 {
	if x != nil {
		return x.LastLoginTime
	}
	return 0
}

func (x *StaffBasicInfo) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

// 更新老师基础信息请求参数
type UpdateBasicStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 老师id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 老师姓名，限30字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 手机区号
	ReCode string `protobuf:"bytes,3,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`
	// 称谓，默认为老师
	Title string `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	// 多音字
	Polyphone string `protobuf:"bytes,7,opt,name=polyphone,proto3" json:"polyphone,omitempty"`
	// 角色id
	RoleId int64 `protobuf:"varint,8,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 归属部门id
	DeptIds []int64 `protobuf:"varint,9,rep,packed,name=dept_ids,json=deptIds,proto3" json:"dept_ids,omitempty"`
	// 入职时间，时间戳，单位：秒
	EntryDate int64 `protobuf:"varint,10,opt,name=entry_date,json=entryDate,proto3" json:"entry_date,omitempty"`
	// 任教班级id
	ClassIds []int64 `protobuf:"varint,11,rep,packed,name=class_ids,json=classIds,proto3" json:"class_ids,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,12,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,13,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,14,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,15,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,16,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateBasicStaffReq) Reset() {
	*x = UpdateBasicStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBasicStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBasicStaffReq) ProtoMessage() {}

func (x *UpdateBasicStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBasicStaffReq.ProtoReflect.Descriptor instead.
func (*UpdateBasicStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateBasicStaffReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateBasicStaffReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateBasicStaffReq) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *UpdateBasicStaffReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UpdateBasicStaffReq) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UpdateBasicStaffReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateBasicStaffReq) GetPolyphone() string {
	if x != nil {
		return x.Polyphone
	}
	return ""
}

func (x *UpdateBasicStaffReq) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *UpdateBasicStaffReq) GetDeptIds() []int64 {
	if x != nil {
		return x.DeptIds
	}
	return nil
}

func (x *UpdateBasicStaffReq) GetEntryDate() int64 {
	if x != nil {
		return x.EntryDate
	}
	return 0
}

func (x *UpdateBasicStaffReq) GetClassIds() []int64 {
	if x != nil {
		return x.ClassIds
	}
	return nil
}

func (x *UpdateBasicStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateBasicStaffReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateBasicStaffReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateBasicStaffReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateBasicStaffReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type UpdateSimpleStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 老师id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 老师姓名，限30字符
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,3,opt,name=gender,proto3" json:"gender,omitempty"`
	// 入职时间，时间戳，单位：秒
	EntryDate int64 `protobuf:"varint,4,opt,name=entry_date,json=entryDate,proto3" json:"entry_date,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,6,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,7,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,8,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,9,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,10,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UpdateSimpleStaffReq) Reset() {
	*x = UpdateSimpleStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSimpleStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSimpleStaffReq) ProtoMessage() {}

func (x *UpdateSimpleStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSimpleStaffReq.ProtoReflect.Descriptor instead.
func (*UpdateSimpleStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateSimpleStaffReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSimpleStaffReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateSimpleStaffReq) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UpdateSimpleStaffReq) GetEntryDate() int64 {
	if x != nil {
		return x.EntryDate
	}
	return 0
}

func (x *UpdateSimpleStaffReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateSimpleStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *UpdateSimpleStaffReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UpdateSimpleStaffReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateSimpleStaffReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *UpdateSimpleStaffReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type ChangeStaffMobileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 老师id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 原手机号
	OldMobile string `protobuf:"bytes,2,opt,name=old_mobile,json=oldMobile,proto3" json:"old_mobile,omitempty"`
	// 新手机区号
	ReCode string `protobuf:"bytes,3,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 新手机号
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,5,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,6,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,7,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,8,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,9,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *ChangeStaffMobileReq) Reset() {
	*x = ChangeStaffMobileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeStaffMobileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeStaffMobileReq) ProtoMessage() {}

func (x *ChangeStaffMobileReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeStaffMobileReq.ProtoReflect.Descriptor instead.
func (*ChangeStaffMobileReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{14}
}

func (x *ChangeStaffMobileReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeStaffMobileReq) GetOldMobile() string {
	if x != nil {
		return x.OldMobile
	}
	return ""
}

func (x *ChangeStaffMobileReq) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *ChangeStaffMobileReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *ChangeStaffMobileReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ChangeStaffMobileReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ChangeStaffMobileReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *ChangeStaffMobileReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ChangeStaffMobileReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 离职老师请求参数
type LeaveStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 老师id
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 离职时间，时间戳：秒
	LeaveTime int64 `protobuf:"varint,2,opt,name=leave_time,json=leaveTime,proto3" json:"leave_time,omitempty"`
	// 离职原因
	Reason string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *LeaveStaffReq) Reset() {
	*x = LeaveStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaveStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveStaffReq) ProtoMessage() {}

func (x *LeaveStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveStaffReq.ProtoReflect.Descriptor instead.
func (*LeaveStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{15}
}

func (x *LeaveStaffReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *LeaveStaffReq) GetLeaveTime() int64 {
	if x != nil {
		return x.LeaveTime
	}
	return 0
}

func (x *LeaveStaffReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *LeaveStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *LeaveStaffReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *LeaveStaffReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *LeaveStaffReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *LeaveStaffReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 离职老师返回参数
type LeaveStaffRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 失败列表，若为空则表示全部成功
	FailureList []*StaffFailure `protobuf:"bytes,1,rep,name=failure_list,json=failureList,proto3" json:"failure_list,omitempty"`
}

func (x *LeaveStaffRsp) Reset() {
	*x = LeaveStaffRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaveStaffRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveStaffRsp) ProtoMessage() {}

func (x *LeaveStaffRsp) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveStaffRsp.ProtoReflect.Descriptor instead.
func (*LeaveStaffRsp) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{16}
}

func (x *LeaveStaffRsp) GetFailureList() []*StaffFailure {
	if x != nil {
		return x.FailureList
	}
	return nil
}

type StaffFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 老师id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 老师姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 失败原因
	Reason string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *StaffFailure) Reset() {
	*x = StaffFailure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffFailure) ProtoMessage() {}

func (x *StaffFailure) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffFailure.ProtoReflect.Descriptor instead.
func (*StaffFailure) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{17}
}

func (x *StaffFailure) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffFailure) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StaffFailure) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 删除老师请求参数
type DeleteStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *DeleteStaffReq) Reset() {
	*x = DeleteStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffReq) ProtoMessage() {}

func (x *DeleteStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffReq.ProtoReflect.Descriptor instead.
func (*DeleteStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteStaffReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *DeleteStaffReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *DeleteStaffReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *DeleteStaffReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *DeleteStaffReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 获取老师列表请求参数
type ListStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,3,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 状态（1：在职，2：离职） 不传默认为1
	Status uint32 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	// 老师姓名
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// 老师手机号
	Mobile string `protobuf:"bytes,6,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 入职起始时间
	EntryStartDate int64 `protobuf:"varint,7,opt,name=entry_start_date,json=entryStartDate,proto3" json:"entry_start_date,omitempty"`
	// 入职结束时间
	EntryEndDate int64 `protobuf:"varint,8,opt,name=entry_end_date,json=entryEndDate,proto3" json:"entry_end_date,omitempty"`
	// 部门id
	DeptIds []int64 `protobuf:"varint,10,rep,packed,name=dept_ids,json=deptIds,proto3" json:"dept_ids,omitempty"`
	// 角色id
	RoleIds []int64 `protobuf:"varint,11,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	// 考勤卡
	CardNo string `protobuf:"bytes,12,opt,name=card_no,json=cardNo,proto3" json:"card_no,omitempty"`
	// 离职起始时间，时间戳 单位：秒
	LeaveStartDate int64 `protobuf:"varint,13,opt,name=leave_start_date,json=leaveStartDate,proto3" json:"leave_start_date,omitempty"`
	// 离职结束时间，时间戳 单位：秒
	LeaveEndDate int64 `protobuf:"varint,14,opt,name=leave_end_date,json=leaveEndDate,proto3" json:"leave_end_date,omitempty"`
}

func (x *ListStaffReq) Reset() {
	*x = ListStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffReq) ProtoMessage() {}

func (x *ListStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffReq.ProtoReflect.Descriptor instead.
func (*ListStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{19}
}

func (x *ListStaffReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListStaffReq) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ListStaffReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListStaffReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListStaffReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *ListStaffReq) GetEntryStartDate() int64 {
	if x != nil {
		return x.EntryStartDate
	}
	return 0
}

func (x *ListStaffReq) GetEntryEndDate() int64 {
	if x != nil {
		return x.EntryEndDate
	}
	return 0
}

func (x *ListStaffReq) GetDeptIds() []int64 {
	if x != nil {
		return x.DeptIds
	}
	return nil
}

func (x *ListStaffReq) GetRoleIds() []int64 {
	if x != nil {
		return x.RoleIds
	}
	return nil
}

func (x *ListStaffReq) GetCardNo() string {
	if x != nil {
		return x.CardNo
	}
	return ""
}

func (x *ListStaffReq) GetLeaveStartDate() int64 {
	if x != nil {
		return x.LeaveStartDate
	}
	return 0
}

func (x *ListStaffReq) GetLeaveEndDate() int64 {
	if x != nil {
		return x.LeaveEndDate
	}
	return 0
}

// 查询老师返回参数
type ListStaffRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总条数
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 当前页
	Page int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数
	PerPage uint32 `protobuf:"varint,3,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// 老师信息列表
	List []*StaffInfo `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListStaffRsp) Reset() {
	*x = ListStaffRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffRsp) ProtoMessage() {}

func (x *ListStaffRsp) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffRsp.ProtoReflect.Descriptor instead.
func (*ListStaffRsp) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{20}
}

func (x *ListStaffRsp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListStaffRsp) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListStaffRsp) GetPerPage() uint32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListStaffRsp) GetList() []*StaffInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type StaffInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 姓名
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 称谓
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// 性别，0表示未知，1表示男性，2表示女性
	Gender uint32 `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	// 电话区号
	ReCode string `protobuf:"bytes,5,opt,name=re_code,json=reCode,proto3" json:"re_code,omitempty"`
	// 手机号
	Mobile string `protobuf:"bytes,6,opt,name=mobile,proto3" json:"mobile,omitempty"`
	// 多音字
	Polyphone string `protobuf:"bytes,7,opt,name=polyphone,proto3" json:"polyphone,omitempty"`
	// 角色
	Role *StaffInfo_RoleInfo `protobuf:"bytes,8,opt,name=role,proto3" json:"role,omitempty"`
	// 部门
	DeptList []*StaffInfo_DeptInfo `protobuf:"bytes,9,rep,name=dept_list,json=deptList,proto3" json:"dept_list,omitempty"`
	// 任教班级
	TeachList []*StaffInfo_TeachInfo `protobuf:"bytes,10,rep,name=teach_list,json=teachList,proto3" json:"teach_list,omitempty"`
	// 来源
	Source int32 `protobuf:"varint,11,opt,name=source,proto3" json:"source,omitempty"`
	// 入职日期
	EntryDate int64 `protobuf:"varint,12,opt,name=entry_date,json=entryDate,proto3" json:"entry_date,omitempty"`
	// 离职日期
	LeaveDate int64 `protobuf:"varint,13,opt,name=leave_date,json=leaveDate,proto3" json:"leave_date,omitempty"`
	// 离职原因
	LeaveReason string `protobuf:"bytes,14,opt,name=leave_reason,json=leaveReason,proto3" json:"leave_reason,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,15,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 绑定考勤卡列表
	CardList []string `protobuf:"bytes,16,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
	// 最后一次登陆时间
	LastLoginTime int64 `protobuf:"varint,17,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time,omitempty"`
	// 称谓
	Alias string `protobuf:"bytes,18,opt,name=alias,proto3" json:"alias,omitempty"`
	// 是否已离职
	IsLeave bool `protobuf:"varint,19,opt,name=is_leave,json=isLeave,proto3" json:"is_leave,omitempty"`
}

func (x *StaffInfo) Reset() {
	*x = StaffInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffInfo) ProtoMessage() {}

func (x *StaffInfo) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffInfo.ProtoReflect.Descriptor instead.
func (*StaffInfo) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{21}
}

func (x *StaffInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StaffInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *StaffInfo) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *StaffInfo) GetReCode() string {
	if x != nil {
		return x.ReCode
	}
	return ""
}

func (x *StaffInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *StaffInfo) GetPolyphone() string {
	if x != nil {
		return x.Polyphone
	}
	return ""
}

func (x *StaffInfo) GetRole() *StaffInfo_RoleInfo {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *StaffInfo) GetDeptList() []*StaffInfo_DeptInfo {
	if x != nil {
		return x.DeptList
	}
	return nil
}

func (x *StaffInfo) GetTeachList() []*StaffInfo_TeachInfo {
	if x != nil {
		return x.TeachList
	}
	return nil
}

func (x *StaffInfo) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *StaffInfo) GetEntryDate() int64 {
	if x != nil {
		return x.EntryDate
	}
	return 0
}

func (x *StaffInfo) GetLeaveDate() int64 {
	if x != nil {
		return x.LeaveDate
	}
	return 0
}

func (x *StaffInfo) GetLeaveReason() string {
	if x != nil {
		return x.LeaveReason
	}
	return ""
}

func (x *StaffInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *StaffInfo) GetCardList() []string {
	if x != nil {
		return x.CardList
	}
	return nil
}

func (x *StaffInfo) GetLastLoginTime() int64 {
	if x != nil {
		return x.LastLoginTime
	}
	return 0
}

func (x *StaffInfo) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *StaffInfo) GetIsLeave() bool {
	if x != nil {
		return x.IsLeave
	}
	return false
}

// 老师重新入职请求参数
type ReEntryStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 老师id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 部门id
	DeptIds []int64 `protobuf:"varint,2,rep,packed,name=dept_ids,json=deptIds,proto3" json:"dept_ids,omitempty"`
	// 角色id
	RoleId int64 `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,4,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,7,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *ReEntryStaffReq) Reset() {
	*x = ReEntryStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReEntryStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReEntryStaffReq) ProtoMessage() {}

func (x *ReEntryStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReEntryStaffReq.ProtoReflect.Descriptor instead.
func (*ReEntryStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{22}
}

func (x *ReEntryStaffReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReEntryStaffReq) GetDeptIds() []int64 {
	if x != nil {
		return x.DeptIds
	}
	return nil
}

func (x *ReEntryStaffReq) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *ReEntryStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ReEntryStaffReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ReEntryStaffReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *ReEntryStaffReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ReEntryStaffReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 重置密码
type ResetStaffPasswordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 学校id
	InstId int64 `protobuf:"varint,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 渠道 （1：园长后台，2：app端）
	Channel uint32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	// 创建人id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 创建人姓名
	OperatorName string `protobuf:"bytes,5,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *ResetStaffPasswordReq) Reset() {
	*x = ResetStaffPasswordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetStaffPasswordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetStaffPasswordReq) ProtoMessage() {}

func (x *ResetStaffPasswordReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetStaffPasswordReq.ProtoReflect.Descriptor instead.
func (*ResetStaffPasswordReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{23}
}

func (x *ResetStaffPasswordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResetStaffPasswordReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *ResetStaffPasswordReq) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ResetStaffPasswordReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *ResetStaffPasswordReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ResetStaffPasswordReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 批量获取老师信息
type FindStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 老师ID
	StaffId []int64 `protobuf:"varint,2,rep,packed,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *FindStaffReq) Reset() {
	*x = FindStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStaffReq) ProtoMessage() {}

func (x *FindStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStaffReq.ProtoReflect.Descriptor instead.
func (*FindStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{24}
}

func (x *FindStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *FindStaffReq) GetStaffId() []int64 {
	if x != nil {
		return x.StaffId
	}
	return nil
}

type FindStaffRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 老师信息列表
	Data []*StaffBasicInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *FindStaffRsp) Reset() {
	*x = FindStaffRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStaffRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStaffRsp) ProtoMessage() {}

func (x *FindStaffRsp) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStaffRsp.ProtoReflect.Descriptor instead.
func (*FindStaffRsp) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{25}
}

func (x *FindStaffRsp) GetData() []*StaffBasicInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取单个老师信息
type GetStaffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 学校id
	InstId int64 `protobuf:"varint,1,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	// 老师ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *GetStaffReq) Reset() {
	*x = GetStaffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffReq) ProtoMessage() {}

func (x *GetStaffReq) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffReq.ProtoReflect.Descriptor instead.
func (*GetStaffReq) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{26}
}

func (x *GetStaffReq) GetInstId() int64 {
	if x != nil {
		return x.InstId
	}
	return 0
}

func (x *GetStaffReq) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

type StaffInfo_RoleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 角色id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 角色名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 角色编号
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	// 是否是管理员
	IsAdmin bool `protobuf:"varint,4,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin,omitempty"`
	// 是否是内置角色
	IsBuiltin bool `protobuf:"varint,5,opt,name=is_builtin,json=isBuiltin,proto3" json:"is_builtin,omitempty"`
	// 数据范围(1：全园，2：关联班级）
	DataScope int32 `protobuf:"varint,6,opt,name=data_scope,json=dataScope,proto3" json:"data_scope,omitempty"`
}

func (x *StaffInfo_RoleInfo) Reset() {
	*x = StaffInfo_RoleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffInfo_RoleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffInfo_RoleInfo) ProtoMessage() {}

func (x *StaffInfo_RoleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffInfo_RoleInfo.ProtoReflect.Descriptor instead.
func (*StaffInfo_RoleInfo) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{21, 0}
}

func (x *StaffInfo_RoleInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffInfo_RoleInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StaffInfo_RoleInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *StaffInfo_RoleInfo) GetIsAdmin() bool {
	if x != nil {
		return x.IsAdmin
	}
	return false
}

func (x *StaffInfo_RoleInfo) GetIsBuiltin() bool {
	if x != nil {
		return x.IsBuiltin
	}
	return false
}

func (x *StaffInfo_RoleInfo) GetDataScope() int32 {
	if x != nil {
		return x.DataScope
	}
	return 0
}

type StaffInfo_DeptInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 部门id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 部门名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *StaffInfo_DeptInfo) Reset() {
	*x = StaffInfo_DeptInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffInfo_DeptInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffInfo_DeptInfo) ProtoMessage() {}

func (x *StaffInfo_DeptInfo) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffInfo_DeptInfo.ProtoReflect.Descriptor instead.
func (*StaffInfo_DeptInfo) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{21, 1}
}

func (x *StaffInfo_DeptInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffInfo_DeptInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type StaffInfo_TeachInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 班级id
	ClassId int64 `protobuf:"varint,1,opt,name=class_id,json=classId,proto3" json:"class_id,omitempty"`
	// 班级名称
	ClassName string `protobuf:"bytes,2,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`
	// 年级名称
	GradeName string `protobuf:"bytes,3,opt,name=grade_name,json=gradeName,proto3" json:"grade_name,omitempty"`
}

func (x *StaffInfo_TeachInfo) Reset() {
	*x = StaffInfo_TeachInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_staff_v1_staff_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffInfo_TeachInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffInfo_TeachInfo) ProtoMessage() {}

func (x *StaffInfo_TeachInfo) ProtoReflect() protoreflect.Message {
	mi := &file_staff_v1_staff_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffInfo_TeachInfo.ProtoReflect.Descriptor instead.
func (*StaffInfo_TeachInfo) Descriptor() ([]byte, []int) {
	return file_staff_v1_staff_proto_rawDescGZIP(), []int{21, 2}
}

func (x *StaffInfo_TeachInfo) GetClassId() int64 {
	if x != nil {
		return x.ClassId
	}
	return 0
}

func (x *StaffInfo_TeachInfo) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *StaffInfo_TeachInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

var File_staff_v1_staff_proto protoreflect.FileDescriptor

var file_staff_v1_staff_proto_rawDesc = []byte{
	0x0a, 0x14, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x86, 0x01, 0x0a, 0x14, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69,
	0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x07, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x04, 0x52, 0x06, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72, 0x0b,
	0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31, 0x30, 0x7d, 0x24, 0x52, 0x06, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x22, 0x2c, 0x0a, 0x14, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65, 0x78, 0x69, 0x73,
	0x74, 0x22, 0x55, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69,
	0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x43, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x52, 0x0a,
	0x0f, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x3b, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x22, 0x51,
	0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xa0, 0x01, 0x0a, 0x14, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x72,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x22, 0x3b, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x22, 0x6d, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x0f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x75, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0f, 0x75, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0xe9, 0x04, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69,
	0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x07, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x04,
	0x52, 0x06, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72, 0x0b, 0x32,
	0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31, 0x30, 0x7d, 0x24, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x00, 0x30, 0x01, 0x30, 0x02, 0x52,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x70, 0x6f, 0x6c, 0x79, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x32, 0x52, 0x09, 0x70, 0x6f, 0x6c, 0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x1f,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x0a, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x07, 0x64,
	0x65, 0x70, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49,
	0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02,
	0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xad, 0x03, 0x0a,
	0x0e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x61, 0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x70, 0x6f, 0x6c, 0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x6f, 0x6c, 0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x64, 0x65, 0x70, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x26,
	0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x22, 0xc5, 0x04, 0x0a,
	0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x63, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x07,
	0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x04, 0x52, 0x06, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x28, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72, 0x0b, 0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31, 0x30,
	0x7d, 0x24, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a,
	0x06, 0x30, 0x00, 0x30, 0x01, 0x30, 0x02, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12,
	0x1f, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x0a, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x25, 0x0a, 0x09, 0x70, 0x6f, 0x6c, 0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x09, 0x70, 0x6f,
	0x6c, 0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28,
	0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x64, 0x65, 0x70,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x07, 0x64, 0x65, 0x70, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14,
	0x52, 0x02, 0x69, 0x70, 0x22, 0xe4, 0x02, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x00, 0x30, 0x01,
	0x30, 0x02, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xe8, 0x02, 0x0a, 0x14,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a,
	0x0a, 0x6f, 0x6c, 0x64, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72, 0x0b, 0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b, 0x31,
	0x30, 0x7d, 0x24, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x22,
	0x0a, 0x07, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x04, 0x52, 0x06, 0x72, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x72, 0x0b, 0x32, 0x09, 0x5e, 0x31, 0x5c, 0x64, 0x7b,
	0x31, 0x30, 0x7d, 0x24, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x07,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xb0, 0x02, 0x0a, 0x0d, 0x4c, 0x65, 0x61, 0x76, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0x4e, 0x0a, 0x0d, 0x4c, 0x65, 0x61,
	0x76, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x0c, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x0b, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x4a, 0x0a, 0x0c, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xe3, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xd7, 0x03, 0x0a, 0x0c,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x22, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x00, 0x52, 0x07, 0x70, 0x65, 0x72,
	0x50, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x2a, 0x06, 0x30, 0x00, 0x30,
	0x01, 0x30, 0x02, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x00, 0x18, 0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x12, 0x31, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x64, 0x65, 0x70, 0x74, 0x49, 0x64, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x6e, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x4e, 0x6f, 0x12, 0x31, 0x0a, 0x10, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0e, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x0e, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x5f,
	0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0c, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x45, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x9e, 0x07, 0x0a, 0x09, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f, 0x6c,
	0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6f,
	0x6c, 0x79, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x52,
	0x6f, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x3d, 0x0a,
	0x09, 0x64, 0x65, 0x70, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x44, 0x65, 0x70, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x64, 0x65, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0a,
	0x74, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x54, 0x65, 0x61, 0x63, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x65, 0x61, 0x76, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x65, 0x61, 0x76,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x5f, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x4c, 0x65, 0x61, 0x76, 0x65, 0x1a, 0x9b, 0x01, 0x0a, 0x08, 0x52, 0x6f, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x62, 0x75, 0x69, 0x6c,
	0x74, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x42, 0x75, 0x69,
	0x6c, 0x74, 0x69, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x53, 0x63,
	0x6f, 0x70, 0x65, 0x1a, 0x2e, 0x0a, 0x08, 0x44, 0x65, 0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x1a, 0x64, 0x0a, 0x09, 0x54, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xab, 0x02, 0x0a, 0x0f, 0x52, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x07, 0x64, 0x65, 0x70, 0x74, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x72,
	0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x14, 0x52, 0x02, 0x69, 0x70, 0x22, 0xea, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x2a, 0x04, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14,
	0x52, 0x02, 0x69, 0x70, 0x22, 0x55, 0x0a, 0x0c, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x06,
	0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x0c, 0x46,
	0x69, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x61,
	0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x53, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07,
	0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x74, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x32, 0xc1, 0x09, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x4b, 0x0a, 0x0b,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x1c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x61,
	0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x10, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x63, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x69, 0x63, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x11, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x51, 0x0a,
	0x11, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x48, 0x0a, 0x0a, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x65,
	0x61, 0x76, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x65, 0x61, 0x76, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0c, 0x52, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x09, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x4e, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x12, 0x45, 0x0a, 0x09, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x1a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69,
	0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x40, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x12, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x1a,
	0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x66, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65,
	0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0b, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x11, 0x5a, 0x0f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_staff_v1_staff_proto_rawDescOnce sync.Once
	file_staff_v1_staff_proto_rawDescData = file_staff_v1_staff_proto_rawDesc
)

func file_staff_v1_staff_proto_rawDescGZIP() []byte {
	file_staff_v1_staff_proto_rawDescOnce.Do(func() {
		file_staff_v1_staff_proto_rawDescData = protoimpl.X.CompressGZIP(file_staff_v1_staff_proto_rawDescData)
	})
	return file_staff_v1_staff_proto_rawDescData
}

var file_staff_v1_staff_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_staff_v1_staff_proto_goTypes = []interface{}{
	(*VerifyStaffMobileReq)(nil),    // 0: api.staff.v1.VerifyStaffMobileReq
	(*VerifyStaffMobileRsp)(nil),    // 1: api.staff.v1.VerifyStaffMobileRsp
	(*SearchStaffReq)(nil),          // 2: api.staff.v1.SearchStaffReq
	(*SearchStaffRsp)(nil),          // 3: api.staff.v1.SearchStaffRsp
	(*SimpleStaffInfo)(nil),         // 4: api.staff.v1.SimpleStaffInfo
	(*ListUnActivatedStaffReq)(nil), // 5: api.staff.v1.ListUnActivatedStaffReq
	(*ListUnActivatedStaffRsp)(nil), // 6: api.staff.v1.ListUnActivatedStaffRsp
	(*UnActivatedStaffInfo)(nil),    // 7: api.staff.v1.UnActivatedStaffInfo
	(*GetStaffActivateInfoReq)(nil), // 8: api.staff.v1.GetStaffActivateInfoReq
	(*GetStaffActivateInfoRsp)(nil), // 9: api.staff.v1.GetStaffActivateInfoRsp
	(*CreateStaffReq)(nil),          // 10: api.staff.v1.CreateStaffReq
	(*StaffBasicInfo)(nil),          // 11: api.staff.v1.StaffBasicInfo
	(*UpdateBasicStaffReq)(nil),     // 12: api.staff.v1.UpdateBasicStaffReq
	(*UpdateSimpleStaffReq)(nil),    // 13: api.staff.v1.UpdateSimpleStaffReq
	(*ChangeStaffMobileReq)(nil),    // 14: api.staff.v1.ChangeStaffMobileReq
	(*LeaveStaffReq)(nil),           // 15: api.staff.v1.LeaveStaffReq
	(*LeaveStaffRsp)(nil),           // 16: api.staff.v1.LeaveStaffRsp
	(*StaffFailure)(nil),            // 17: api.staff.v1.StaffFailure
	(*DeleteStaffReq)(nil),          // 18: api.staff.v1.DeleteStaffReq
	(*ListStaffReq)(nil),            // 19: api.staff.v1.ListStaffReq
	(*ListStaffRsp)(nil),            // 20: api.staff.v1.ListStaffRsp
	(*StaffInfo)(nil),               // 21: api.staff.v1.StaffInfo
	(*ReEntryStaffReq)(nil),         // 22: api.staff.v1.ReEntryStaffReq
	(*ResetStaffPasswordReq)(nil),   // 23: api.staff.v1.ResetStaffPasswordReq
	(*FindStaffReq)(nil),            // 24: api.staff.v1.FindStaffReq
	(*FindStaffRsp)(nil),            // 25: api.staff.v1.FindStaffRsp
	(*GetStaffReq)(nil),             // 26: api.staff.v1.GetStaffReq
	(*StaffInfo_RoleInfo)(nil),      // 27: api.staff.v1.StaffInfo.RoleInfo
	(*StaffInfo_DeptInfo)(nil),      // 28: api.staff.v1.StaffInfo.DeptInfo
	(*StaffInfo_TeachInfo)(nil),     // 29: api.staff.v1.StaffInfo.TeachInfo
	(*emptypb.Empty)(nil),           // 30: google.protobuf.Empty
}
var file_staff_v1_staff_proto_depIdxs = []int32{
	4,  // 0: api.staff.v1.SearchStaffRsp.list:type_name -> api.staff.v1.SimpleStaffInfo
	7,  // 1: api.staff.v1.ListUnActivatedStaffRsp.list:type_name -> api.staff.v1.UnActivatedStaffInfo
	17, // 2: api.staff.v1.LeaveStaffRsp.failure_list:type_name -> api.staff.v1.StaffFailure
	21, // 3: api.staff.v1.ListStaffRsp.list:type_name -> api.staff.v1.StaffInfo
	27, // 4: api.staff.v1.StaffInfo.role:type_name -> api.staff.v1.StaffInfo.RoleInfo
	28, // 5: api.staff.v1.StaffInfo.dept_list:type_name -> api.staff.v1.StaffInfo.DeptInfo
	29, // 6: api.staff.v1.StaffInfo.teach_list:type_name -> api.staff.v1.StaffInfo.TeachInfo
	11, // 7: api.staff.v1.FindStaffRsp.data:type_name -> api.staff.v1.StaffBasicInfo
	10, // 8: api.staff.v1.Staff.CreateStaff:input_type -> api.staff.v1.CreateStaffReq
	12, // 9: api.staff.v1.Staff.UpdateBasicStaff:input_type -> api.staff.v1.UpdateBasicStaffReq
	13, // 10: api.staff.v1.Staff.UpdateSimpleStaff:input_type -> api.staff.v1.UpdateSimpleStaffReq
	14, // 11: api.staff.v1.Staff.ChangeStaffMobile:input_type -> api.staff.v1.ChangeStaffMobileReq
	15, // 12: api.staff.v1.Staff.LeaveStaff:input_type -> api.staff.v1.LeaveStaffReq
	22, // 13: api.staff.v1.Staff.ReEntryStaff:input_type -> api.staff.v1.ReEntryStaffReq
	18, // 14: api.staff.v1.Staff.DeleteStaff:input_type -> api.staff.v1.DeleteStaffReq
	19, // 15: api.staff.v1.Staff.ListStaff:input_type -> api.staff.v1.ListStaffReq
	23, // 16: api.staff.v1.Staff.ResetPassword:input_type -> api.staff.v1.ResetStaffPasswordReq
	24, // 17: api.staff.v1.Staff.FindStaff:input_type -> api.staff.v1.FindStaffReq
	26, // 18: api.staff.v1.Staff.GetStaff:input_type -> api.staff.v1.GetStaffReq
	8,  // 19: api.staff.v1.Staff.GetStaffActivateInfo:input_type -> api.staff.v1.GetStaffActivateInfoReq
	5,  // 20: api.staff.v1.Staff.ListUnActivatedStaff:input_type -> api.staff.v1.ListUnActivatedStaffReq
	2,  // 21: api.staff.v1.Staff.SearchStaff:input_type -> api.staff.v1.SearchStaffReq
	0,  // 22: api.staff.v1.Staff.VerifyStaffMobile:input_type -> api.staff.v1.VerifyStaffMobileReq
	11, // 23: api.staff.v1.Staff.CreateStaff:output_type -> api.staff.v1.StaffBasicInfo
	30, // 24: api.staff.v1.Staff.UpdateBasicStaff:output_type -> google.protobuf.Empty
	30, // 25: api.staff.v1.Staff.UpdateSimpleStaff:output_type -> google.protobuf.Empty
	30, // 26: api.staff.v1.Staff.ChangeStaffMobile:output_type -> google.protobuf.Empty
	16, // 27: api.staff.v1.Staff.LeaveStaff:output_type -> api.staff.v1.LeaveStaffRsp
	30, // 28: api.staff.v1.Staff.ReEntryStaff:output_type -> google.protobuf.Empty
	30, // 29: api.staff.v1.Staff.DeleteStaff:output_type -> google.protobuf.Empty
	20, // 30: api.staff.v1.Staff.ListStaff:output_type -> api.staff.v1.ListStaffRsp
	30, // 31: api.staff.v1.Staff.ResetPassword:output_type -> google.protobuf.Empty
	25, // 32: api.staff.v1.Staff.FindStaff:output_type -> api.staff.v1.FindStaffRsp
	21, // 33: api.staff.v1.Staff.GetStaff:output_type -> api.staff.v1.StaffInfo
	9,  // 34: api.staff.v1.Staff.GetStaffActivateInfo:output_type -> api.staff.v1.GetStaffActivateInfoRsp
	6,  // 35: api.staff.v1.Staff.ListUnActivatedStaff:output_type -> api.staff.v1.ListUnActivatedStaffRsp
	3,  // 36: api.staff.v1.Staff.SearchStaff:output_type -> api.staff.v1.SearchStaffRsp
	1,  // 37: api.staff.v1.Staff.VerifyStaffMobile:output_type -> api.staff.v1.VerifyStaffMobileRsp
	23, // [23:38] is the sub-list for method output_type
	8,  // [8:23] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_staff_v1_staff_proto_init() }
func file_staff_v1_staff_proto_init() {
	if File_staff_v1_staff_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_staff_v1_staff_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyStaffMobileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyStaffMobileRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchStaffRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimpleStaffInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnActivatedStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnActivatedStaffRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnActivatedStaffInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffActivateInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffActivateInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffBasicInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBasicStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSimpleStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeStaffMobileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaveStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaveStaffRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffFailure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReEntryStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetStaffPasswordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStaffRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffInfo_RoleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffInfo_DeptInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_staff_v1_staff_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffInfo_TeachInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_staff_v1_staff_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_staff_v1_staff_proto_goTypes,
		DependencyIndexes: file_staff_v1_staff_proto_depIdxs,
		MessageInfos:      file_staff_v1_staff_proto_msgTypes,
	}.Build()
	File_staff_v1_staff_proto = out.File
	file_staff_v1_staff_proto_rawDesc = nil
	file_staff_v1_staff_proto_goTypes = nil
	file_staff_v1_staff_proto_depIdxs = nil
}
