package localize

import (
	"bytes"
	"context"
	"embed"
	"github.com/BurntSushi/toml"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/metadata"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
	"strconv"
)

type localizerKey struct{}

//go:embed locale.*.toml
var localeFs embed.FS
var bundle *i18n.Bundle

func init() {
	bundle = i18n.NewBundle(language.Chinese)
	bundle.RegisterUnmarshalFunc("toml", toml.Unmarshal)
	_, err := bundle.LoadMessageFileFS(localeFs, "locale.en.toml")
	if err != nil {
		panic(err)
	}
	_, err = bundle.LoadMessageFileFS(localeFs, "locale.zh.toml")
	if err != nil {
		panic(err)
	}
}

func I18N() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if md, ok := metadata.FromServerContext(ctx); ok {
				lang := md.Get("x-md-global-language")
				localizer := i18n.NewLocalizer(bundle, lang)
				ctx = context.WithValue(ctx, localizerKey{}, localizer)
			}
			return handler(ctx, req)
		}
	}
}

func FromContext(ctx context.Context) *i18n.Localizer {
	val := ctx.Value(localizerKey{})
	if val == nil {
		// 默认中文环境
		localizer := i18n.NewLocalizer(bundle, "zh")
		ctx = context.WithValue(ctx, localizerKey{}, localizer)
		val = localizer
	}
	return val.(*i18n.Localizer)
}

// GetCodeMsg 根据语言获取代码描述
func GetCodeMsg(ctx context.Context, code int64) string {
	var buffer bytes.Buffer
	buffer.WriteString("code_")
	buffer.WriteString(strconv.FormatInt(int64(code), 10))
	return GetMessage(ctx, buffer.String(), nil)
}

// GetMessage 根据语言获取描述信息
func GetMessage(ctx context.Context, code string, args map[string]interface{}) string {
	if localizer := FromContext(ctx); localizer != nil {
		message, err := localizer.Localize(&i18n.LocalizeConfig{
			MessageID:    code,
			TemplateData: args,
		})
		if err != nil {
			log.Errorw("msg", "localize error", "code", code, "error", err.Error())
		}
		return message
	} else {
		return ""
	}
}
