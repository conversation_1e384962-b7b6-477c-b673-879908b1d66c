package mq

import "github.com/go-kratos/kratos/v2/log"

type Logger struct {
	log *log.Helper
}

func NewLogger(logger log.Logger, filterLevel log.Level) Logger {
	return Logger{log: log.<PERSON><PERSON>per(log.<PERSON>Filter(logger, log.FilterLevel(filterLevel)))}
}

func (l Logger) Debug(msg string, fields map[string]interface{}) {
	l.log.Debug(msg)
}

func (l Logger) Info(msg string, fields map[string]interface{}) {
	l.log.Info(msg)
}

func (l Logger) Warning(msg string, fields map[string]interface{}) {
	l.log.Warn(msg)
}

func (l Logger) Error(msg string, fields map[string]interface{}) {
	l.log.Error(msg)
}

func (l Logger) Fatal(msg string, fields map[string]interface{}) {
	l.log.Fatal(msg)
}

func (l Logger) Level(level string) {

}

func (l Logger) OutputPath(path string) (err error) {
	return nil
}
