package mq

import (
	"context"
	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/go-kratos/kratos/v2/log"
)

type Producer struct {
	p   rocketmq.Producer
	log *log.Helper
}

func NewProducer(producer rocketmq.Producer, logger log.Logger) *Producer {
	return &Producer{
		p:   producer,
		log: log.NewHelper(log.With(logger, "module", "mq/producer")),
	}
}

func (p *Producer) SyncSend(ctx context.Context, msg ...*primitive.Message) error {
	// TODO 增加重试机制

	ret, err := p.p.SendSync(ctx, msg...)
	if err != nil {
		p.log.WithContext(ctx).Errorw("msg", "send message failed.", "err", err.<PERSON>rror())
		return err
	}

	if ret.Status != primitive.SendOK {
		// 不成功
	}

	return nil
}
