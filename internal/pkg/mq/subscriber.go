package mq

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/gookit/goutil/strutil"
	"github.com/redis/go-redis/v9"
	"strings"
	"time"
)

type (
	NormalConsumer  rocketmq.PushConsumer
	OrderlyConsumer rocketmq.PushConsumer
)

type Subscriber[T any] struct {
	handlerMap  map[string]MessageHandler[T]
	redisClient *redis.Client
	log         *log.Helper
	consumer    rocketmq.PushConsumer
	// redis 防重key前缀
	keyPrefix string
	topic     string
	retries   int32
	orderly   bool
}

// MessageHandler MQ消息处理函数
type MessageHandler[T any] func(context.Context, *T) error

// RegisterMessageHandler 根据事件类型注册相应的处理函数
func (m *Subscriber[T]) RegisterMessageHandler(eventType string, fn MessageHandler[T]) {
	if _, ok := m.handlerMap[eventType]; ok {
		panic(fmt.Sprintf("handler for event type: %s already registered", eventType))
	}
	m.handlerMap[eventType] = fn
}

func (m *Subscriber[T]) Subscribe() error {
	err := m.consumer.Subscribe(m.topic, consumer.MessageSelector{}, func(ctx context.Context, msgs ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
		for _, msg := range msgs {
			err := m.handle(ctx, msg)
			if err != nil {
				m.log.WithContext(ctx).Errorf("[rocketmq]: process message failed: %v", err)
				if m.orderly {
					return consumer.SuspendCurrentQueueAMoment, nil
				}
				return consumer.ConsumeRetryLater, nil
			}
		}
		return consumer.ConsumeSuccess, nil
	})

	if err != nil {
		m.log.Errorf("Subscribe topic: %s failed, err: %v", m.topic, err)
	}

	return err
}

func (m *Subscriber[T]) handle(ctx context.Context, msg *primitive.MessageExt) error {
	var event T
	decoder := json.NewDecoder(strings.NewReader(string(msg.Body)))
	decoder.UseNumber()
	if err := decoder.Decode(&event); err != nil {
		m.log.WithContext(ctx).Errorf("json.Decode mq message failed, err: %v", err)
		return nil
	}

	handledKey := fmt.Sprintf("%s:handled:%s:%s", m.keyPrefix, m.topic, msg.MsgId)
	failKey := fmt.Sprintf("%s:retry:%s:%s", m.keyPrefix, m.topic, msg.MsgId)
	// 判断消息是否已处理
	val, err := m.redisClient.Get(ctx, handledKey).Result()
	if err != nil && err != redis.Nil {
		m.log.WithContext(ctx).Errorf("redis.Get failed, key: %s, msgId: %s, err: %v", handledKey, msg.MsgId, err)
		return err
	}

	if strutil.IsNotBlank(val) {
		// 重复消费，直接返回成功
		return nil
	}

	// 类判判断
	var key string
	switch i := any(event).(type) {
	case EventMessage[any]:
		key = i.EventType
	case FileOptMessage[any]:
		key = i.OptType
	default:
		key = "default"
	}

	handleFun, ok := m.handlerMap[key]
	// 不在关注的事件列表中，则不做任何处理直接返回
	if !ok {
		return nil
	}

	// 调用业务中对应的消息处理方法
	err = handleFun(ctx, &event)
	if err != nil {
		// 处理失败 则等待消息的重新发送
		count, err1 := m.redisClient.Incr(ctx, failKey).Result()
		if err1 != nil && err1 != redis.Nil {
			m.log.WithContext(ctx).Errorf("redis.Incr failed, key: %s, err: %v", failKey, err1)
			return err
		}

		// 重试次数超过阈值 则不再重试
		if count > int64(m.retries) {
			// TODO 发送告警
			m.log.WithContext(ctx).Errorw("msg", "consume message retry times exceed threshold", "msgId", msg.MsgId, "body", string(msg.Body))
			return nil
		}

		// 设置失效时间
		_ = m.redisClient.Expire(ctx, failKey, 48*time.Hour).Err()
		return nil
	}

	// redis设置消息已处理标识
	err = m.redisClient.Set(ctx, handledKey, 1, 48*time.Hour).Err()
	if err != nil {
		m.log.WithContext(ctx).Warnf("redis.Set failed, key: %s, msgId: %s, err: %v", handledKey, msg.MsgId, err)
	}

	return nil
}

func NewSubscriber[T any](logger log.Logger, redisClient *redis.Client, consumer rocketmq.PushConsumer, keyPrefix string, topic string, retries int32, orderly bool) *Subscriber[T] {
	s := &Subscriber[T]{
		handlerMap:  make(map[string]MessageHandler[T]),
		log:         log.NewHelper(log.With(logger, "module", "mq/consumer")),
		redisClient: redisClient,
		consumer:    consumer,
		keyPrefix:   keyPrefix,
		topic:       topic,
		retries:     retries,
		orderly:     orderly,
	}
	return s
}
