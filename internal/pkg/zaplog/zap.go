package zaplog

import (
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"strings"
)

var _ log.Logger = (*ZapLogger)(nil)

type ZapLogger struct {
	log  *zap.Logger
	Sync func() error
}

// Logger 配置zap日志,将zap日志库引入
func NewZapLogger(opts ...Option) log.Logger {
	o := &Options{
		File:       File,
		Level:      Level,
		MaxSize:    MaxSize,
		MaxAge:     MaxAge,
		Backups:    Backups,
		Stacktrace: Stacktrace,
		IsStdOut:   IsStdOut,
	}

	for _, opt := range opts {
		opt(o)
	}

	//配置zap日志库的编码器
	encoder := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		StacktraceKey:  "stack",
		EncodeTime:     zapcore.RFC3339TimeEncoder,
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	//日志切割
	writeSyncer := &lumberjack.Logger{
		Filename:   o.File,         //指定日志存储位置
		MaxSize:    int(o.MaxSize), //日志的最大大小（MB）
		MaxBackups: int(o.Backups), //日志的最大保存数量
		MaxAge:     int(o.MaxAge),  //日志文件存储最大天数
		Compress:   true,           //是否执行压缩
	}
	zapcore.AddSync(writeSyncer)

	//设置日志级别
	level := parseLevel(o.Level)
	var core zapcore.Core

	// --根据配置文件判断输出到控制台还是日志文件--
	if o.IsStdOut {
		core = zapcore.NewCore(
			zapcore.NewJSONEncoder(encoder), // 编码器配置
			zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout), zapcore.AddSync(writeSyncer)), // 打印到控制台和文件
			level, // 日志级别
		)
	} else {
		core = zapcore.NewCore(
			zapcore.NewJSONEncoder(encoder),                           // 编码器配置
			zapcore.NewMultiWriteSyncer(zapcore.AddSync(writeSyncer)), // 打印到文件
			level, // 日志级别
		)
	}
	zapLogger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(parseLevel(o.Stacktrace)), zap.AddCallerSkip(2))
	return &ZapLogger{log: zapLogger, Sync: zapLogger.Sync}
}

func parseLevel(l string) zapcore.Level {
	switch strings.ToLower(l) {
	case "panic", "dpanic":
		return zapcore.PanicLevel
	case "fatal":
		return zapcore.FatalLevel
	case "error":
		return zapcore.ErrorLevel
	case "warn", "warning":
		return zapcore.WarnLevel
	case "info":
		return zapcore.InfoLevel
	case "debug":
		return zapcore.DebugLevel
	default:
		return zapcore.DebugLevel
	}
}

// Log 实现log接口
func (l *ZapLogger) Log(level log.Level, keyvals ...interface{}) error {
	if len(keyvals) == 0 {
		return nil
	}

	if (len(keyvals) & 1) == 1 {
		keyvals = append(keyvals, "KEYVALS UNPAIRED")
	}

	var data []zap.Field
	for i := 0; i < len(keyvals); i += 2 {
		data = append(data, zap.Any(fmt.Sprint(keyvals[i]), keyvals[i+1]))
	}

	switch level {
	case log.LevelDebug:
		l.log.Debug("", data...)
	case log.LevelInfo:
		l.log.Info("", data...)
	case log.LevelWarn:
		l.log.Warn("", data...)
	case log.LevelError:
		l.log.Error("", data...)
	case log.LevelFatal:
		l.log.Fatal("", data...)
	}
	return nil
}
