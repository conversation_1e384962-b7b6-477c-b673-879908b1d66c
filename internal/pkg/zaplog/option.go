package zaplog

type Option func(*Options)

type Options struct {
	File       string // 日志文件，包含路径
	Level      string // 日志级别
	MaxSize    uint32 // 单个文件最大大小，单位：MB
	Backups    uint32 // 备份文件数最大保留数
	MaxAge     uint32 // 日志保留最大天数
	Stacktrace string // 记录堆栈的级别
	IsStdOut   bool   // 是否标准输出console输出
}

func WithFile(file string) Option {
	return func(o *Options) {
		o.File = file
	}
}

func WithLevel(level string) Option {
	return func(o *Options) {
		o.Level = level
	}
}

func WithMaxSize(mxSize uint32) Option {
	return func(o *Options) {
		o.MaxSize = mxSize
	}
}

func WithMaxAge(maxAge uint32) Option {
	return func(o *Options) {
		o.MaxAge = maxAge
	}
}

func WithBackups(backups uint32) Option {
	return func(o *Options) {
		o.Backups = backups
	}
}

func WithStacktrace(stacktrace string) Option {
	return func(o *Options) {
		o.Stacktrace = stacktrace
	}
}

func WithIsStdOut(isStdout bool) Option {
	return func(o *Options) {
		o.IsStdOut = isStdout
	}
}

// 默认参数
const (
	File       string = "/var/log/app.log" // 日志保存路径
	Level      string = "debug"            // 日志记录级别
	MaxSize    uint32 = 1024               // 日志文件大小 单位：MB
	Backups    uint32 = 100                // 保留的旧日志文件的最大数量
	MaxAge     uint32 = 30                 // 日志保存的天数
	Stacktrace string = "error"            // 记录堆栈的级别
	IsStdOut   bool   = true               // 是否标准输出console输出
)
