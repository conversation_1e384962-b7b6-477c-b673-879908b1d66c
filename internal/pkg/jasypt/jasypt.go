package jasypt

import (
	"github.com/Mystery00/go-jasypt"
	"github.com/Mystery00/go-jasypt/iv"
	"github.com/Mystery00/go-jasypt/salt"
)

const (
	// 使用算法
	ALGORITHM = "PBEWithHMACSHA512AndAES_256"
)

// Encrypt 加密字符串
// message: 加密的文本
// key: 密钥
func Encrypt(text string, key string) (string, error) {
	// create a new instance of jasypt
	encryptor := jasypt.New(ALGORITHM, jasypt.NewConfig(
		jasypt.SetPassword(key),
		jasypt.SetSaltGenerator(salt.RandomSaltGenerator{}),
		jasypt.SetIvGenerator(iv.RandomIvGenerator{}),
	))
	// encrypt the message
	return encryptor.Encrypt(text)
}

// Decrypt 解密字符串
// encode: 密文
// key: 密钥
func Decrypt(encode string, key string) (string, error) {
	// create a new instance of jasypt
	encryptor := jasypt.New(ALGORITHM, jasypt.NewConfig(
		jasypt.SetPassword(key),
		jasypt.SetSaltGenerator(salt.RandomSaltGenerator{}),
		jasypt.SetIvGenerator(iv.RandomIvGenerator{}),
	))
	// decrypt the message
	return encryptor.Decrypt(encode)
}
